# GamePlayer-Raspberry 核心依赖
# 游戏开发框架
pygame>=2.5.0

# Web框架
flask>=3.0.0

# HTTP请求
requests>=2.32.4

# 数据处理
numpy>=1.26.0

# 进度条
tqdm>=4.66.0

# 图像处理
pillow>=10.2.0

# 配置文件
pyyaml>=6.0.1

# 系统信息
psutil>=7.0.0

# 环境变量
python-dotenv>=1.1.1

# 测试框架
pytest>=8.0.0
pytest-cov>=4.1.0
pytest-asyncio>=0.23.0
pytest-mock>=3.12.0

# AWS SDK (可选)
boto3>=1.39.0
botocore>=1.39.0

# SSH连接 (可选)
paramiko>=3.4.0

# 绘图 (可选)
matplotlib>=3.8.0

# URL解析 - 兼容版本
urllib3>=1.26.0,<3.0.0

# 安全相关依赖
cryptography>=42.0.0
certifi>=2024.12.0
charset-normalizer>=3.3.0
idna>=3.7
