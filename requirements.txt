# GamePlayer-Raspberry 核心依赖
# 游戏开发框架
pygame>=2.0.0

# Web框架
flask>=2.0.0

# HTTP请求
requests>=2.25.0

# 数据处理
numpy>=1.20.0

# 进度条
tqdm>=4.60.0

# 图像处理
pillow>=8.0.0

# 配置文件
pyyaml>=5.4.0

# 系统信息
psutil>=5.8.0

# 环境变量
python-dotenv>=0.19.0

# 测试框架
pytest>=6.0.0
pytest-cov>=2.12.0
pytest-asyncio>=0.15.0
pytest-mock>=3.6.0

# AWS SDK (可选)
boto3>=1.17.0
botocore>=1.20.0

# SSH连接 (可选)
paramiko>=2.7.0

# 绘图 (可选)
matplotlib>=3.3.0

# URL解析
urllib3>=1.26.0
