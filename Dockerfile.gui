# GamePlayer-Raspberry Docker图形化运行环境
# 支持X11转发和VNC远程桌面

FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV DISPLAY=:1
ENV HOME=/home/<USER>
ENV USER=gamer
ENV PYTHONPATH=/opt/gameplayer
ENV LANG=zh_CN.UTF-8
ENV LC_ALL=zh_CN.UTF-8

# 安装系统依赖和图形环境
RUN apt-get update && apt-get install -y \
    # 基础系统
    sudo curl wget git vim nano htop \
    # Python环境
    python3 python3-pip python3-dev python3-venv \
    # 图形界面基础
    xorg xvfb x11vnc fluxbox \
    # 图形库
    libgl1-mesa-glx libglu1-mesa \
    libxrandr2 libxss1 libxcursor1 libxcomposite1 \
    libxdamage1 libxfixes3 libxi6 libxtst6 \
    # 音频支持
    pulseaudio alsa-utils \
    # 游戏相关库
    libsdl2-dev libsdl2-image-dev libsdl2-mixer-dev libsdl2-ttf-dev \
    # 模拟器
    mednafen fceux snes9x-gtk visualboyadvance-m \
    # 网络工具
    net-tools iputils-ping \
    # 文件工具
    unzip p7zip-full \
    # 字体支持
    fonts-dejavu fonts-liberation fonts-noto-cjk \
    # 中文支持
    language-pack-zh-hans \
    # 清理缓存
    && rm -rf /var/lib/apt/lists/*

# 创建用户
RUN useradd -m -s /bin/bash gamer && \
    echo "gamer:gamer123" | chpasswd && \
    usermod -aG sudo,audio,video gamer && \
    echo "gamer ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# 切换到用户
USER gamer
WORKDIR /home/<USER>

# 创建项目目录
RUN mkdir -p /home/<USER>/GamePlayer-Raspberry

# 复制项目文件
COPY --chown=gamer:gamer . /home/<USER>/GamePlayer-Raspberry/

# 设置工作目录
WORKDIR /home/<USER>/GamePlayer-Raspberry

# 安装Python依赖
RUN pip3 install --user -r requirements.txt

# 创建必要目录
RUN mkdir -p data/roms/{nes,snes,gameboy,gba,genesis} && \
    mkdir -p data/saves/{nes,snes,gameboy,gba,genesis} && \
    mkdir -p data/web/images/covers && \
    mkdir -p logs && \
    mkdir -p ~/.vnc

# 设置VNC密码
RUN echo "gamer123" | vncpasswd -f > ~/.vnc/passwd && \
    chmod 600 ~/.vnc/passwd

# 创建VNC启动脚本
RUN echo '#!/bin/bash\n\
export DISPLAY=:1\n\
Xvfb :1 -screen 0 1920x1080x24 &\n\
sleep 2\n\
fluxbox &\n\
x11vnc -display :1 -nopw -listen localhost -xkb -ncache 10 -ncache_cr -forever &\n\
sleep 2\n\
' > ~/.vnc/start_vnc.sh && chmod +x ~/.vnc/start_vnc.sh

# 创建游戏启动脚本
RUN echo '#!/bin/bash\n\
echo "🎮 GamePlayer-Raspberry Docker图形化环境启动中..."\n\
echo "================================================"\n\
\n\
# 设置显示环境\n\
export DISPLAY=:1\n\
export PULSE_RUNTIME_PATH=/tmp/pulse\n\
\n\
# 启动VNC服务器\n\
echo "🖥️ 启动VNC服务器..."\n\
Xvfb :1 -screen 0 1920x1080x24 &\n\
sleep 3\n\
\n\
# 启动窗口管理器\n\
echo "🪟 启动窗口管理器..."\n\
fluxbox &\n\
sleep 2\n\
\n\
# 启动VNC服务\n\
echo "🔗 启动VNC服务..."\n\
x11vnc -display :1 -nopw -listen 0.0.0.0 -xkb -ncache 10 -ncache_cr -forever -shared &\n\
sleep 2\n\
\n\
# 启动音频服务\n\
echo "🔊 启动音频服务..."\n\
pulseaudio --start --log-target=syslog &\n\
sleep 2\n\
\n\
# 运行高级修复工具\n\
echo "🔧 运行系统修复..."\n\
cd /home/<USER>/GamePlayer-Raspberry\n\
python3 src/scripts/advanced_emulator_fixer.py --no-progress\n\
\n\
# 启动Web服务器\n\
echo "🌐 启动Web服务器..."\n\
python3 src/scripts/simple_demo_server.py &\n\
WEB_PID=$!\n\
sleep 3\n\
\n\
echo "✅ 启动完成！"\n\
echo "================================================"\n\
echo "🌐 Web界面: http://localhost:3020"\n\
echo "🖥️ VNC连接: localhost:5900 (密码: gamer123)"\n\
echo "🎮 支持的模拟器: mednafen, fceux, snes9x, visualboyadvance-m"\n\
echo "📁 ROM目录: /home/<USER>/GamePlayer-Raspberry/data/roms/"\n\
echo "💾 存档目录: /home/<USER>/GamePlayer-Raspberry/data/saves/"\n\
echo "================================================"\n\
\n\
# 保持容器运行\n\
wait $WEB_PID\n\
' > /home/<USER>/start_gameplayer_gui.sh && \
    chmod +x /home/<USER>/start_gameplayer_gui.sh

# 创建测试脚本
RUN echo '#!/bin/bash\n\
echo "🧪 测试图形化环境..."\n\
export DISPLAY=:1\n\
\n\
# 测试X11\n\
echo "测试X11显示..."\n\
xdpyinfo >/dev/null 2>&1 && echo "✅ X11正常" || echo "❌ X11异常"\n\
\n\
# 测试模拟器\n\
echo "测试模拟器..."\n\
which mednafen >/dev/null && echo "✅ mednafen已安装" || echo "❌ mednafen未安装"\n\
which fceux >/dev/null && echo "✅ fceux已安装" || echo "❌ fceux未安装"\n\
which snes9x-gtk >/dev/null && echo "✅ snes9x已安装" || echo "❌ snes9x未安装"\n\
\n\
# 测试Python环境\n\
echo "测试Python环境..."\n\
python3 -c "import pygame; print(\"✅ pygame可用\")" 2>/dev/null || echo "❌ pygame不可用"\n\
python3 -c "import flask; print(\"✅ flask可用\")" 2>/dev/null || echo "❌ flask不可用"\n\
\n\
echo "🎮 环境测试完成"\n\
' > /home/<USER>/test_environment.sh && \
    chmod +x /home/<USER>/test_environment.sh

# 设置权限
RUN chmod +x src/scripts/*.py

# 暴露端口
EXPOSE 3020 5900 6080

# 启动命令
CMD ["/home/<USER>/start_gameplayer_gui.sh"]
