<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 GamePlayer-Raspberry 游戏选择器</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: 'Courier New', monospace;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #00ff00;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 30px;
        }
        .game-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .game-card {
            background: rgba(0,0,0,0.3);
            border: 2px solid #00ff00;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .game-card:hover {
            background: rgba(0,255,0,0.1);
            box-shadow: 0 0 20px rgba(0,255,0,0.3);
            transform: translateY(-5px);
        }
        .game-title {
            font-size: 18px;
            font-weight: bold;
            color: #ffff00;
            margin-bottom: 10px;
        }
        .game-info {
            font-size: 14px;
            color: #cccccc;
            margin-bottom: 15px;
        }
        .game-actions {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #00ff00;
            background: rgba(0,255,0,0.1);
            color: #00ff00;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: rgba(0,255,0,0.2);
        }
        .btn-primary {
            background: rgba(0,255,0,0.2);
            color: white;
        }
        .save-info {
            background: rgba(255,255,0,0.1);
            border: 1px solid #ffff00;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
        }
        .controls {
            background: rgba(0,0,0,0.5);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        .controls h3 {
            color: #00ff00;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 GamePlayer-Raspberry 游戏选择器</h1>
        
        <div id="gameGrid" class="game-grid">
            <!-- 游戏卡片将通过JavaScript动态生成 -->
        </div>
        
        <div class="controls">
            <h3>🕹️ 控制说明</h3>
            <p>• 点击"开始游戏"直接启动游戏</p>
            <p>• 点击"继续游戏"加载最近的存档</p>
            <p>• 点击"管理存档"查看所有存档</p>
            <p>• 游戏中按ESC键返回选择界面</p>
        </div>
    </div>

    <script>
        // 游戏数据（实际应该从API获取）
        const games = [
            {
                id: "micro_mages",
                title: "Micro Mages",
                description: "现代NES平台游戏杰作",
                category: "平台游戏",
                hasProgress: true,
                lastPlayed: "2025-06-26 20:30"
            },
            {
                id: "nova_squirrel",
                title: "Nova the Squirrel",
                description: "现代平台冒险游戏",
                category: "冒险游戏",
                hasProgress: false,
                lastPlayed: null
            },
            {
                id: "lizard",
                title: "Lizard",
                description: "复古风格解谜平台游戏",
                category: "解谜游戏",
                hasProgress: true,
                lastPlayed: "2025-06-25 15:45"
            }
        ];

        // 渲染游戏网格
        function renderGameGrid() {
            const gameGrid = document.getElementById('gameGrid');
            gameGrid.innerHTML = '';

            games.forEach(game => {
                const gameCard = document.createElement('div');
                gameCard.className = 'game-card';
                
                gameCard.innerHTML = `
                    <div class="game-title">${game.title}</div>
                    <div class="game-info">
                        <div>类型: ${game.category}</div>
                        <div>${game.description}</div>
                    </div>
                    ${game.hasProgress ? `
                        <div class="save-info">
                            💾 有存档 | 最后游玩: ${game.lastPlayed}
                        </div>
                    ` : ''}
                    <div class="game-actions">
                        <a href="#" class="btn btn-primary" onclick="startGame('${game.id}')">
                            ${game.hasProgress ? '继续游戏' : '开始游戏'}
                        </a>
                        <a href="#" class="btn" onclick="newGame('${game.id}')">新游戏</a>
                        <a href="#" class="btn" onclick="manageSaves('${game.id}')">管理存档</a>
                    </div>
                `;
                
                gameGrid.appendChild(gameCard);
            });
        }

        // 游戏控制函数
        function startGame(gameId) {
            console.log(`启动游戏: ${gameId}`);
            // 实际实现中应该调用后端API启动游戏
            alert(`正在启动 ${gameId}...`);
        }

        function newGame(gameId) {
            console.log(`新游戏: ${gameId}`);
            alert(`正在开始新游戏 ${gameId}...`);
        }

        function manageSaves(gameId) {
            console.log(`管理存档: ${gameId}`);
            alert(`打开 ${gameId} 存档管理...`);
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            renderGameGrid();
            console.log('🎮 GamePlayer-Raspberry 游戏选择器已加载');
        });
    </script>
</body>
</html>
