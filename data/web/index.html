<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 GamePlayer-Ra<PERSON>berry</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: 'Courier New', monospace;
            color: white;
            text-align: center;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        h1 {
            color: #00ff00;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 30px;
        }
        canvas {
            border: 3px solid #00ff00;
            border-radius: 10px;
            background: #000;
            box-shadow: 0 0 20px rgba(0,255,0,0.3);
        }
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            border: 1px solid #00ff00;
        }
        .score {
            font-size: 24px;
            color: #ffff00;
            margin: 10px 0;
        }
        .instructions {
            margin: 20px 0;
            font-size: 16px;
            line-height: 1.6;
        }
        .key {
            background: #333;
            color: #00ff00;
            padding: 5px 10px;
            border-radius: 5px;
            margin: 0 5px;
            border: 1px solid #00ff00;
        }

        /* 游戏列表样式 */
        .game-list {
            text-align: center;
            padding: 20px;
        }

        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .game-item {
            background: rgba(0,255,0,0.1);
            border: 2px solid #00ff00;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .game-item:hover {
            background: rgba(0,255,0,0.2);
            border-color: #00ff88;
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,255,0,0.3);
        }

        .game-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .game-title {
            font-size: 18px;
            font-weight: bold;
            color: #00ff00;
            margin-bottom: 5px;
        }

        .game-desc {
            font-size: 12px;
            color: #88ff88;
            line-height: 1.4;
        }

        /* 游戏界面样式 */
        .game-interface {
            text-align: center;
        }

        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 0 20px;
        }

        .back-btn {
            background: rgba(255,0,0,0.2);
            border: 1px solid #ff0000;
            color: #ff0000;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,0,0,0.3);
            border-color: #ff4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 GamePlayer-Raspberry</h1>

        <!-- 游戏列表界面 -->
        <div id="gameList" class="game-list">
            <h2>🎮 选择游戏</h2>
            <div class="games-grid">
                <div class="game-item" onclick="selectGame('space-shooter')">
                    <div class="game-icon">🚀</div>
                    <div class="game-title">太空射击</div>
                    <div class="game-desc">经典的太空射击游戏</div>
                </div>
                <div class="game-item" onclick="selectGame('tetris')">
                    <div class="game-icon">🧩</div>
                    <div class="game-title">俄罗斯方块</div>
                    <div class="game-desc">经典的益智游戏</div>
                </div>
                <div class="game-item" onclick="selectGame('snake')">
                    <div class="game-icon">🐍</div>
                    <div class="game-title">贪吃蛇</div>
                    <div class="game-desc">经典的贪吃蛇游戏</div>
                </div>
                <div class="game-item" onclick="selectGame('mario')">
                    <div class="game-icon">🍄</div>
                    <div class="game-title">超级马里奥</div>
                    <div class="game-desc">经典的平台跳跃游戏</div>
                </div>
                <div class="game-item" onclick="selectGame('pacman')">
                    <div class="game-icon">👻</div>
                    <div class="game-title">吃豆人</div>
                    <div class="game-desc">经典的迷宫游戏</div>
                </div>
                <div class="game-item" onclick="selectGame('contra')">
                    <div class="game-icon">🔫</div>
                    <div class="game-title">魂斗罗</div>
                    <div class="game-desc">经典的动作射击游戏</div>
                </div>
            </div>
        </div>

        <!-- 游戏界面 -->
        <div id="gameInterface" class="game-interface" style="display: none;">
            <div class="game-header">
                <button onclick="backToGameList()" class="back-btn">← 返回游戏列表</button>
                <h3 id="currentGameTitle">太空射击</h3>
                <div class="score">得分: <span id="score">0</span></div>
            </div>
            <canvas id="gameCanvas" width="800" height="600"></canvas>
        </div>
        
        <div class="controls">
            <div class="instructions">
                <h3>🕹️ 游戏控制</h3>
                <p>
                    <span class="key">W</span> <span class="key">A</span> <span class="key">S</span> <span class="key">D</span> 或 方向键 - 移动
                </p>
                <p>
                    <span class="key">空格</span> - 发射子弹 | <span class="key">R</span> - 重新开始
                </p>
                <p>
                    <span class="key">点击屏幕</span> - 启用音频
                </p>
            </div>

            <div class="audio-controls" style="margin-top: 20px;">
                <h3>🔊 音频控制</h3>
                <div style="margin: 10px 0;">
                    <label style="color: #00ff00;">主音量: </label>
                    <input type="range" id="masterVolume" min="0" max="100" value="80"
                           style="width: 150px;" onchange="updateMasterVolume(this.value)">
                    <span id="masterVolumeValue">80%</span>
                </div>
                <div style="margin: 10px 0;">
                    <label style="color: #00ff00;">音效音量: </label>
                    <input type="range" id="sfxVolume" min="0" max="100" value="70"
                           style="width: 150px;" onchange="updateSfxVolume(this.value)">
                    <span id="sfxVolumeValue">70%</span>
                </div>
                <div style="margin: 10px 0;">
                    <label style="color: #00ff00;">音乐音量: </label>
                    <input type="range" id="musicVolume" min="0" max="100" value="30"
                           style="width: 150px;" onchange="updateMusicVolume(this.value)">
                    <span id="musicVolumeValue">30%</span>
                </div>
                <div style="margin: 10px 0;">
                    <button onclick="toggleAudio()" style="
                        background: rgba(0,255,0,0.2);
                        border: 1px solid #00ff00;
                        color: #00ff00;
                        padding: 5px 15px;
                        border-radius: 5px;
                        cursor: pointer;
                    ">🔊 切换音频</button>
                </div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🎯 游戏目标</h3>
            <p>控制绿色飞船，射击红色敌人，避免碰撞！</p>
            <p>这是一个经典的NES风格射击游戏</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const scoreElement = document.getElementById('score');

        // 音频系统
        class AudioSystem {
            constructor() {
                this.audioContext = null;
                this.sounds = {};
                this.musicGain = null;
                this.sfxGain = null;
                this.masterGain = null;
                this.initialized = false;
                this.musicVolume = 0.3;
                this.sfxVolume = 0.7;
                this.masterVolume = 0.8;
            }

            async initialize() {
                try {
                    // 创建音频上下文
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

                    // 创建音量控制节点
                    this.masterGain = this.audioContext.createGain();
                    this.musicGain = this.audioContext.createGain();
                    this.sfxGain = this.audioContext.createGain();

                    // 连接音频节点
                    this.masterGain.connect(this.audioContext.destination);
                    this.musicGain.connect(this.masterGain);
                    this.sfxGain.connect(this.masterGain);

                    // 设置初始音量
                    this.masterGain.gain.value = this.masterVolume;
                    this.musicGain.gain.value = this.musicVolume;
                    this.sfxGain.gain.value = this.sfxVolume;

                    // 生成音效
                    await this.generateSounds();

                    this.initialized = true;
                    console.log('🔊 音频系统初始化成功');

                } catch (error) {
                    console.error('❌ 音频系统初始化失败:', error);
                }
            }

            async generateSounds() {
                // 生成各种音效
                this.sounds.shoot = await this.createTone(800, 0.1, 'square');
                this.sounds.hit = await this.createTone(300, 0.2, 'sawtooth');
                this.sounds.powerup = await this.createChord([440, 554, 659], 0.3);
                this.sounds.explosion = await this.createNoise(0.3);
                this.sounds.gameOver = await this.createTone(200, 1.0, 'triangle');
                this.sounds.background = await this.createBackgroundMusic();
            }

            async createTone(frequency, duration, waveType = 'sine') {
                const sampleRate = this.audioContext.sampleRate;
                const frameCount = sampleRate * duration;
                const buffer = this.audioContext.createBuffer(1, frameCount, sampleRate);
                const data = buffer.getChannelData(0);

                for (let i = 0; i < frameCount; i++) {
                    const t = i / sampleRate;
                    let value;

                    switch (waveType) {
                        case 'square':
                            value = Math.sin(2 * Math.PI * frequency * t) > 0 ? 0.3 : -0.3;
                            break;
                        case 'sawtooth':
                            value = 0.3 * (2 * (frequency * t % 1) - 1);
                            break;
                        case 'triangle':
                            value = 0.3 * (2 * Math.abs(2 * (frequency * t % 1) - 1) - 1);
                            break;
                        default: // sine
                            value = 0.3 * Math.sin(2 * Math.PI * frequency * t);
                    }

                    // 添加淡出效果
                    const fadeOut = Math.max(0, 1 - (t / duration) * 2);
                    data[i] = value * fadeOut;
                }

                return buffer;
            }

            async createChord(frequencies, duration) {
                const sampleRate = this.audioContext.sampleRate;
                const frameCount = sampleRate * duration;
                const buffer = this.audioContext.createBuffer(1, frameCount, sampleRate);
                const data = buffer.getChannelData(0);

                for (let i = 0; i < frameCount; i++) {
                    const t = i / sampleRate;
                    let value = 0;

                    frequencies.forEach(freq => {
                        value += 0.1 * Math.sin(2 * Math.PI * freq * t);
                    });

                    // 添加淡出效果
                    const fadeOut = Math.max(0, 1 - (t / duration) * 1.5);
                    data[i] = value * fadeOut;
                }

                return buffer;
            }

            async createNoise(duration) {
                const sampleRate = this.audioContext.sampleRate;
                const frameCount = sampleRate * duration;
                const buffer = this.audioContext.createBuffer(1, frameCount, sampleRate);
                const data = buffer.getChannelData(0);

                for (let i = 0; i < frameCount; i++) {
                    const t = i / sampleRate;
                    const value = (Math.random() * 2 - 1) * 0.2;

                    // 添加淡出效果
                    const fadeOut = Math.max(0, 1 - (t / duration) * 3);
                    data[i] = value * fadeOut;
                }

                return buffer;
            }

            async createBackgroundMusic() {
                // 创建简单的背景音乐循环
                const duration = 4.0; // 4秒循环
                const sampleRate = this.audioContext.sampleRate;
                const frameCount = sampleRate * duration;
                const buffer = this.audioContext.createBuffer(1, frameCount, sampleRate);
                const data = buffer.getChannelData(0);

                // 简单的旋律
                const melody = [440, 494, 523, 587, 659, 587, 523, 494]; // A, B, C, D, E, D, C, B
                const noteDuration = duration / melody.length;

                for (let i = 0; i < frameCount; i++) {
                    const t = i / sampleRate;
                    const noteIndex = Math.floor(t / noteDuration);
                    const noteTime = (t % noteDuration) / noteDuration;
                    const frequency = melody[noteIndex % melody.length];

                    // 主旋律
                    let value = 0.1 * Math.sin(2 * Math.PI * frequency * t);

                    // 添加和声
                    value += 0.05 * Math.sin(2 * Math.PI * (frequency * 0.75) * t);

                    // 音符淡入淡出
                    const envelope = Math.sin(Math.PI * noteTime);
                    data[i] = value * envelope;
                }

                return buffer;
            }

            playSound(soundName, volume = 1.0) {
                if (!this.initialized || !this.sounds[soundName]) {
                    return;
                }

                try {
                    const source = this.audioContext.createBufferSource();
                    const gainNode = this.audioContext.createGain();

                    source.buffer = this.sounds[soundName];
                    gainNode.gain.value = volume;

                    source.connect(gainNode);
                    gainNode.connect(this.sfxGain);

                    source.start();
                } catch (error) {
                    console.error('播放音效失败:', error);
                }
            }

            playBackgroundMusic() {
                if (!this.initialized || !this.sounds.background) {
                    return;
                }

                try {
                    // 停止当前背景音乐
                    this.stopBackgroundMusic();

                    // 创建循环播放的背景音乐
                    this.backgroundSource = this.audioContext.createBufferSource();
                    this.backgroundSource.buffer = this.sounds.background;
                    this.backgroundSource.loop = true;
                    this.backgroundSource.connect(this.musicGain);
                    this.backgroundSource.start();

                    console.log('🎵 背景音乐开始播放');
                } catch (error) {
                    console.error('播放背景音乐失败:', error);
                }
            }

            stopBackgroundMusic() {
                if (this.backgroundSource) {
                    try {
                        this.backgroundSource.stop();
                        this.backgroundSource = null;
                    } catch (error) {
                        // 忽略已停止的音源错误
                    }
                }
            }

            setMasterVolume(volume) {
                this.masterVolume = Math.max(0, Math.min(1, volume));
                if (this.masterGain) {
                    this.masterGain.gain.value = this.masterVolume;
                }
            }

            setSfxVolume(volume) {
                this.sfxVolume = Math.max(0, Math.min(1, volume));
                if (this.sfxGain) {
                    this.sfxGain.gain.value = this.sfxVolume;
                }
            }

            setMusicVolume(volume) {
                this.musicVolume = Math.max(0, Math.min(1, volume));
                if (this.musicGain) {
                    this.musicGain.gain.value = this.musicVolume;
                }
            }
        }

        // 创建音频系统实例
        const audioSystem = new AudioSystem();

        // 游戏状态
        let score = 0;
        let gameRunning = true;
        let audioInitialized = false;
        let currentGame = null;

        // 游戏数据
        const games = {
            'space-shooter': {
                title: '太空射击',
                icon: '🚀',
                description: '经典的太空射击游戏',
                gameFunction: startSpaceShooter
            },
            'tetris': {
                title: '俄罗斯方块',
                icon: '🧩',
                description: '经典的益智游戏',
                gameFunction: startTetris
            },
            'snake': {
                title: '贪吃蛇',
                icon: '🐍',
                description: '经典的贪吃蛇游戏',
                gameFunction: startSnake
            },
            'mario': {
                title: '超级马里奥',
                icon: '🍄',
                description: '经典的平台跳跃游戏',
                gameFunction: startMario
            },
            'pacman': {
                title: '吃豆人',
                icon: '👻',
                description: '经典的迷宫游戏',
                gameFunction: startPacman
            },
            'contra': {
                title: '魂斗罗',
                icon: '🔫',
                description: '经典的动作射击游戏',
                gameFunction: startContra
            }
        };
        
        // 玩家
        const player = {
            x: canvas.width / 2,
            y: canvas.height - 50,
            width: 30,
            height: 30,
            speed: 5,
            color: '#00ff00'
        };

        // 子弹数组
        const bullets = [];
        const bulletSpeed = 7;

        // 敌人数组
        const enemies = [];
        const enemySpeed = 2;

        // 按键状态
        const keys = {};

        // 初始化音频系统
        async function initializeAudio() {
            if (!audioInitialized) {
                await audioSystem.initialize();
                audioInitialized = true;

                // 开始播放背景音乐
                audioSystem.playBackgroundMusic();

                // 显示音频状态
                console.log('🔊 音频系统已启动');
                showAudioStatus();
            }
        }

        function showAudioStatus() {
            const statusDiv = document.createElement('div');
            statusDiv.style.cssText = `
                position: fixed;
                top: 10px;
                left: 10px;
                background: rgba(0, 255, 0, 0.8);
                color: black;
                padding: 5px 10px;
                border-radius: 5px;
                font-size: 12px;
                z-index: 1000;
            `;
            statusDiv.textContent = '🔊 音频已启用';
            document.body.appendChild(statusDiv);

            setTimeout(() => {
                document.body.removeChild(statusDiv);
            }, 2000);
        }

        // 事件监听
        document.addEventListener('keydown', (e) => {
            keys[e.key.toLowerCase()] = true;
            if (e.key === ' ') {
                e.preventDefault();
                shootBullet();
            }
            if (e.key.toLowerCase() === 'r') {
                resetGame();
            }

            // 初始化音频系统（需要用户交互）
            if (!audioInitialized) {
                initializeAudio();
            }
        });

        // 点击事件也可以初始化音频
        document.addEventListener('click', () => {
            if (!audioInitialized) {
                initializeAudio();
            }
        });

        document.addEventListener('keyup', (e) => {
            keys[e.key.toLowerCase()] = false;
        });

        // 射击子弹
        function shootBullet() {
            bullets.push({
                x: player.x + player.width / 2,
                y: player.y,
                width: 4,
                height: 10,
                color: '#ffff00'
            });

            // 播放射击音效
            audioSystem.playSound('shoot', 0.5);
        }

        // 创建敌人
        function createEnemy() {
            enemies.push({
                x: Math.random() * (canvas.width - 30),
                y: -30,
                width: 25,
                height: 25,
                color: '#ff0000'
            });
        }

        // 更新游戏
        function update() {
            if (!gameRunning) return;

            // 移动玩家
            if (keys['w'] || keys['arrowup']) player.y = Math.max(0, player.y - player.speed);
            if (keys['s'] || keys['arrowdown']) player.y = Math.min(canvas.height - player.height, player.y + player.speed);
            if (keys['a'] || keys['arrowleft']) player.x = Math.max(0, player.x - player.speed);
            if (keys['d'] || keys['arrowright']) player.x = Math.min(canvas.width - player.width, player.x + player.speed);

            // 更新子弹
            for (let i = bullets.length - 1; i >= 0; i--) {
                bullets[i].y -= bulletSpeed;
                if (bullets[i].y < 0) {
                    bullets.splice(i, 1);
                }
            }

            // 更新敌人
            for (let i = enemies.length - 1; i >= 0; i--) {
                enemies[i].y += enemySpeed;
                if (enemies[i].y > canvas.height) {
                    enemies.splice(i, 1);
                }
            }

            // 检查子弹与敌人碰撞
            for (let i = bullets.length - 1; i >= 0; i--) {
                for (let j = enemies.length - 1; j >= 0; j--) {
                    if (bullets[i] && enemies[j] && 
                        bullets[i].x < enemies[j].x + enemies[j].width &&
                        bullets[i].x + bullets[i].width > enemies[j].x &&
                        bullets[i].y < enemies[j].y + enemies[j].height &&
                        bullets[i].y + bullets[i].height > enemies[j].y) {
                        
                        bullets.splice(i, 1);
                        enemies.splice(j, 1);
                        score += 10;
                        scoreElement.textContent = score;

                        // 播放击中音效
                        audioSystem.playSound('hit', 0.7);
                        break;
                    }
                }
            }

            // 检查玩家与敌人碰撞
            for (let enemy of enemies) {
                if (player.x < enemy.x + enemy.width &&
                    player.x + player.width > enemy.x &&
                    player.y < enemy.y + enemy.height &&
                    player.y + player.height > enemy.y) {
                    
                    gameRunning = false;

                    // 播放游戏结束音效
                    audioSystem.playSound('gameOver', 1.0);
                    audioSystem.stopBackgroundMusic();

                    alert(`游戏结束！最终得分: ${score}\n按R键重新开始`);
                }
            }

            // 随机生成敌人
            if (Math.random() < 0.02) {
                createEnemy();
            }
        }

        // 绘制游戏
        function draw() {
            // 清空画布
            ctx.fillStyle = '#000011';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制星空背景
            ctx.fillStyle = '#ffffff';
            for (let i = 0; i < 50; i++) {
                const x = (Date.now() * 0.01 + i * 123) % canvas.width;
                const y = (Date.now() * 0.02 + i * 456) % canvas.height;
                ctx.fillRect(x, y, 1, 1);
            }

            // 绘制玩家
            ctx.fillStyle = player.color;
            ctx.fillRect(player.x, player.y, player.width, player.height);
            
            // 绘制玩家细节
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(player.x + 5, player.y + 5, 20, 5);
            ctx.fillRect(player.x + 12, player.y, 6, 15);

            // 绘制子弹
            ctx.fillStyle = '#ffff00';
            for (let bullet of bullets) {
                ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height);
            }

            // 绘制敌人
            for (let enemy of enemies) {
                ctx.fillStyle = enemy.color;
                ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);
                
                // 敌人细节
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(enemy.x + 5, enemy.y + 5, 15, 3);
                ctx.fillRect(enemy.x + 10, enemy.y + 15, 5, 8);
            }

            // 绘制游戏标题
            if (!gameRunning) {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.fillStyle = '#ff0000';
                ctx.font = '48px Courier New';
                ctx.textAlign = 'center';
                ctx.fillText('游戏结束', canvas.width / 2, canvas.height / 2);
                
                ctx.fillStyle = '#ffffff';
                ctx.font = '24px Courier New';
                ctx.fillText('按 R 键重新开始', canvas.width / 2, canvas.height / 2 + 50);
            }
        }

        // 重置游戏
        function resetGame() {
            score = 0;
            scoreElement.textContent = score;
            gameRunning = true;
            bullets.length = 0;
            enemies.length = 0;
            player.x = canvas.width / 2;
            player.y = canvas.height - 50;

            // 播放开始音效并重新开始背景音乐
            audioSystem.playSound('powerup', 0.8);
            if (audioInitialized) {
                audioSystem.playBackgroundMusic();
            }
        }

        // 游戏循环
        function gameLoop() {
            update();
            draw();
            requestAnimationFrame(gameLoop);
        }

        // 音频控制函数
        function updateMasterVolume(value) {
            const volume = value / 100;
            audioSystem.setMasterVolume(volume);
            document.getElementById('masterVolumeValue').textContent = value + '%';
        }

        function updateSfxVolume(value) {
            const volume = value / 100;
            audioSystem.setSfxVolume(volume);
            document.getElementById('sfxVolumeValue').textContent = value + '%';
        }

        function updateMusicVolume(value) {
            const volume = value / 100;
            audioSystem.setMusicVolume(volume);
            document.getElementById('musicVolumeValue').textContent = value + '%';
        }

        function toggleAudio() {
            if (audioInitialized) {
                // 切换音频开关
                if (audioSystem.masterVolume > 0) {
                    audioSystem.setMasterVolume(0);
                    document.getElementById('masterVolume').value = 0;
                    document.getElementById('masterVolumeValue').textContent = '0%';
                } else {
                    audioSystem.setMasterVolume(0.8);
                    document.getElementById('masterVolume').value = 80;
                    document.getElementById('masterVolumeValue').textContent = '80%';
                }
            } else {
                initializeAudio();
            }
        }

        // 游戏选择和切换函数
        function selectGame(gameId) {
            if (games[gameId]) {
                currentGame = gameId;

                // 隐藏游戏列表，显示游戏界面
                document.getElementById('gameList').style.display = 'none';
                document.getElementById('gameInterface').style.display = 'block';

                // 更新游戏标题
                document.getElementById('currentGameTitle').textContent = games[gameId].title;

                // 重置得分
                score = 0;
                document.getElementById('score').textContent = score;

                // 播放选择音效
                audioSystem.playSound('select', 0.8);

                // 启动选中的游戏
                games[gameId].gameFunction();

                console.log(`🎮 启动游戏: ${games[gameId].title}`);
            }
        }

        function backToGameList() {
            // 停止当前游戏
            gameRunning = false;

            // 显示游戏列表，隐藏游戏界面
            document.getElementById('gameList').style.display = 'block';
            document.getElementById('gameInterface').style.display = 'none';

            // 播放返回音效
            audioSystem.playSound('beep', 0.6);

            currentGame = null;
            console.log('🔙 返回游戏列表');
        }

        // 游戏实现函数
        function startSpaceShooter() {
            // 当前的太空射击游戏逻辑
            gameRunning = true;
            gameLoop();
        }

        function startTetris() {
            // 俄罗斯方块游戏逻辑（简化版）
            gameRunning = true;
            alert('🧩 俄罗斯方块游戏即将推出！');
            backToGameList();
        }

        function startSnake() {
            // 贪吃蛇游戏逻辑（简化版）
            gameRunning = true;
            alert('🐍 贪吃蛇游戏即将推出！');
            backToGameList();
        }

        function startMario() {
            // 超级马里奥游戏逻辑（简化版）
            gameRunning = true;
            alert('🍄 超级马里奥游戏即将推出！');
            backToGameList();
        }

        function startPacman() {
            // 吃豆人游戏逻辑（简化版）
            gameRunning = true;
            alert('👻 吃豆人游戏即将推出！');
            backToGameList();
        }

        function startContra() {
            // 魂斗罗游戏逻辑（简化版）
            gameRunning = true;
            alert('🔫 魂斗罗游戏即将推出！');
            backToGameList();
        }

        // 启动游戏系统
        console.log('🎮 GamePlayer-Raspberry 浏览器游戏启动!');
        console.log('📋 可用游戏:', Object.keys(games).length, '个');

        // 显示游戏列表（默认界面）
        document.getElementById('gameList').style.display = 'block';
        document.getElementById('gameInterface').style.display = 'none';
    </script>
</body>
</html>
