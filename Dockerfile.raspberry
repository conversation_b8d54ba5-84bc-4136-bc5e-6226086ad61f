FROM python:3.9-slim

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    git \
    wget \
    curl \
    build-essential \
    cmake \
    pkg-config \
    libsdl2-dev \
    libsdl2-image-dev \
    libsdl2-mixer-dev \
    libsdl2-ttf-dev \
    libfreetype6-dev \
    libasound2-dev \
    libpulse-dev \
    libudev-dev \
    libx11-dev \
    libxext-dev \
    libxrandr-dev \
    libxcursor-dev \
    libxi-dev \
    libxinerama-dev \
    libxxf86vm-dev \
    libgl1-mesa-dev \
    libglu1-mesa-dev \
    libgles2-mesa-dev \
    libegl1-mesa-dev \
    libdrm-dev \
    libgbm-dev \
    libxkbcommon-dev \
    libwayland-dev \
    libxss-dev \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /app

# 复制项目文件
COPY . /app/

# 升级 pip 和 setuptools
RUN pip3 install --upgrade pip setuptools wheel

# 安装 numpy 和 matplotlib 的预编译版本
RUN pip3 install numpy==1.21.6 matplotlib==3.5.3 --only-binary=all

# 安装其他 Python 依赖
RUN pip3 install requests paramiko tqdm boto3 flask pytest pytest-cov pytest-asyncio pytest-mock pillow pyyaml psutil python-dotenv pathlib typing

# 创建必要的目录结构
RUN mkdir -p /opt/retropie/emulators/nesticle \
    /opt/retropie/configs/nes \
    /home/<USER>/RetroPie/roms/nes \
    /home/<USER>/RetroPie/cheats \
    /home/<USER>/RetroPie/saves/nes \
    /usr/share/applications \
    /etc/systemd/system

# 设置权限
RUN chmod +x scripts/*.sh

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["bash", "scripts/docker_start.sh"] 