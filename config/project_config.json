{"emulator": {"type": "retroarch", "default": "nesticle", "cheats_dir": "/home/<USER>/Retro<PERSON>ie/cheats/", "saves_dir": "/home/<USER>/RetroPie/saves/", "nesticle": {"version": "95", "enabled": true, "auto_install": true, "is_default": true, "config_path": "/home/<USER>/Retro<PERSON>ie/configs/nes/", "core_path": "/opt/retropie/emulators/retroarch/cores/", "download_url": "https://github.com/RetroPie/RetroPie-Setup/raw/master/scriptmodules/emulators/nesticle.sh", "install_script": "nesticle_install.sh", "config_template": "nesticle_config.cfg", "rom_extensions": [".nes", ".NES"], "features": ["high_compatibility", "save_states", "cheat_support", "netplay", "rewind", "shaders", "infinite_lives", "auto_save"], "cheats": {"enabled": true, "infinite_lives": true, "auto_cheat": true, "cheat_codes": {"super_mario_bros": {"infinite_lives": "00FF-01-99", "invincible": "00FF-01-FF", "max_power": "00FF-01-03"}, "contra": {"infinite_lives": "00FF-01-99", "infinite_ammo": "00FF-01-FF", "spread_gun": "00FF-01-01"}, "mega_man": {"infinite_lives": "00FF-01-99", "infinite_energy": "00FF-01-FF", "all_weapons": "00FF-01-FF"}}}, "save_states": {"enabled": true, "auto_save": true, "save_interval": 30, "max_saves": 10, "save_path": "/home/<USER>/Retro<PERSON>ie/saves/nes/"}}, "virtuanes": {"version": "0.97", "enabled": true, "auto_install": true, "is_default": false, "config_path": "/home/<USER>/Retro<PERSON>ie/configs/nes/", "core_path": "/opt/retropie/emulators/retroarch/cores/", "download_url": "https://github.com/RetroPie/RetroPie-Setup/raw/master/scriptmodules/emulators/virtuanes.sh", "install_script": "virtuanes_install.sh", "config_template": "virtuanes_config.cfg", "rom_extensions": [".nes", ".NES"], "features": ["high_compatibility", "save_states", "cheat_support", "netplay", "rewind", "shaders"]}}, "cloud_save": {"provider": "cos", "cos": {"bucket": "your-bucket", "region": "ap-xxx", "secret_id": "your-secret-id", "secret_key": "your-secret-key", "base_url": "https://xxx.cos.ap-xxx.myqcloud.com"}, "custom_api": {"api_url": "https://your-api.example.com/upload", "api_key": "your-api-key"}}, "rom_sources": {"archive_org": {"base_url": "https://archive.org", "search_url": "https://archive.org/advancedsearch.php", "download_patterns": ["nes-100-in-1", "nes-games-collection", "nintendo-entertainment-system-roms"]}}, "nes_zip_urls": ["https://archive.org/download/nes-100-in-1/NES%20100-in-1.zip"], "raspberry_pi": {"host": "*************", "port": 22, "username": "pi", "password": "", "key_file": "", "roms_path": "/home/<USER>/Retro<PERSON>ie/roms/nes/"}, "download": {"timeout": 30, "chunk_size": 8192, "max_retries": 3, "retry_delay": 5}, "verification": {"verify_checksum": true, "checksum_algorithm": "sha256"}, "log_upload": {"bucket": "your-bucket", "prefix": "retropie-logs", "aws_access_key": "your-access-key", "aws_secret_key": "your-secret-key", "region": "ap-east-1"}}