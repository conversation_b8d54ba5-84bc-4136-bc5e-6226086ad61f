# 目录结构

- ../
  - web_config.py
  - .coverage
  - auto_refactor_structure.sh
  - .gitignore
  - core/
    - hdmi_config.py
    - rom_downloader.py
    - retropie_installer.py
    - __pycache__/
      - logger_config.cpython-39.pyc
  - config/
    - requirements.txt
    - project_config.json
    - install.sh
    - install.bat
    - firstboot_setup.service
    - rom_config.json
  - tests/
    - test_installer.py
    - test_rom_downloader.py
    - test_hdmi_config.py
    - logs/
      - gameplayer_20250624.log
  - docs/
    - DIR_TREE.md
    - LICENSE
    - README_HDMI.md
    - README.md
  - logs/
    - rom_downloader.log
    - hdmi_config.log
    - retropie_installer.log
    - gameplayer_20250624.log
    - logs/
      - rom_downloader.log
      - hdmi_config.log
      - retropie_installer.log
    - log_reports/
      - elk_export_20250624_115111.json
      - log_report_20250624_114909.md
      - trend.png
  - scripts/
    - auto_save_sync.py
    - auto_save_sync_hook.sh
    - retropie_ecosystem_auto.sh
    - auto_migrate_to_pi.sh
    - immersive_hardware_auto.sh
    - setup_auto_sync.sh
  - .git/
    - ORIG_HEAD
    - config
    - HEAD
    - description
    - index
    - COMMIT_EDITMSG
    - FETCH_HEAD
    - objects/
      - 59/
        - e9236b554b3584423a03d92f884aeaf83d5719
        - 32f80d9df8821152681b1332ffef8f97ca516c
        - 38f609e75a28e9af01328663ada0c2e00aa5c4
      - 66/
        - 725f40ebe50bcd851f29d4f6fbbb1d2a2fe5ae
      - 68/
        - 592abdc42e226d13939092de0eb83e659c701a
      - 6f/
        - 1930a7bd60c9a71e8dfc8668e1a09cb5558abd
      - 9b/
        - 59a58ff25211a0dabffd43ebaffdabff25fb49
      - 9e/
        - e8d1b863fd961f955f41851c0de5eac3bcd496
      - 32/
        - 5ac6080f578aa4342bfd287bae8eaab87850bb
      - 3c/
        - 0ddab292489fae3e3cd888dafd54b29cdb152d
      - 51/
        - 1fb0585b448d59ed6da6fd15679bd3d8d30dbc
      - 3d/
        - 4a9c5965c50985bdd6abbfe3afbe66a8425281
      - 58/
        - 236fae3f63b26776015ae714c596b10cb61874
        - d25ce6b92a8a32e7d54909f2b77ea317b55625
      - 94/
        - 960c08fc853dbe5147633c56a1bdb08be6b30a
        - 77af489fb44b5d5b6e2398cf571903813dc9bf
      - 60/
        - 88f28ef505d8db5360eb92d0334184fcd801ad
      - 05/
        - 19ecba6ea913e21689ec692e81e9e4973fbf73
      - 9d/
        - d45f7d769dcff56ec82700bed6d07337eff9c9
        - 349f478869c8cf4dbc9d6e3cdd83491c986f52
      - 9c/
        - 8cd38698460faae9f9f2dad098dafb9f864b2a
      - 02/
        - eac6989ad65d3fcea222171a83a1348ee0dbd9
      - b5/
        - 62ea1bd829dcd6683994c77c691c9ba51edbcc
        - 4ac35583a54b57402de561fa95bcb7d74ee9a9
      - d9/
        - bc58fa80741f842288cf0000e1b0f0a76eb87f
      - ac/
        - 9bf3cb363d1cd986d884690a3a5bf4dc836018
      - ad/
        - 66b1e1ee73cbc380d6fb6b3ebb430b6e1b2a20
      - be/
        - b3ffba77db202255e45b9da0e6c89d58fefb03
        - 7fd21b688587184fcf4ecca86feb2f5e2ce8cd
      - b3/
        - c02839d935a94c73780efdb6856504371d0540
      - d1/
        - 9dda1adc2fe701842fe3791b7682b2ef4badca
      - d8/
        - 4ef77b4d7d27e06f6ca7f97e39b267658befab
      - ab/
        - 5201f2de40eb4671d613dc759754621af34232
      - e2/
        - fa9e3f9158350622671b4dd59bc6eb184e167a
      - eb/
        - 2a7452728a65b8017a474115ff9f6e8e200430
      - c7/
        - d25db660a2c5a7e8c913d3894b007e5c4f12c4
      - fd/
        - 7ec36c79ca3d1bf2d6c05c2788ec0ec8e351c8
      - f2/
        - f73d595814294c56d7ad5758beade4df2e5213
      - e3/
        - 91f46a1116a1370224b6451d846505c143f7f4
      - ca/
        - 26c0e2d6ddc82a98b70e59e71ca339b3a6bbd6
      - ed/
        - 61132598138383c23eba0e883c3577caac8f57
      - ec/
        - 40a87f33f0eba4d17a81ca9fceca96828f426e
      - 20/
        - e37dfa42283ffe92117ee8348808ea94e8f242
      - 18/
        - 71e288fe67117958388e4351170d6a9c9fdfa8
        - 4809068b7a30939db3b158b40fdbeb703292d6
      - 27/
        - 508286f9299887811eea3805bb01cebf5bcb10
        - 8b1ea5280494d7d1ca435ba63579fab5194181
      - 4b/
        - 825dc642cb6eb9a060e54bf8d69288fbee4904
      - pack/
      - 29/
        - c6729b63318f0f13b5cc2c808b6034e78485ed
      - 45/
        - cefcfaae881fd5fc28155746ce5815b966596a
      - 1f/
        - 96449a951b88235c36d9a2bb493d9d3d623572
      - 73/
        - f854bc227868b6004c1c2f0e8db331ae440b26
        - 943fff0c90e066f2d8e386f27eedcf29c2ebea
      - 1a/
        - 4582b8bb13d877fd0409289628123f498b7c4a
      - 17/
        - be865cc347bd3404f34063d7647bf4693cb18d
      - 7b/
        - 99623709f948f3e1c82da0b32888a60a1c4539
      - 4c/
        - 0166a65dc8c0b2f02b0ae1c3fbb280641ebb8b
      - 4d/
        - 374c19b7120b1ba55c6aadc28c28b3cafeb906
      - 81/
        - 5e24e105838ae0f281cec0af37ab4c64ffefbf
        - 1d9fcd5c955317db89bd5a900af2fe8fd2d7f2
      - 72/
        - e219ed161d93a3b7cb9a4f1e36df5519795b3a
      - 2a/
        - 673ec4025f203c8936e3b13dcf4fbb561cc061
        - 472e15300eb20dfd27881c379c85c532830419
      - 43/
        - b0b94a08b50ed14e6d4098885220f8e95de437
      - 88/
        - a1a89ffb928319c018a34f5aa75525d762baab
      - 9f/
        - c5853097a459467438ce953e2069d9fcc0a2b3
      - 6b/
        - 61fd59d5aa1136f787010099853a97c17e8427
        - 7a0cb651786e0a63fea51c365240f36332468c
      - 07/
        - fc916303e30844d2b7ae383381f69d54fe7561
      - 00/
        - b16c484e7c0f8c30dfcfd6f99463551a6680b4
      - info/
      - 62/
        - 072ef4d23309d07bd2e395ed5bb3d0bc134152
      - 3f/
        - 4742d8024dea72bf8fa6287b533ba737d58a7b
      - 30/
        - 897ea35068feaf597aeaaf7fabab1288865890
      - 5b/
        - 64902cead379a7e01517c4378c98aeeb43395f
      - 55/
        - 4bbff91940c99309dc4064e06644fde750025e
        - 9cc19997859d474391eacf0faa3a317f84674b
        - 0a2bb58f8a4109a767c894202721ca10a13a3f
      - 97/
        - a6ee9584af9e4da1493af3ef3c8f9d0916aff7
      - 63/
        - 372b075d1de94e9e4ab6da2e43aceea400ff43
      - d3/
        - 2cd857407d4a87172a51c02240a099f9cd73fc
      - ba/
        - 09fe7f6c21e2001897118775b46f0983c11063
      - b6/
        - aeacd99fc2a937fe2cedabe43ca0aa600bb077
      - b7/
        - b9ca6293241db4a843bdf9599fa6a86fb7faa5
      - ef/
        - ab07115fe120af93d85853d33115f271af0d30
      - c3/
        - fbcc6c86a48a168d9d1e6f0bfa6ab58afdba2c
        - 01eed688c124efc12a231e9e9753b8b5f8af46
      - e8/
        - 330a2f0140671f4692aaa0924accf501c7f482
      - cb/
        - 3bc84178230f598610e609422a8320acd87ac9
      - f8/
        - e9555f14ba14be949fea8f65a4ccd8dc252980
        - e17b1553b9d5623eff7d3108adf1da41e84983
      - 2c/
        - d18cfb3402017b3d4d1371d5315804e6c73ce7
      - 84/
        - c2a7af0340ffdca5c2ec7d69e8508c202d56da
      - 4f/
        - 9fc5d3d29cb01c7521c55c526b18fc3dd4e99c
        - 0756d6a0558839046e77ed77823813ca288825
      - 8d/
        - 5e40918344590abd25cf1412f1f82c39790be2
      - 12/
        - 7658e52e259822dfdffb699c5ce8743537fe30
      - 1d/
        - b23935bfeb07ecd4a8c8d91c7a2a92d1ad3233
      - 47/
        - ed7c6e32bfaabaee5ee49d21b4ca78eb979894
      - 13/
        - 79e28951ad35a30638a87f369c972338a6c013
      - 8e/
        - d81f1266ac808ef06992b942e82ee710043024
    - info/
      - exclude
    - logs/
      - HEAD
      - refs/
        - heads/
          - master
        - remotes/
          - origin/
            - main
    - hooks/
      - commit-msg.sample
      - post-commit
      - pre-rebase.sample
      - pre-commit.sample
      - post-checkout
      - applypatch-msg.sample
      - fsmonitor-watchman.sample
      - pre-receive.sample
      - prepare-commit-msg.sample
      - post-update.sample
      - pre-applypatch.sample
      - pre-push
      - post-merge
      - pre-push.sample
      - update.sample
    - refs/
      - heads/
        - master
      - tags/
        - v2.0.0
      - remotes/
        - origin/
          - main
    - lfs/
      - cache/
        - locks/
          - refs/
            - heads/
              - main/
                - verifiable
      - tmp/
    - branches/
    - rebase-apply/
      - keep
      - next
      - head-name
      - last
      - 0003
      - 0004
      - patch-merge-index
      - 0005
      - utf8
      - 0002
      - orig-head
      - threeway
      - onto
      - apply-opt
      - original-commit
      - quiet
      - patch
      - final-commit
      - messageid
      - 0001
      - scissors
      - sign
      - rebasing
      - author-script
      - abort-safety
  - .vscode/
    - settings.json
  - downloads/
    - retropie-buster-4.8-rpi4_400.img
    - retropie-buster-4.8-rpi4_400.img.gz
    - roms/
    - downloads/
      - retropie-buster-4.8-rpi4_400.img
      - retropie-buster-4.8-rpi4_400.img.gz
      - roms/
