{"optimization_summary": {"total_files": 78, "total_issues_fixed": 0, "total_improvements": 89}, "file_results": [{"file_path": "setup.py", "original_lines": 47, "optimized_lines": 47, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "test_nes_api.py", "original_lines": 78, "optimized_lines": 78, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "test_nes_launch.py", "original_lines": 39, "optimized_lines": 39, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "tools/dev/auto_optimizer.py", "original_lines": 455, "optimized_lines": 455, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "tools/dev/fix_imports.py", "original_lines": 145, "optimized_lines": 145, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "tools/dev/restructure_project.py", "original_lines": 465, "optimized_lines": 465, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "tools/dev/code_analyzer.py", "original_lines": 386, "optimized_lines": 387, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["优化代码格式和空行", "分析类型提示需求"]}, {"file_path": "tools/dev/project_cleaner.py", "original_lines": 344, "optimized_lines": 344, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["类型提示分析失败: invalid syntax (<unknown>, line 238)"]}, {"file_path": "tools/dev/auto_install_top_nes_emulators.py", "original_lines": 183, "optimized_lines": 190, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["优化代码格式和空行", "分析类型提示需求"]}, {"file_path": "tools/dev/code_optimizer.py", "original_lines": 439, "optimized_lines": 439, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "tests/unit/test_rom_integration.py", "original_lines": 268, "optimized_lines": 268, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "tests/unit/test_installer.py", "original_lines": 133, "optimized_lines": 133, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "tests/unit/test_rom_downloader.py", "original_lines": 198, "optimized_lines": 198, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "tests/unit/test_virtuanes_installer.py", "original_lines": 277, "optimized_lines": 277, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "tests/unit/test_nesticle_installer.py", "original_lines": 430, "optimized_lines": 430, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["类型提示分析失败: invalid syntax (<unknown>, line 398)"]}, {"file_path": "src/core/nes_emulator.py", "original_lines": 687, "optimized_lines": 687, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/cheat_manager.py", "original_lines": 598, "optimized_lines": 598, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/virtuanes_installer.py", "original_lines": 419, "optimized_lines": 419, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/config_manager.py", "original_lines": 253, "optimized_lines": 253, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/hdmi_config.py", "original_lines": 373, "optimized_lines": 373, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/rom_manager.py", "original_lines": 429, "optimized_lines": 429, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/enhanced_cover_downloader.py", "original_lines": 358, "optimized_lines": 358, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/nesticle_installer.py", "original_lines": 658, "optimized_lines": 658, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/save_manager.py", "original_lines": 330, "optimized_lines": 330, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["类型提示分析失败: expected an indented block (<unknown>, line 163)"]}, {"file_path": "src/core/cover_downloader.py", "original_lines": 289, "optimized_lines": 289, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/audio_manager.py", "original_lines": 447, "optimized_lines": 447, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/base_installer.py", "original_lines": 87, "optimized_lines": 87, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["优化导入语句顺序", "优化代码格式和空行", "分析类型提示需求"]}, {"file_path": "src/core/rom_downloader.py", "original_lines": 544, "optimized_lines": 544, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/device_manager.py", "original_lines": 427, "optimized_lines": 427, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["类型提示分析失败: expected an indented block (<unknown>, line 254)"]}, {"file_path": "src/core/retropie_installer.py", "original_lines": 784, "optimized_lines": 784, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/settings_manager.py", "original_lines": 350, "optimized_lines": 350, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/bing_cover_downloader.py", "original_lines": 371, "optimized_lines": 371, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/game_launcher.py", "original_lines": 352, "optimized_lines": 352, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["类型提示分析失败: invalid syntax (<unknown>, line 235)"]}, {"file_path": "src/core/system_checker.py", "original_lines": 642, "optimized_lines": 642, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/core/game_health_checker.py", "original_lines": 610, "optimized_lines": 610, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/web/web_config.py", "original_lines": 91, "optimized_lines": 91, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["优化导入语句顺序", "分析类型提示需求"]}, {"file_path": "src/scripts/demo_all_features.py", "original_lines": 527, "optimized_lines": 527, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/auto_save_sync.py", "original_lines": 212, "optimized_lines": 212, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/test_auto_cheats.py", "original_lines": 203, "optimized_lines": 203, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/test_enhanced_covers.py", "original_lines": 216, "optimized_lines": 216, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/verify_docker_integration.py", "original_lines": 264, "optimized_lines": 264, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/quick_function_test.py", "original_lines": 282, "optimized_lines": 282, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/auto_code_fix.py", "original_lines": 361, "optimized_lines": 361, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/simple_demo_server.py", "original_lines": 2544, "optimized_lines": 2544, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/demo_game.py", "original_lines": 296, "optimized_lines": 296, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/smart_installer.py", "original_lines": 576, "optimized_lines": 576, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["类型提示分析失败: expected an indented block (<unknown>, line 539)"]}, {"file_path": "src/scripts/rom_manager.py", "original_lines": 344, "optimized_lines": 344, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/image_integration_checker.py", "original_lines": 431, "optimized_lines": 431, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/download_roms.py", "original_lines": 221, "optimized_lines": 221, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/check_dependencies.py", "original_lines": 351, "optimized_lines": 351, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/nes_game_launcher.py", "original_lines": 616, "optimized_lines": 617, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["优化代码格式和空行", "分析类型提示需求"]}, {"file_path": "src/scripts/final_nes_fix.py", "original_lines": 316, "optimized_lines": 316, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/auto_security_fix.py", "original_lines": 263, "optimized_lines": 263, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/rom_downloader.py", "original_lines": 553, "optimized_lines": 554, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["优化代码格式和空行", "分析类型提示需求"]}, {"file_path": "src/scripts/fix_emulator_startup.py", "original_lines": 417, "optimized_lines": 417, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/check_core_dependencies.py", "original_lines": 167, "optimized_lines": 167, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/test_core_functions.py", "original_lines": 402, "optimized_lines": 402, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/universal_game_launcher.py", "original_lines": 114, "optimized_lines": 115, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["优化代码格式和空行", "分析类型提示需求"]}, {"file_path": "src/scripts/enhanced_game_launcher.py", "original_lines": 534, "optimized_lines": 534, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/auto_fix_all_games.py", "original_lines": 283, "optimized_lines": 283, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/quick_fix_nes.py", "original_lines": 226, "optimized_lines": 226, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/manage_cover_sources.py", "original_lines": 301, "optimized_lines": 301, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/simple_nes_player.py", "original_lines": 370, "optimized_lines": 370, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/fix_nes_emulator.py", "original_lines": 363, "optimized_lines": 363, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/run_nes_game.py", "original_lines": 401, "optimized_lines": 402, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["优化代码格式和空行", "分析类型提示需求"]}, {"file_path": "src/scripts/auto_full_pipeline.py", "original_lines": 304, "optimized_lines": 304, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/fix_nes_runtime_failure.py", "original_lines": 438, "optimized_lines": 438, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/auto_update_rom_sources.py", "original_lines": 361, "optimized_lines": 361, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/scripts/test_settings.py", "original_lines": 202, "optimized_lines": 202, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/systems/systems/retropie/core/log_analyzer.py", "original_lines": 218, "optimized_lines": 218, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/systems/systems/retropie/core/logger_config.py", "original_lines": 48, "optimized_lines": 48, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/systems/systems/retropie/core/log_uploader.py", "original_lines": 43, "optimized_lines": 43, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/systems/systems/retropie/core/retropie_installer.py", "original_lines": 573, "optimized_lines": 573, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/systems/systems/retropie/roms/rom_downloader.py", "original_lines": 557, "optimized_lines": 557, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/systems/systems/retropie/tests/tests/conftest.py", "original_lines": 8, "optimized_lines": 8, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["优化导入语句顺序", "分析类型提示需求"]}, {"file_path": "src/systems/systems/retropie/tests/tests/run_all_tests.py", "original_lines": 10, "optimized_lines": 10, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["优化导入语句顺序", "分析类型提示需求"]}, {"file_path": "src/systems/systems/hdmi/core/hdmi_config.py", "original_lines": 354, "optimized_lines": 354, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}, {"file_path": "src/systems/systems/hdmi/tests/test_hdmi_config.py", "original_lines": 229, "optimized_lines": 229, "issues_fixed": [], "performance_improvements": [], "code_quality_improvements": ["分析类型提示需求"]}]}