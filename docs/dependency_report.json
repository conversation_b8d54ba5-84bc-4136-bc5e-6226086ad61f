{"summary": {"total_python_files": 53, "total_third_party_imports": 28, "total_required_packages": 28, "total_requirements": 262, "total_installed": 54, "missing_packages": 16, "unused_packages": 252}, "third_party_imports": ["boto3", "botocore", "cheat_manager", "concurrent", "device_manager", "flask", "gzip", "logger_config", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "numpy", "<PERSON><PERSON><PERSON>", "pkg_resources", "platform", "pygame", "pytest", "requests", "rom_downloader", "save_manager", "setuptools", "signal", "socketserver", "struct", "tarfile", "tqdm", "urllib3", "wave", "webbrowser", "zipfile"], "required_packages": ["boto3", "botocore", "cheat-manager", "concurrent", "device-manager", "flask", "gzip", "logger-config", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "numpy", "<PERSON><PERSON><PERSON>", "pkg-resources", "platform", "pygame", "pytest", "requests", "rom-downloader", "save-manager", "setuptools", "signal", "socketserver", "struct", "tarfile", "tqdm", "urllib3", "wave", "webbrowser", "zipfile"], "requirements_files": {"main": ["boto3", "flask", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "pillow", "psutil", "pytest", "pytest-asyncio", "pytest-cov", "pytest-mock", "python-dotenv", "pyyaml", "requests", "tqdm"], "config": ["absl-py", "accelerate", "addict", "aiofiles", "aiohttp", "aiosignal", "albumentations", "alembic", "altair", "antlr4-python3-runtime", "anyio", "astroid", "async-generator", "async-timeout", "asynctest", "attrs", "autopep8", "bcrypt", "beautifulsoup4", "black", "bleak", "blinker", "boto3", "botocore", "braceexpand", "cachetools", "certifi", "cffi", "cfgv", "chardet", "charset-normalizer", "click", "colorama", "coloredlogs", "contourpy", "controlnet-aux", "coverage", "cryptography", "cssselect", "cycler", "dataclasses-json", "decorator", "diffusers", "dill", "distlib", "dnspython", "docker", "duckduckgo-search", "ecdsa", "einops", "entrypoints", "exceptiongroup", "<PERSON><PERSON><PERSON>", "ffmpy", "filelock", "flake8", "flask", "flask-cors", "flatbuffers", "fonttools", "frozenlist", "fsspec", "ftfy", "future", "gitdb", "git<PERSON><PERSON>on", "google-api-core", "google-api-python-client", "google-auth", "google-auth-httplib2", "google-auth-o<PERSON>hlib", "google-search-results", "googleapis-common-protos", "gradio", "greenlet", "grpcio", "gtts", "gunicorn", "h11", "httpcore", "httplib2", "httpx", "huggingface-hub", "humanfriendly", "identify", "idna", "imageio", "imageio-ffmpeg", "importlib-metadata", "importlib-resources", "iniconfig", "invisible-watermark", "isort", "itsdangerous", "jinja2", "jmespath", "joblib", "jsonschema", "kiwisolver", "kornia", "langchain", "langflow", "lazy-loader", "lightning-utilities", "linkify-it-py", "lmdb", "loguru", "lxml", "mako", "markdown", "markdown-it-py", "markupsafe", "marshmallow", "marshmallow-enum", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mccabe", "mdit-py-plugins", "mdurl", "mpmath", "msgpack", "multidict", "mypy", "mypy-extensions", "networkx", "nodeenv", "numpy", "<PERSON><PERSON><PERSON><PERSON>", "omegaconf", "onnx", "onnxruntime", "openai", "opencv-python", "opencv-python-headless", "<PERSON><PERSON><PERSON>", "outcome", "packaging", "paho-mqtt", "pandas", "<PERSON><PERSON><PERSON>", "passlib", "pathspec", "pdoc", "pillow", "pinecone-client", "platformdirs", "playsound", "pluggy", "pre-commit", "prettytable", "protobuf", "psutil", "psycopg2-binary", "py-cpuinfo", "p<PERSON><PERSON>", "pyasn1", "pyasn1-modules", "pycodestyle", "pyc<PERSON><PERSON>", "pycryptodome", "pydantic", "<PERSON><PERSON><PERSON>", "pydub", "pyflakes", "pygments", "pylint", "pympler", "pymysql", "pynacl", "pyobjc-core", "pyobjc-framework-cocoa", "pyobjc-framework-corebluetooth", "pyobjc-framework-libdispatch", "pyparsing", "pyrsistent", "pyserial", "pysocks", "pytest", "pytest-asyncio", "pytest-benchmark", "pytest-cov", "pytest-integration", "pytest-mock", "python-can", "python-dateutil", "python-dotenv", "python-jose", "python-multipart", "pytz", "pytz-deprecation-shim", "pywavelets", "pyyaml", "qudida", "readability-lxml", "redis", "regex", "requests", "requests-o<PERSON><PERSON><PERSON>", "rfc3986", "rich", "rsa", "ruff", "s3transfer", "safetensors", "scikit-image", "scikit-learn", "scipy", "selenium", "semver", "sentencepiece", "six", "smmap", "sniffio", "sortedcontainers", "soupsieve", "sourcery", "sqlalchemy", "starlette", "streamlit", "sympy", "tb-nightly", "tenacity", "tensorboard", "tensorboard-data-server", "tensorboard-plugin-wit", "test-tube", "threadpoolctl", "tifffile", "tiktoken", "timm", "tokenizers", "toml", "to<PERSON>li", "<PERSON><PERSON><PERSON><PERSON>", "toolz", "tornado", "tqdm", "transformers", "trio", "trio-websocket", "tweepy", "typer", "typing-extensions", "typing-inspect", "tzdata", "tzlocal", "uc-micro-py", "uritemplate", "urllib3", "u<PERSON><PERSON>", "validators", "virtualenv", "wcwidth", "webdataset", "webdriver-manager", "websocket-client", "websockets", "werkzeug", "wrapt", "wsproto", "yapf", "yarl", "zipp"]}, "installed_packages": ["bcrypt", "blinker", "boto3", "botocore", "certifi", "cffi", "charset-normalizer", "click", "contourpy", "coverage", "cryptography", "cycler", "exceptiongroup", "flask", "fonttools", "idna", "iniconfig", "itsdangerous", "jinja2", "jmespath", "kiwisolver", "markupsafe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "numpy", "packaging", "paho-mqtt", "<PERSON><PERSON><PERSON>", "pillow", "pip", "pluggy", "psutil", "pyc<PERSON><PERSON>", "pygame", "pygments", "pynacl", "pyparsing", "pytest", "pytest-asyncio", "pytest-cov", "pytest-mock", "python-dateutil", "python-dotenv", "pyyaml", "requests", "ruff", "s3transfer", "setuptools", "six", "sqlalchemy", "to<PERSON>li", "tqdm", "typing_extensions", "urllib3", "werkzeug"], "missing_packages": ["cheat-manager", "concurrent", "device-manager", "gzip", "logger-config", "pkg-resources", "platform", "rom-downloader", "save-manager", "signal", "socketserver", "struct", "tarfile", "wave", "webbrowser", "zipfile"], "unused_packages": ["absl-py", "accelerate", "addict", "aiofiles", "aiohttp", "aiosignal", "albumentations", "alembic", "altair", "antlr4-python3-runtime", "anyio", "astroid", "async-generator", "async-timeout", "asynctest", "attrs", "autopep8", "bcrypt", "beautifulsoup4", "black", "bleak", "blinker", "braceexpand", "cachetools", "certifi", "cffi", "cfgv", "chardet", "charset-normalizer", "click", "colorama", "coloredlogs", "contourpy", "controlnet-aux", "coverage", "cryptography", "cssselect", "cycler", "dataclasses-json", "decorator", "diffusers", "dill", "distlib", "dnspython", "docker", "duckduckgo-search", "ecdsa", "einops", "entrypoints", "exceptiongroup", "<PERSON><PERSON><PERSON>", "ffmpy", "filelock", "flake8", "flask-cors", "flatbuffers", "fonttools", "frozenlist", "fsspec", "ftfy", "future", "gitdb", "git<PERSON><PERSON>on", "google-api-core", "google-api-python-client", "google-auth", "google-auth-httplib2", "google-auth-o<PERSON>hlib", "google-search-results", "googleapis-common-protos", "gradio", "greenlet", "grpcio", "gtts", "gunicorn", "h11", "httpcore", "httplib2", "httpx", "huggingface-hub", "humanfriendly", "identify", "idna", "imageio", "imageio-ffmpeg", "importlib-metadata", "importlib-resources", "iniconfig", "invisible-watermark", "isort", "itsdangerous", "jinja2", "jmespath", "joblib", "jsonschema", "kiwisolver", "kornia", "langchain", "langflow", "lazy-loader", "lightning-utilities", "linkify-it-py", "lmdb", "loguru", "lxml", "mako", "markdown", "markdown-it-py", "markupsafe", "marshmallow", "marshmallow-enum", "mccabe", "mdit-py-plugins", "mdurl", "mpmath", "msgpack", "multidict", "mypy", "mypy-extensions", "networkx", "nodeenv", "<PERSON><PERSON><PERSON><PERSON>", "omegaconf", "onnx", "onnxruntime", "openai", "opencv-python", "opencv-python-headless", "<PERSON><PERSON><PERSON>", "outcome", "packaging", "paho-mqtt", "pandas", "passlib", "pathspec", "pdoc", "pillow", "pinecone-client", "platformdirs", "playsound", "pluggy", "pre-commit", "prettytable", "protobuf", "psutil", "psycopg2-binary", "py-cpuinfo", "p<PERSON><PERSON>", "pyasn1", "pyasn1-modules", "pycodestyle", "pyc<PERSON><PERSON>", "pycryptodome", "pydantic", "<PERSON><PERSON><PERSON>", "pydub", "pyflakes", "pygments", "pylint", "pympler", "pymysql", "pynacl", "pyobjc-core", "pyobjc-framework-cocoa", "pyobjc-framework-corebluetooth", "pyobjc-framework-libdispatch", "pyparsing", "pyrsistent", "pyserial", "pysocks", "pytest-asyncio", "pytest-benchmark", "pytest-cov", "pytest-integration", "pytest-mock", "python-can", "python-dateutil", "python-dotenv", "python-jose", "python-multipart", "pytz", "pytz-deprecation-shim", "pywavelets", "pyyaml", "qudida", "readability-lxml", "redis", "regex", "requests-o<PERSON><PERSON><PERSON>", "rfc3986", "rich", "rsa", "ruff", "s3transfer", "safetensors", "scikit-image", "scikit-learn", "scipy", "selenium", "semver", "sentencepiece", "six", "smmap", "sniffio", "sortedcontainers", "soupsieve", "sourcery", "sqlalchemy", "starlette", "streamlit", "sympy", "tb-nightly", "tenacity", "tensorboard", "tensorboard-data-server", "tensorboard-plugin-wit", "test-tube", "threadpoolctl", "tifffile", "tiktoken", "timm", "tokenizers", "toml", "to<PERSON>li", "<PERSON><PERSON><PERSON><PERSON>", "toolz", "tornado", "transformers", "trio", "trio-websocket", "tweepy", "typer", "typing-extensions", "typing-inspect", "tzdata", "tzlocal", "uc-micro-py", "uritemplate", "u<PERSON><PERSON>", "validators", "virtualenv", "wcwidth", "webdataset", "webdriver-manager", "websocket-client", "websockets", "werkzeug", "wrapt", "wsproto", "yapf", "yarl", "zipp"], "file_imports": {"src/core/nes_emulator.py": ["cheat_manager", "device_manager", "pygame", "save_manager", "struct"], "src/core/virtuanes_installer.py": ["requests"], "src/core/rom_manager.py": ["requests", "zipfile"], "src/core/nesticle_installer.py": ["requests"], "src/core/save_manager.py": ["requests"], "src/core/audio_manager.py": ["numpy", "pygame", "wave"], "src/core/rom_downloader.py": ["<PERSON><PERSON><PERSON>", "requests", "tqdm", "urllib3", "zipfile"], "src/core/device_manager.py": ["pygame"], "src/core/retropie_installer.py": ["gzip", "platform", "requests", "tarfile", "zipfile"], "src/web/web_config.py": ["flask"], "src/scripts/auto_save_sync.py": ["boto3", "botocore", "<PERSON><PERSON><PERSON>", "requests", "tqdm"], "src/scripts/demo_game.py": ["pygame"], "src/scripts/smart_installer.py": ["pkg_resources", "platform"], "src/scripts/rom_manager.py": ["rom_downloader"], "src/scripts/nes_game_launcher.py": ["pygame", "rom_downloader"], "src/scripts/rom_downloader.py": ["requests", "zipfile"], "src/scripts/enhanced_game_launcher.py": ["socketserver", "webbrowser"], "src/scripts/raspberry_image_builder.py": ["gzip", "requests", "rom_downloader", "zipfile"], "src/scripts/simple_nes_player.py": ["pygame"], "src/scripts/run_nes_game.py": ["signal"], "src/systems/systems/retropie/core/log_analyzer.py": ["flask", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requests"], "src/systems/systems/retropie/core/logger_config.py": ["boto3"], "src/systems/systems/retropie/core/log_uploader.py": ["boto3"], "src/systems/systems/retropie/roms/rom_downloader.py": ["concurrent", "logger_config", "<PERSON><PERSON><PERSON>", "requests", "tqdm", "urllib3", "zipfile"], "src/systems/systems/retropie/tests/tests/conftest.py": ["pytest"], "src/systems/systems/retropie/tests/tests/run_all_tests.py": ["pytest"], "src/systems/systems/hdmi/core/hdmi_config.py": ["logger_config"], "src/systems/systems/hdmi/tests/test_hdmi_config.py": ["pytest"], "setup.py": ["setuptools"]}}