#!/usr/bin/env python3
"""
简化的GamePlayer-Raspberry演示服务器
用于Docker浏览器演示
"""

import os
import sys
import json
import time
import threading
from pathlib import Path
from flask import Flask, render_template_string, jsonify, request, send_from_directory

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入核心模块
try:
    from src.core.game_launcher import GameLauncher
    from src.core.system_checker import SystemChecker
    from src.core.cheat_manager import CheatManager
    from src.core.settings_manager import SettingsManager
except ImportError as e:
    print(f"⚠️ 导入核心模块失败: {e}")
    GameLauncher = None
    SystemChecker = None
    CheatManager = None
    SettingsManager = None

app = Flask(__name__)

# 初始化核心组件
game_launcher = GameLauncher() if GameLauncher else None
system_checker = SystemChecker() if SystemChecker else None
cheat_manager = CheatManager() if CheatManager else None
settings_manager = SettingsManager() if SettingsManager else None

# 模拟游戏数据库
GAMES_DATABASE = {
    "nes": [
        {
            "id": "super_mario_bros",
            "name": "Super Mario Bros",
            "file": "Super_Mario_Bros_Demo.nes",
            "description": "经典的横版跳跃游戏，马里奥的冒险之旅",
            "genre": "平台跳跃",
            "year": 1985,
            "players": "1-2",
            "image": "/static/images/nes/super_mario_bros.png",
            "cheats": ["infinite_lives", "invincibility", "level_select"],
            "save_slots": 3
        },
        {
            "id": "zelda",
            "name": "The Legend of Zelda",
            "file": "The_Legend_of_Zelda_Demo.nes",
            "description": "史诗级冒险RPG，林克的传说开始",
            "genre": "动作冒险",
            "year": 1986,
            "players": "1",
            "image": "/static/images/nes/zelda.png",
            "cheats": ["infinite_lives", "max_abilities", "all_items"],
            "save_slots": 3
        },
        {
            "id": "metroid",
            "name": "Metroid",
            "file": "Metroid_Demo.nes",
            "description": "科幻探索游戏，萨姆斯的银河冒险",
            "genre": "动作冒险",
            "year": 1986,
            "players": "1",
            "image": "/static/images/nes/metroid.png",
            "cheats": ["infinite_lives", "invincibility", "all_weapons"],
            "save_slots": 3
        },
        {
            "id": "castlevania",
            "name": "Castlevania",
            "file": "Castlevania_Demo.nes",
            "description": "哥特式动作游戏，对抗德古拉伯爵",
            "genre": "动作",
            "year": 1986,
            "players": "1",
            "image": "/static/images/nes/castlevania.png",
            "cheats": ["infinite_lives", "invincibility", "max_abilities"],
            "save_slots": 3
        },
        {
            "id": "mega_man",
            "name": "Mega Man",
            "file": "Mega_Man_Demo.nes",
            "description": "机器人动作游戏，洛克人的战斗",
            "genre": "动作",
            "year": 1987,
            "players": "1",
            "image": "/static/images/nes/mega_man.png",
            "cheats": ["infinite_lives", "all_weapons", "invincibility"],
            "save_slots": 3
        }
    ],
    "snes": [
        {
            "id": "super_mario_world",
            "name": "Super Mario World",
            "file": "Super_Mario_World_Demo.smc",
            "description": "超级马里奥的恐龙岛冒险",
            "genre": "平台跳跃",
            "year": 1990,
            "players": "1-2",
            "image": "/static/images/snes/super_mario_world.png",
            "cheats": ["infinite_lives", "invincibility", "level_select"],
            "save_slots": 4
        },
        {
            "id": "chrono_trigger",
            "name": "Chrono Trigger",
            "file": "Chrono_Trigger_Demo.smc",
            "description": "时空穿越RPG经典之作",
            "genre": "RPG",
            "year": 1995,
            "players": "1",
            "image": "/static/images/snes/chrono_trigger.png",
            "cheats": ["max_abilities", "infinite_mp", "all_items"],
            "save_slots": 3
        },
        {
            "id": "super_metroid",
            "name": "Super Metroid",
            "file": "Super_Metroid_Demo.smc",
            "description": "萨姆斯的银河探索续作",
            "genre": "动作冒险",
            "year": 1994,
            "players": "1",
            "image": "/static/images/snes/super_metroid.png",
            "cheats": ["infinite_lives", "invincibility", "all_weapons"],
            "save_slots": 3
        }
    ],
    "gameboy": [
        {
            "id": "tetris",
            "name": "Tetris",
            "file": "Tetris_Demo.gb",
            "description": "经典俄罗斯方块游戏",
            "genre": "益智",
            "year": 1989,
            "players": "1-2",
            "image": "/static/images/gb/tetris.png",
            "cheats": ["infinite_time", "level_select"],
            "save_slots": 1
        },
        {
            "id": "pokemon_red",
            "name": "Pokemon Red",
            "file": "Pokemon_Red_Demo.gb",
            "description": "口袋妖怪红版，收集所有精灵",
            "genre": "RPG",
            "year": 1996,
            "players": "1",
            "image": "/static/images/gb/pokemon_red.png",
            "cheats": ["infinite_money", "all_pokemon", "max_level"],
            "save_slots": 1
        }
    ],
    "gba": [
        {
            "id": "pokemon_ruby",
            "name": "Pokemon Ruby",
            "file": "Pokemon_Ruby_Demo.gba",
            "description": "口袋妖怪红宝石版",
            "genre": "RPG",
            "year": 2002,
            "players": "1",
            "image": "/static/images/gba/pokemon_ruby.png",
            "cheats": ["infinite_money", "all_pokemon", "max_level"],
            "save_slots": 1
        },
        {
            "id": "fire_emblem",
            "name": "Fire Emblem",
            "file": "Fire_Emblem_Demo.gba",
            "description": "战略RPG经典之作",
            "genre": "战略RPG",
            "year": 2003,
            "players": "1",
            "image": "/static/images/gba/fire_emblem.png",
            "cheats": ["infinite_money", "max_stats", "all_weapons"],
            "save_slots": 3
        }
    ],
    "genesis": [
        {
            "id": "sonic",
            "name": "Sonic the Hedgehog",
            "file": "Sonic_the_Hedgehog_Demo.md",
            "description": "音速小子的高速冒险",
            "genre": "平台跳跃",
            "year": 1991,
            "players": "1",
            "image": "/static/images/genesis/sonic.png",
            "cheats": ["infinite_lives", "invincibility", "level_select"],
            "save_slots": 3
        }
    ]
}

# 模拟存档数据
SAVE_DATA = {
    "super_mario_bros": {
        "slot_1": {"level": "1-1", "lives": 3, "score": 1200, "timestamp": "2025-06-27 10:30:00"},
        "slot_2": {"level": "4-2", "lives": 5, "score": 15600, "timestamp": "2025-06-26 15:45:00"},
        "slot_3": {"level": "8-4", "lives": 2, "score": 45200, "timestamp": "2025-06-25 20:15:00"}
    },
    "zelda": {
        "slot_1": {"area": "Hyrule Castle", "hearts": 8, "items": ["sword", "shield"], "timestamp": "2025-06-27 09:15:00"},
        "slot_2": {"area": "Death Mountain", "hearts": 12, "items": ["sword", "shield", "bow"], "timestamp": "2025-06-26 18:30:00"}
    }
}

# 模拟金手指配置
CHEAT_CONFIGS = {
    "infinite_lives": {"name": "无限生命", "code": "AEAEAE", "enabled": True, "auto": True},
    "invincibility": {"name": "无敌模式", "code": "AEAEAE", "enabled": True, "auto": True},
    "level_select": {"name": "关卡选择", "code": "AAAAAA", "enabled": True, "auto": True},
    "max_abilities": {"name": "最大能力", "code": "AEAEAE", "enabled": True, "auto": True},
    "all_weapons": {"name": "全武器", "code": "BBBBBB", "enabled": False, "auto": False},
    "all_items": {"name": "全道具", "code": "CCCCCC", "enabled": False, "auto": False},
    "infinite_money": {"name": "无限金钱", "code": "DDDDDD", "enabled": False, "auto": False},
    "max_level": {"name": "最高等级", "code": "EEEEEE", "enabled": False, "auto": False}
}

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 GamePlayer-Raspberry - 游戏中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .status-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #FFD700;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .feature-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .demo-section {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .demo-button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .demo-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .games-section {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .system-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .system-tab {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .system-tab:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .system-tab.active {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border-color: #FFD700;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .game-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .game-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
            border-color: #FFD700;
        }

        .game-card.playing {
            border: 2px solid #4CAF50;
            box-shadow: 0 0 20px rgba(76, 175, 80, 0.5);
        }

        .game-image {
            width: 100%;
            height: 150px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 10px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            position: relative;
        }

        .game-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 8px;
            color: #FFD700;
        }

        .game-info {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .game-description {
            font-size: 0.85rem;
            opacity: 0.8;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .game-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 15px;
        }

        .badge {
            background: rgba(255,215,0,0.2);
            color: #FFD700;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            border: 1px solid rgba(255,215,0,0.3);
        }

        .play-button {
            width: 100%;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: scale(1.02);
        }

        .play-button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .game-status {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-content {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255,255,255,0.3);
            border-top: 5px solid #FFD700;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            opacity: 0.8;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="pulse">🎮 GamePlayer-Raspberry</h1>
            <p>多系统游戏模拟器 - 游戏中心</p>
        </div>
        
        <div class="status-card">
            <h2>🚀 系统状态</h2>
            <p><strong>版本:</strong> v4.0.0</p>
            <p><strong>运行环境:</strong> Docker容器</p>
            <p><strong>Web服务器:</strong> Flask (端口 {{ port }})</p>
            <p><strong>状态:</strong> <span style="color: #4CAF50;">✅ 运行正常</span></p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <h3>🎮 支持的游戏系统</h3>
                <ul class="feature-list">
                    <li>NES (任天堂红白机)</li>
                    <li>SNES (超级任天堂)</li>
                    <li>Game Boy (掌机)</li>
                    <li>Game Boy Advance</li>
                    <li>Sega Genesis</li>
                    <li>PlayStation (PSX)</li>
                    <li>Nintendo 64</li>
                    <li>Arcade (街机)</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🎯 核心功能</h3>
                <ul class="feature-list">
                    <li>自动金手指系统</li>
                    <li>无限生命模式</li>
                    <li>无敌模式</li>
                    <li>关卡选择</li>
                    <li>存档管理</li>
                    <li>设置配置</li>
                    <li>Web界面控制</li>
                    <li>一键部署</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🔧 技术特性</h3>
                <ul class="feature-list">
                    <li>Docker容器化</li>
                    <li>树莓派优化</li>
                    <li>响应式Web设计</li>
                    <li>自动依赖管理</li>
                    <li>多环境支持</li>
                    <li>完整测试覆盖</li>
                    <li>详细文档</li>
                    <li>开源免费</li>
                </ul>
            </div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div class="stat-label">游戏系统</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100+</div>
                <div class="stat-label">游戏ROM</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100%</div>
                <div class="stat-label">测试通过</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">24</div>
                <div class="stat-label">金手指类型</div>
            </div>
        </div>
        
        <div class="games-section">
            <h2>🎮 游戏中心</h2>
            <p>选择游戏系统，点击游戏开始体验：</p>

            <div class="system-tabs">
                <div class="system-tab active" data-system="nes">🎮 NES</div>
                <div class="system-tab" data-system="snes">🎯 SNES</div>
                <div class="system-tab" data-system="gameboy">📱 Game Boy</div>
                <div class="system-tab" data-system="gba">🎲 GBA</div>
                <div class="system-tab" data-system="genesis">🔵 Genesis</div>
            </div>

            <div id="games-container">
                <div class="games-grid" id="games-grid">
                    <!-- 游戏卡片将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎮 快速功能</h2>
            <p>体验GamePlayer-Raspberry的核心功能：</p>
            <br>
            <button class="demo-button" onclick="showCheats()">🔧 金手指系统</button>
            <button class="demo-button" onclick="showSettings()">⚙️ 系统设置</button>
            <button class="demo-button" onclick="showStatus()">📊 系统状态</button>
            <button class="demo-button" onclick="showSaveData()">💾 存档管理</button>
        </div>
        
        <div class="footer">
            <p>🍓 GamePlayer-Raspberry - 让经典游戏在树莓派上重新焕发生机</p>
            <p>GitHub: <a href="https://github.com/LIUCHAOVSYAN/GamePlayer-Raspberry" style="color: #FFD700;">LIUCHAOVSYAN/GamePlayer-Raspberry</a></p>
        </div>
    </div>

    <!-- 加载覆盖层 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h3 id="loading-title">🎮 正在启动游戏...</h3>
            <p id="loading-message">正在加载ROM文件和配置...</p>
            <div id="loading-progress">
                <p>✅ 加载游戏文件</p>
                <p>✅ 应用金手指配置</p>
                <p>✅ 加载存档数据</p>
                <p>🎯 启动模拟器...</p>
            </div>
        </div>
    </div>
    
    <script>
        let currentSystem = 'nes';
        let gamesData = {};
        let currentGame = null;

        // 加载游戏数据
        async function loadGames() {
            try {
                const response = await fetch('/api/games');
                gamesData = await response.json();
                displayGames(currentSystem);
            } catch (error) {
                console.error('加载游戏数据失败:', error);
            }
        }

        // 显示游戏列表
        function displayGames(system) {
            const gamesGrid = document.getElementById('games-grid');
            const games = gamesData[system] || [];

            gamesGrid.innerHTML = games.map(game => `
                <div class="game-card" data-game-id="${game.id}">
                    <div class="game-status" id="status-${game.id}">就绪</div>
                    <div class="game-image">
                        🎮
                    </div>
                    <div class="game-title">${game.name}</div>
                    <div class="game-info">📅 ${game.year} | 👥 ${game.players}人 | 🎯 ${game.genre}</div>
                    <div class="game-description">${game.description}</div>
                    <div class="game-badges">
                        ${game.cheats.map(cheat => `<span class="badge">🎯 ${getCheatName(cheat)}</span>`).join('')}
                        <span class="badge">💾 ${game.save_slots}存档</span>
                    </div>
                    <button class="play-button" onclick="startGame('${game.id}', '${system}')">
                        🚀 开始游戏
                    </button>
                </div>
            `).join('');
        }

        // 获取金手指名称
        function getCheatName(cheatId) {
            const cheatNames = {
                'infinite_lives': '无限生命',
                'invincibility': '无敌模式',
                'level_select': '关卡选择',
                'max_abilities': '最大能力',
                'all_weapons': '全武器',
                'all_items': '全道具',
                'infinite_money': '无限金钱',
                'max_level': '最高等级'
            };
            return cheatNames[cheatId] || cheatId;
        }

        // 系统标签切换
        function setupSystemTabs() {
            const tabs = document.querySelectorAll('.system-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // 移除所有活动状态
                    tabs.forEach(t => t.classList.remove('active'));
                    // 添加当前活动状态
                    tab.classList.add('active');
                    // 更新当前系统
                    currentSystem = tab.dataset.system;
                    // 显示对应游戏
                    displayGames(currentSystem);
                });
            });
        }

        // 启动游戏
        async function startGame(gameId, system) {
            currentGame = { id: gameId, system: system };

            // 显示加载界面
            showLoading();

            try {
                // 调用真正的游戏启动API
                const response = await fetch('/api/launch_game', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        game_id: gameId,
                        system: system
                    })
                });

                const result = await response.json();

                // 隐藏加载界面
                hideLoading();

                if (result.success) {
                    // 显示游戏运行状态
                    updateGameStatus(gameId, '运行中');

                    // 显示游戏启动成功消息
                    showGameStartedReal(result);
                } else {
                    alert('游戏启动失败: ' + result.error);
                    updateGameStatus(gameId, '启动失败');
                }

            } catch (error) {
                hideLoading();
                alert('游戏启动失败: ' + error.message);
                updateGameStatus(gameId, '启动失败');
            }
        }

        // 模拟游戏启动过程
        async function simulateGameStart(gameId, system) {
            const steps = [
                { message: '正在加载ROM文件...', delay: 1000 },
                { message: '正在应用金手指配置...', delay: 800 },
                { message: '正在加载存档数据...', delay: 600 },
                { message: '正在启动模拟器...', delay: 1200 },
                { message: '游戏启动完成！', delay: 500 }
            ];

            for (let i = 0; i < steps.length; i++) {
                const step = steps[i];
                updateLoadingMessage(step.message);
                await new Promise(resolve => setTimeout(resolve, step.delay));
            }
        }

        // 显示加载界面
        function showLoading() {
            const overlay = document.getElementById('loading-overlay');
            overlay.style.display = 'flex';
            updateLoadingMessage('正在初始化游戏...');
        }

        // 隐藏加载界面
        function hideLoading() {
            const overlay = document.getElementById('loading-overlay');
            overlay.style.display = 'none';
        }

        // 更新加载消息
        function updateLoadingMessage(message) {
            const messageElement = document.getElementById('loading-message');
            messageElement.textContent = message;
        }

        // 更新游戏状态
        function updateGameStatus(gameId, status) {
            const statusElement = document.getElementById(`status-${gameId}`);
            if (statusElement) {
                statusElement.textContent = status;
                const gameCard = statusElement.closest('.game-card');
                if (status === '运行中') {
                    gameCard.classList.add('playing');
                } else {
                    gameCard.classList.remove('playing');
                }
            }
        }

        // 显示真正的游戏启动成功消息
        function showGameStartedReal(result) {
            const gameInfo = result.game_info;
            const enabledCheats = result.enabled_cheats || [];
            const isDemo = result.demo_mode || false;

            let message = `🎮 ${gameInfo.name} 启动成功！\\n\\n`;
            message += `📁 游戏文件: ${gameInfo.file}\\n`;
            message += `🎯 已启用金手指: ${enabledCheats.length}个\\n`;
            message += `💾 存档槽位: ${gameInfo.save_slots}个\\n\\n`;

            if (enabledCheats.length > 0) {
                message += `🎯 自动启用的金手指:\\n`;
                enabledCheats.forEach(cheat => {
                    message += `• ${cheat.name}\\n`;
                });
                message += `\\n`;
            }

            if (result.pid) {
                message += `🔧 进程ID: ${result.pid}\\n`;
                message += `🎮 游戏正在真实运行！\\n\\n`;
                message += `💡 提示: 游戏窗口可能会在后台打开`;
            } else if (isDemo) {
                message += `🎮 演示模式运行\\n`;
                message += `💡 在真实环境中，游戏会启动模拟器窗口`;
            } else {
                message += `🎮 游戏正在运行！`;
            }

            alert(message);
        }

        // 停止游戏
        async function stopGame(gameId) {
            try {
                const response = await fetch('/api/stop_game', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        game_id: gameId
                    })
                });

                const result = await response.json();

                if (result.success) {
                    updateGameStatus(gameId, '已停止');
                    alert('游戏已停止');
                } else {
                    alert('停止游戏失败: ' + result.error);
                }

            } catch (error) {
                alert('停止游戏失败: ' + error.message);
            }
        }

        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const response = await fetch('/api/system_check');
                const result = await response.json();

                let message = `🔍 系统状态检查结果\\n\\n`;
                message += `📊 总体状态: ${getStatusEmoji(result.overall_status)} ${result.overall_status}\\n\\n`;

                for (const [checkName, checkResult] of Object.entries(result.checks)) {
                    const status = checkResult.status ? '✅' : '❌';
                    message += `${status} ${checkName}: ${checkResult.message}\\n`;

                    if (checkResult.fix_result) {
                        const fixStatus = checkResult.fix_result.success ? '🔧✅' : '🔧❌';
                        message += `  ${fixStatus} 自动修复: ${checkResult.fix_result.message}\\n`;
                    }
                }

                if (result.demo_mode) {
                    message += `\\n💡 当前为演示模式`;
                }

                alert(message);

            } catch (error) {
                alert('系统检查失败: ' + error.message);
            }
        }

        function getStatusEmoji(status) {
            switch (status) {
                case 'healthy': return '🟢';
                case 'warning': return '🟡';
                case 'critical': return '🔴';
                default: return '⚪';
            }
        }

        // 显示金手指配置界面
        async function showCheats() {
            try {
                const response = await fetch(`/api/cheat_config/${currentSystem}`);
                const result = await response.json();

                if (result.success) {
                    showCheatConfigModal(result.cheats, currentSystem);
                } else {
                    alert('加载金手指配置失败');
                }
            } catch (error) {
                alert('加载金手指配置失败: ' + error.message);
            }
        }

        // 显示金手指配置模态框
        function showCheatConfigModal(cheats, system) {
            let modalHtml = `
                <div id="cheat-modal" style="
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.8); display: flex; align-items: center; justify-content: center;
                    z-index: 2000;
                ">
                    <div style="
                        background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);
                        border-radius: 15px; padding: 30px; max-width: 600px; width: 90%;
                        border: 1px solid rgba(255,255,255,0.2); color: white;
                        max-height: 80vh; overflow-y: auto;
                    ">
                        <h2>🎯 ${system.toUpperCase()} 金手指配置</h2>
                        <div id="cheat-list">
            `;

            for (const [cheatId, cheatData] of Object.entries(cheats)) {
                const checked = cheatData.enabled ? 'checked' : '';
                modalHtml += `
                    <div style="margin: 15px 0; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="checkbox" ${checked} onchange="toggleCheat('${system}', '${cheatId}', this.checked)"
                                   style="margin-right: 10px; transform: scale(1.2);">
                            <div>
                                <div style="font-weight: bold; color: #FFD700;">${cheatData.name || cheatId}</div>
                                <div style="font-size: 0.9em; opacity: 0.8;">${cheatData.description || '暂无描述'}</div>
                                <div style="font-size: 0.8em; opacity: 0.6;">代码: ${cheatData.code || 'N/A'}</div>
                            </div>
                        </label>
                    </div>
                `;
            }

            modalHtml += `
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <button onclick="closeCheatModal()" style="
                                background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
                                color: white; border: none; padding: 10px 20px;
                                border-radius: 20px; cursor: pointer;
                            ">关闭</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // 切换金手指状态
        async function toggleCheat(system, cheatId, enabled) {
            try {
                const response = await fetch(`/api/cheat_config/${system}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        cheat_id: cheatId,
                        enabled: enabled
                    })
                });

                const result = await response.json();

                if (!result.success) {
                    alert('更新金手指失败: ' + result.error);
                    // 恢复复选框状态
                    event.target.checked = !enabled;
                }
            } catch (error) {
                alert('更新金手指失败: ' + error.message);
                event.target.checked = !enabled;
            }
        }

        // 关闭金手指模态框
        function closeCheatModal() {
            const modal = document.getElementById('cheat-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 显示系统设置界面
        async function showSettings() {
            try {
                const response = await fetch('/api/settings');
                const result = await response.json();

                if (result.success) {
                    showSettingsModal(result.settings);
                } else {
                    alert('加载系统设置失败');
                }
            } catch (error) {
                alert('加载系统设置失败: ' + error.message);
            }
        }

        // 显示设置模态框
        function showSettingsModal(settings) {
            let modalHtml = `
                <div id="settings-modal" style="
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.8); display: flex; align-items: center; justify-content: center;
                    z-index: 2000;
                ">
                    <div style="
                        background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);
                        border-radius: 15px; padding: 30px; max-width: 700px; width: 90%;
                        border: 1px solid rgba(255,255,255,0.2); color: white;
                        max-height: 80vh; overflow-y: auto;
                    ">
                        <h2>⚙️ 系统设置</h2>
                        <div id="settings-list">
            `;

            for (const [category, categorySettings] of Object.entries(settings)) {
                modalHtml += `<h3 style="color: #FFD700; margin-top: 20px;">${getCategoryName(category)}</h3>`;

                for (const [setting, value] of Object.entries(categorySettings)) {
                    modalHtml += createSettingControl(category, setting, value);
                }
            }

            modalHtml += `
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <button onclick="closeSettingsModal()" style="
                                background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
                                color: white; border: none; padding: 10px 20px;
                                border-radius: 20px; cursor: pointer;
                            ">关闭</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        function getCategoryName(category) {
            const names = {
                'display': '🖥️ 显示设置',
                'audio': '🔊 音频设置',
                'input': '🎮 输入设置',
                'performance': '⚡ 性能设置'
            };
            return names[category] || category;
        }

        function createSettingControl(category, setting, value) {
            const settingId = `${category}_${setting}`;

            if (typeof value === 'boolean') {
                const checked = value ? 'checked' : '';
                return `
                    <div style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="checkbox" ${checked} onchange="updateSetting('${category}', '${setting}', this.checked)"
                                   style="margin-right: 10px; transform: scale(1.2);">
                            <span>${getSettingName(setting)}</span>
                        </label>
                    </div>
                `;
            } else if (typeof value === 'number') {
                return `
                    <div style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                        <label style="display: block;">
                            <span>${getSettingName(setting)}: ${value}</span>
                            <input type="range" min="0" max="100" value="${value}"
                                   onchange="updateSetting('${category}', '${setting}', parseInt(this.value))"
                                   style="width: 100%; margin-top: 5px;">
                        </label>
                    </div>
                `;
            } else {
                return `
                    <div style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                        <label style="display: block;">
                            <span>${getSettingName(setting)}:</span>
                            <input type="text" value="${value}"
                                   onchange="updateSetting('${category}', '${setting}', this.value)"
                                   style="width: 100%; margin-top: 5px; padding: 5px; border-radius: 5px; border: none;">
                        </label>
                    </div>
                `;
            }
        }

        function getSettingName(setting) {
            const names = {
                'fullscreen': '全屏模式',
                'resolution': '分辨率',
                'vsync': '垂直同步',
                'scaling': '缩放模式',
                'enabled': '启用',
                'volume': '音量',
                'sample_rate': '采样率',
                'buffer_size': '缓冲区大小',
                'gamepad_enabled': '手柄启用',
                'keyboard_enabled': '键盘启用',
                'auto_detect_gamepad': '自动检测手柄',
                'gamepad_deadzone': '手柄死区',
                'frame_skip': '跳帧',
                'speed_limit': '速度限制',
                'rewind_enabled': '倒带功能',
                'save_states': '即时存档'
            };
            return names[setting] || setting;
        }

        // 更新设置
        async function updateSetting(category, setting, value) {
            try {
                const response = await fetch('/api/settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        category: category,
                        setting: setting,
                        value: value
                    })
                });

                const result = await response.json();

                if (!result.success) {
                    alert('更新设置失败: ' + result.error);
                }
            } catch (error) {
                alert('更新设置失败: ' + error.message);
            }
        }

        // 关闭设置模态框
        function closeSettingsModal() {
            const modal = document.getElementById('settings-modal');
            if (modal) {
                modal.remove();
            }
        }

        function showStatus() {
            checkSystemStatus();
        }

        function showSaveData() {
            alert('💾 存档管理系统\\n\\n功能特性：\\n• 自动存档 - 游戏进度自动保存\\n• 多存档槽 - 支持多个存档位置\\n• 快速加载 - 一键加载存档\\n• 存档备份 - 云端同步存档\\n• 存档预览 - 显示存档详情\\n\\n永不丢失游戏进度！');
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载游戏数据
            loadGames();

            // 设置系统标签
            setupSystemTabs();

            // 添加动态效果
            const cards = document.querySelectorAll('.feature-card, .stat-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = (index * 0.1) + 's';
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });
        
        // CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE, port=request.environ.get('SERVER_PORT', '3000'))

@app.route('/api/status')
def api_status():
    """API状态"""
    return jsonify({
        'status': 'running',
        'version': 'v4.0.0',
        'environment': 'docker',
        'features': {
            'game_systems': 8,
            'rom_count': '100+',
            'cheat_types': 24,
            'test_coverage': '100%'
        }
    })

@app.route('/api/games')
def api_games():
    """游戏列表API"""
    return jsonify(GAMES_DATABASE)

@app.route('/api/game/<system>/<game_id>')
def api_game_info(system, game_id):
    """获取特定游戏信息"""
    games = GAMES_DATABASE.get(system, [])
    game = next((g for g in games if g['id'] == game_id), None)

    if not game:
        return jsonify({'error': 'Game not found'}), 404

    return jsonify(game)

@app.route('/api/saves/<game_id>')
def api_saves(game_id):
    """获取游戏存档信息"""
    saves = SAVE_DATA.get(game_id, {})

    # 找到最新存档
    latest_save = None
    latest_time = None

    for slot, save_data in saves.items():
        if 'timestamp' in save_data:
            if latest_time is None or save_data['timestamp'] > latest_time:
                latest_time = save_data['timestamp']
                latest_save = save_data

    return jsonify({
        'slots': len(saves),
        'saves': saves,
        'latest': latest_save
    })

@app.route('/api/cheats/<game_id>')
def api_game_cheats(game_id):
    """获取游戏金手指信息"""
    # 从游戏数据库中找到游戏
    game = None
    for system_games in GAMES_DATABASE.values():
        for g in system_games:
            if g['id'] == game_id:
                game = g
                break
        if game:
            break

    if not game:
        return jsonify({'error': 'Game not found'}), 404

    # 获取游戏支持的金手指
    game_cheats = game.get('cheats', [])

    # 构建金手指信息
    enabled_cheats = []
    available_cheats = []

    for cheat_id in game_cheats:
        cheat_config = CHEAT_CONFIGS.get(cheat_id, {})
        cheat_info = {
            'id': cheat_id,
            'name': cheat_config.get('name', cheat_id),
            'code': cheat_config.get('code', ''),
            'enabled': cheat_config.get('enabled', False),
            'auto': cheat_config.get('auto', False)
        }

        if cheat_config.get('auto', False):
            enabled_cheats.append(cheat_info)
        else:
            available_cheats.append(cheat_info)

    return jsonify({
        'enabled': enabled_cheats,
        'available': available_cheats,
        'total': len(game_cheats)
    })

@app.route('/api/cheats')
def api_cheats():
    """金手指系统API"""
    return jsonify({
        'configs': CHEAT_CONFIGS,
        'auto_enabled': [k for k, v in CHEAT_CONFIGS.items() if v.get('auto', False)],
        'available': list(CHEAT_CONFIGS.keys()),
        'systems_supported': ['nes', 'snes', 'gameboy', 'gba', 'genesis']
    })

@app.route('/api/launch_game', methods=['POST'])
def api_launch_game():
    """真正启动游戏API"""
    try:
        data = request.get_json()
        system = data.get('system')
        game_id = data.get('game_id')

        if not system or not game_id:
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400

        # 获取游戏信息
        games = GAMES_DATABASE.get(system, [])
        game = next((g for g in games if g['id'] == game_id), None)

        if not game:
            return jsonify({'success': False, 'error': '游戏不存在'}), 404

        # 如果有真正的游戏启动器，使用它
        if game_launcher:
            # 获取启用的金手指
            enabled_cheats = []
            for cheat_id in game.get('cheats', []):
                cheat_config = CHEAT_CONFIGS.get(cheat_id, {})
                if cheat_config.get('auto', False):
                    enabled_cheats.append({
                        'id': cheat_id,
                        'name': cheat_config.get('name', cheat_id),
                        'code': cheat_config.get('code', ''),
                        'enabled': True
                    })

            # 启动游戏
            success, message = game_launcher.launch_game(
                system=system,
                game_id=game_id,
                rom_file=game.get('file', ''),
                cheats=enabled_cheats,
                save_slot=1
            )

            if success:
                return jsonify({
                    'success': True,
                    'message': message,
                    'game_info': game,
                    'enabled_cheats': enabled_cheats,
                    'pid': message.split('PID: ')[-1].rstrip(')') if 'PID:' in message else None
                })
            else:
                return jsonify({'success': False, 'error': message}), 500

        else:
            # 模拟启动（演示模式）
            return jsonify({
                'success': True,
                'message': f'游戏启动成功 (演示模式)',
                'game_info': game,
                'enabled_cheats': [
                    {'id': cheat_id, 'name': CHEAT_CONFIGS.get(cheat_id, {}).get('name', cheat_id)}
                    for cheat_id in game.get('cheats', [])
                    if CHEAT_CONFIGS.get(cheat_id, {}).get('auto', False)
                ],
                'demo_mode': True
            })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/stop_game', methods=['POST'])
def api_stop_game():
    """停止游戏API"""
    try:
        data = request.get_json()
        game_id = data.get('game_id')

        if not game_id:
            return jsonify({'success': False, 'error': '缺少游戏ID'}), 400

        if game_launcher:
            success = game_launcher.stop_game(game_id)
            return jsonify({
                'success': success,
                'message': '游戏已停止' if success else '停止游戏失败'
            })
        else:
            return jsonify({
                'success': True,
                'message': '游戏已停止 (演示模式)',
                'demo_mode': True
            })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/game_status/<game_id>')
def api_game_status(game_id):
    """获取游戏状态API"""
    try:
        if game_launcher:
            status = game_launcher.get_game_status(game_id)
            is_running = game_launcher.is_game_running(game_id)

            return jsonify({
                'game_id': game_id,
                'status': status,
                'is_running': is_running,
                'running_games': list(game_launcher.get_running_games().keys())
            })
        else:
            return jsonify({
                'game_id': game_id,
                'status': '演示模式',
                'is_running': False,
                'demo_mode': True
            })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/system_check')
def api_system_check():
    """系统状态检查API"""
    try:
        if system_checker:
            results = system_checker.check_all_systems()
            return jsonify(results)
        else:
            # 模拟系统检查结果
            return jsonify({
                'timestamp': time.time(),
                'overall_status': 'healthy',
                'checks': {
                    'cheats': {'status': True, 'message': '金手指系统正常'},
                    'gamepad': {'status': True, 'message': '检测到虚拟手柄'},
                    'bluetooth': {'status': True, 'message': '蓝牙服务正常'},
                    'audio': {'status': True, 'message': '音频系统正常'},
                    'video': {'status': True, 'message': '视频系统正常'},
                    'emulators': {'status': False, 'message': '部分模拟器未安装', 'fixable': True},
                    'roms': {'status': True, 'message': '找到演示ROM文件'},
                    'saves': {'status': True, 'message': '存档系统正常'}
                },
                'demo_mode': True
            })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/cheat_config/<system>', methods=['GET', 'POST'])
def api_cheat_config(system):
    """金手指配置API"""
    try:
        if request.method == 'GET':
            # 获取金手指配置
            if cheat_manager:
                cheats = cheat_manager.get_all_cheats_for_system(system)
                return jsonify({
                    'system': system,
                    'cheats': cheats,
                    'success': True
                })
            else:
                # 返回模拟配置
                system_cheats = {}
                for cheat_id, cheat_config in CHEAT_CONFIGS.items():
                    system_cheats[cheat_id] = cheat_config.copy()

                return jsonify({
                    'system': system,
                    'cheats': system_cheats,
                    'demo_mode': True,
                    'success': True
                })

        elif request.method == 'POST':
            # 更新金手指配置
            data = request.get_json()
            cheat_id = data.get('cheat_id')
            enabled = data.get('enabled', False)

            if not cheat_id:
                return jsonify({'success': False, 'error': '缺少金手指ID'}), 400

            if cheat_manager:
                success = cheat_manager.update_cheat_status(system, cheat_id, enabled)
                return jsonify({
                    'success': success,
                    'message': f'金手指 {cheat_id} 已{"启用" if enabled else "禁用"}'
                })
            else:
                # 模拟更新
                if cheat_id in CHEAT_CONFIGS:
                    CHEAT_CONFIGS[cheat_id]['enabled'] = enabled
                    return jsonify({
                        'success': True,
                        'message': f'金手指 {cheat_id} 已{"启用" if enabled else "禁用"} (演示模式)',
                        'demo_mode': True
                    })
                else:
                    return jsonify({'success': False, 'error': '金手指不存在'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings', methods=['GET', 'POST'])
def api_settings():
    """系统设置API"""
    try:
        if request.method == 'GET':
            # 获取系统设置
            if settings_manager:
                settings = settings_manager.get_all_settings()
                return jsonify({
                    'settings': settings,
                    'success': True
                })
            else:
                # 返回模拟设置
                default_settings = {
                    'display': {
                        'fullscreen': True,
                        'resolution': '1920x1080',
                        'vsync': True,
                        'scaling': 'auto'
                    },
                    'audio': {
                        'enabled': True,
                        'volume': 80,
                        'sample_rate': 44100,
                        'buffer_size': 512
                    },
                    'input': {
                        'gamepad_enabled': True,
                        'keyboard_enabled': True,
                        'auto_detect_gamepad': True,
                        'gamepad_deadzone': 0.1
                    },
                    'performance': {
                        'frame_skip': 0,
                        'speed_limit': 100,
                        'rewind_enabled': False,
                        'save_states': True
                    }
                }

                return jsonify({
                    'settings': default_settings,
                    'demo_mode': True,
                    'success': True
                })

        elif request.method == 'POST':
            # 更新系统设置
            data = request.get_json()
            category = data.get('category')
            setting = data.get('setting')
            value = data.get('value')

            if not all([category, setting]):
                return jsonify({'success': False, 'error': '缺少必要参数'}), 400

            if settings_manager:
                success = settings_manager.update_setting(category, setting, value)
                return jsonify({
                    'success': success,
                    'message': f'设置 {category}.{setting} 已更新'
                })
            else:
                # 模拟更新
                return jsonify({
                    'success': True,
                    'message': f'设置 {category}.{setting} 已更新为 {value} (演示模式)',
                    'demo_mode': True
                })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 3000))
    print(f"🎮 GamePlayer-Raspberry Demo Server")
    print(f"🌐 启动Web服务器在端口 {port}")
    print(f"🔗 访问地址: http://localhost:{port}")
    print(f"📱 Docker演示模式已激活")
    
    app.run(host='0.0.0.0', port=port, debug=False)
