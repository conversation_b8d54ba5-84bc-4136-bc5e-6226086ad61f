#!/usr/bin/env python3
"""
简化的GamePlayer-Raspberry演示服务器
用于Docker浏览器演示
"""

import os
import sys
import json
import time
from pathlib import Path
from flask import Flask, render_template_string, jsonify, request, send_from_directory

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

app = Flask(__name__)

# 模拟游戏数据库
GAMES_DATABASE = {
    "nes": [
        {
            "id": "super_mario_bros",
            "name": "Super Mario Bros",
            "file": "Super_Mario_Bros_Demo.nes",
            "description": "经典的横版跳跃游戏，马里奥的冒险之旅",
            "genre": "平台跳跃",
            "year": 1985,
            "players": "1-2",
            "image": "/static/images/nes/super_mario_bros.png",
            "cheats": ["infinite_lives", "invincibility", "level_select"],
            "save_slots": 3
        },
        {
            "id": "zelda",
            "name": "The Legend of Zelda",
            "file": "The_Legend_of_Zelda_Demo.nes",
            "description": "史诗级冒险RPG，林克的传说开始",
            "genre": "动作冒险",
            "year": 1986,
            "players": "1",
            "image": "/static/images/nes/zelda.png",
            "cheats": ["infinite_lives", "max_abilities", "all_items"],
            "save_slots": 3
        },
        {
            "id": "metroid",
            "name": "Metroid",
            "file": "Metroid_Demo.nes",
            "description": "科幻探索游戏，萨姆斯的银河冒险",
            "genre": "动作冒险",
            "year": 1986,
            "players": "1",
            "image": "/static/images/nes/metroid.png",
            "cheats": ["infinite_lives", "invincibility", "all_weapons"],
            "save_slots": 3
        },
        {
            "id": "castlevania",
            "name": "Castlevania",
            "file": "Castlevania_Demo.nes",
            "description": "哥特式动作游戏，对抗德古拉伯爵",
            "genre": "动作",
            "year": 1986,
            "players": "1",
            "image": "/static/images/nes/castlevania.png",
            "cheats": ["infinite_lives", "invincibility", "max_abilities"],
            "save_slots": 3
        },
        {
            "id": "mega_man",
            "name": "Mega Man",
            "file": "Mega_Man_Demo.nes",
            "description": "机器人动作游戏，洛克人的战斗",
            "genre": "动作",
            "year": 1987,
            "players": "1",
            "image": "/static/images/nes/mega_man.png",
            "cheats": ["infinite_lives", "all_weapons", "invincibility"],
            "save_slots": 3
        }
    ],
    "snes": [
        {
            "id": "super_mario_world",
            "name": "Super Mario World",
            "file": "Super_Mario_World_Demo.smc",
            "description": "超级马里奥的恐龙岛冒险",
            "genre": "平台跳跃",
            "year": 1990,
            "players": "1-2",
            "image": "/static/images/snes/super_mario_world.png",
            "cheats": ["infinite_lives", "invincibility", "level_select"],
            "save_slots": 4
        },
        {
            "id": "chrono_trigger",
            "name": "Chrono Trigger",
            "file": "Chrono_Trigger_Demo.smc",
            "description": "时空穿越RPG经典之作",
            "genre": "RPG",
            "year": 1995,
            "players": "1",
            "image": "/static/images/snes/chrono_trigger.png",
            "cheats": ["max_abilities", "infinite_mp", "all_items"],
            "save_slots": 3
        },
        {
            "id": "super_metroid",
            "name": "Super Metroid",
            "file": "Super_Metroid_Demo.smc",
            "description": "萨姆斯的银河探索续作",
            "genre": "动作冒险",
            "year": 1994,
            "players": "1",
            "image": "/static/images/snes/super_metroid.png",
            "cheats": ["infinite_lives", "invincibility", "all_weapons"],
            "save_slots": 3
        }
    ],
    "gameboy": [
        {
            "id": "tetris",
            "name": "Tetris",
            "file": "Tetris_Demo.gb",
            "description": "经典俄罗斯方块游戏",
            "genre": "益智",
            "year": 1989,
            "players": "1-2",
            "image": "/static/images/gb/tetris.png",
            "cheats": ["infinite_time", "level_select"],
            "save_slots": 1
        },
        {
            "id": "pokemon_red",
            "name": "Pokemon Red",
            "file": "Pokemon_Red_Demo.gb",
            "description": "口袋妖怪红版，收集所有精灵",
            "genre": "RPG",
            "year": 1996,
            "players": "1",
            "image": "/static/images/gb/pokemon_red.png",
            "cheats": ["infinite_money", "all_pokemon", "max_level"],
            "save_slots": 1
        }
    ],
    "gba": [
        {
            "id": "pokemon_ruby",
            "name": "Pokemon Ruby",
            "file": "Pokemon_Ruby_Demo.gba",
            "description": "口袋妖怪红宝石版",
            "genre": "RPG",
            "year": 2002,
            "players": "1",
            "image": "/static/images/gba/pokemon_ruby.png",
            "cheats": ["infinite_money", "all_pokemon", "max_level"],
            "save_slots": 1
        },
        {
            "id": "fire_emblem",
            "name": "Fire Emblem",
            "file": "Fire_Emblem_Demo.gba",
            "description": "战略RPG经典之作",
            "genre": "战略RPG",
            "year": 2003,
            "players": "1",
            "image": "/static/images/gba/fire_emblem.png",
            "cheats": ["infinite_money", "max_stats", "all_weapons"],
            "save_slots": 3
        }
    ],
    "genesis": [
        {
            "id": "sonic",
            "name": "Sonic the Hedgehog",
            "file": "Sonic_the_Hedgehog_Demo.md",
            "description": "音速小子的高速冒险",
            "genre": "平台跳跃",
            "year": 1991,
            "players": "1",
            "image": "/static/images/genesis/sonic.png",
            "cheats": ["infinite_lives", "invincibility", "level_select"],
            "save_slots": 3
        }
    ]
}

# 模拟存档数据
SAVE_DATA = {
    "super_mario_bros": {
        "slot_1": {"level": "1-1", "lives": 3, "score": 1200, "timestamp": "2025-06-27 10:30:00"},
        "slot_2": {"level": "4-2", "lives": 5, "score": 15600, "timestamp": "2025-06-26 15:45:00"},
        "slot_3": {"level": "8-4", "lives": 2, "score": 45200, "timestamp": "2025-06-25 20:15:00"}
    },
    "zelda": {
        "slot_1": {"area": "Hyrule Castle", "hearts": 8, "items": ["sword", "shield"], "timestamp": "2025-06-27 09:15:00"},
        "slot_2": {"area": "Death Mountain", "hearts": 12, "items": ["sword", "shield", "bow"], "timestamp": "2025-06-26 18:30:00"}
    }
}

# 模拟金手指配置
CHEAT_CONFIGS = {
    "infinite_lives": {"name": "无限生命", "code": "AEAEAE", "enabled": True, "auto": True},
    "invincibility": {"name": "无敌模式", "code": "AEAEAE", "enabled": True, "auto": True},
    "level_select": {"name": "关卡选择", "code": "AAAAAA", "enabled": True, "auto": True},
    "max_abilities": {"name": "最大能力", "code": "AEAEAE", "enabled": True, "auto": True},
    "all_weapons": {"name": "全武器", "code": "BBBBBB", "enabled": False, "auto": False},
    "all_items": {"name": "全道具", "code": "CCCCCC", "enabled": False, "auto": False},
    "infinite_money": {"name": "无限金钱", "code": "DDDDDD", "enabled": False, "auto": False},
    "max_level": {"name": "最高等级", "code": "EEEEEE", "enabled": False, "auto": False}
}

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 GamePlayer-Raspberry - 游戏中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .status-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #FFD700;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .feature-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .demo-section {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .demo-button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .demo-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .games-section {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .system-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .system-tab {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .system-tab:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .system-tab.active {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border-color: #FFD700;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .game-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .game-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
            border-color: #FFD700;
        }

        .game-card.playing {
            border: 2px solid #4CAF50;
            box-shadow: 0 0 20px rgba(76, 175, 80, 0.5);
        }

        .game-image {
            width: 100%;
            height: 150px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 10px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            position: relative;
        }

        .game-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 8px;
            color: #FFD700;
        }

        .game-info {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .game-description {
            font-size: 0.85rem;
            opacity: 0.8;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .game-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 15px;
        }

        .badge {
            background: rgba(255,215,0,0.2);
            color: #FFD700;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            border: 1px solid rgba(255,215,0,0.3);
        }

        .play-button {
            width: 100%;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: scale(1.02);
        }

        .play-button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .game-status {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-content {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255,255,255,0.3);
            border-top: 5px solid #FFD700;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            opacity: 0.8;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="pulse">🎮 GamePlayer-Raspberry</h1>
            <p>多系统游戏模拟器 - 游戏中心</p>
        </div>
        
        <div class="status-card">
            <h2>🚀 系统状态</h2>
            <p><strong>版本:</strong> v4.0.0</p>
            <p><strong>运行环境:</strong> Docker容器</p>
            <p><strong>Web服务器:</strong> Flask (端口 {{ port }})</p>
            <p><strong>状态:</strong> <span style="color: #4CAF50;">✅ 运行正常</span></p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <h3>🎮 支持的游戏系统</h3>
                <ul class="feature-list">
                    <li>NES (任天堂红白机)</li>
                    <li>SNES (超级任天堂)</li>
                    <li>Game Boy (掌机)</li>
                    <li>Game Boy Advance</li>
                    <li>Sega Genesis</li>
                    <li>PlayStation (PSX)</li>
                    <li>Nintendo 64</li>
                    <li>Arcade (街机)</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🎯 核心功能</h3>
                <ul class="feature-list">
                    <li>自动金手指系统</li>
                    <li>无限生命模式</li>
                    <li>无敌模式</li>
                    <li>关卡选择</li>
                    <li>存档管理</li>
                    <li>设置配置</li>
                    <li>Web界面控制</li>
                    <li>一键部署</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🔧 技术特性</h3>
                <ul class="feature-list">
                    <li>Docker容器化</li>
                    <li>树莓派优化</li>
                    <li>响应式Web设计</li>
                    <li>自动依赖管理</li>
                    <li>多环境支持</li>
                    <li>完整测试覆盖</li>
                    <li>详细文档</li>
                    <li>开源免费</li>
                </ul>
            </div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div class="stat-label">游戏系统</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100+</div>
                <div class="stat-label">游戏ROM</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100%</div>
                <div class="stat-label">测试通过</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">24</div>
                <div class="stat-label">金手指类型</div>
            </div>
        </div>
        
        <div class="games-section">
            <h2>🎮 游戏中心</h2>
            <p>选择游戏系统，点击游戏开始体验：</p>

            <div class="system-tabs">
                <div class="system-tab active" data-system="nes">🎮 NES</div>
                <div class="system-tab" data-system="snes">🎯 SNES</div>
                <div class="system-tab" data-system="gameboy">📱 Game Boy</div>
                <div class="system-tab" data-system="gba">🎲 GBA</div>
                <div class="system-tab" data-system="genesis">🔵 Genesis</div>
            </div>

            <div id="games-container">
                <div class="games-grid" id="games-grid">
                    <!-- 游戏卡片将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎮 快速功能</h2>
            <p>体验GamePlayer-Raspberry的核心功能：</p>
            <br>
            <button class="demo-button" onclick="showCheats()">🔧 金手指系统</button>
            <button class="demo-button" onclick="showSettings()">⚙️ 系统设置</button>
            <button class="demo-button" onclick="showStatus()">📊 系统状态</button>
            <button class="demo-button" onclick="showSaveData()">💾 存档管理</button>
        </div>
        
        <div class="footer">
            <p>🍓 GamePlayer-Raspberry - 让经典游戏在树莓派上重新焕发生机</p>
            <p>GitHub: <a href="https://github.com/LIUCHAOVSYAN/GamePlayer-Raspberry" style="color: #FFD700;">LIUCHAOVSYAN/GamePlayer-Raspberry</a></p>
        </div>
    </div>

    <!-- 加载覆盖层 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h3 id="loading-title">🎮 正在启动游戏...</h3>
            <p id="loading-message">正在加载ROM文件和配置...</p>
            <div id="loading-progress">
                <p>✅ 加载游戏文件</p>
                <p>✅ 应用金手指配置</p>
                <p>✅ 加载存档数据</p>
                <p>🎯 启动模拟器...</p>
            </div>
        </div>
    </div>
    
    <script>
        let currentSystem = 'nes';
        let gamesData = {};
        let currentGame = null;

        // 加载游戏数据
        async function loadGames() {
            try {
                const response = await fetch('/api/games');
                gamesData = await response.json();
                displayGames(currentSystem);
            } catch (error) {
                console.error('加载游戏数据失败:', error);
            }
        }

        // 显示游戏列表
        function displayGames(system) {
            const gamesGrid = document.getElementById('games-grid');
            const games = gamesData[system] || [];

            gamesGrid.innerHTML = games.map(game => `
                <div class="game-card" data-game-id="${game.id}">
                    <div class="game-status" id="status-${game.id}">就绪</div>
                    <div class="game-image">
                        🎮
                    </div>
                    <div class="game-title">${game.name}</div>
                    <div class="game-info">📅 ${game.year} | 👥 ${game.players}人 | 🎯 ${game.genre}</div>
                    <div class="game-description">${game.description}</div>
                    <div class="game-badges">
                        ${game.cheats.map(cheat => `<span class="badge">🎯 ${getCheatName(cheat)}</span>`).join('')}
                        <span class="badge">💾 ${game.save_slots}存档</span>
                    </div>
                    <button class="play-button" onclick="startGame('${game.id}', '${system}')">
                        🚀 开始游戏
                    </button>
                </div>
            `).join('');
        }

        // 获取金手指名称
        function getCheatName(cheatId) {
            const cheatNames = {
                'infinite_lives': '无限生命',
                'invincibility': '无敌模式',
                'level_select': '关卡选择',
                'max_abilities': '最大能力',
                'all_weapons': '全武器',
                'all_items': '全道具',
                'infinite_money': '无限金钱',
                'max_level': '最高等级'
            };
            return cheatNames[cheatId] || cheatId;
        }

        // 系统标签切换
        function setupSystemTabs() {
            const tabs = document.querySelectorAll('.system-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // 移除所有活动状态
                    tabs.forEach(t => t.classList.remove('active'));
                    // 添加当前活动状态
                    tab.classList.add('active');
                    // 更新当前系统
                    currentSystem = tab.dataset.system;
                    // 显示对应游戏
                    displayGames(currentSystem);
                });
            });
        }

        // 启动游戏
        async function startGame(gameId, system) {
            currentGame = { id: gameId, system: system };

            // 显示加载界面
            showLoading();

            try {
                // 模拟游戏启动过程
                await simulateGameStart(gameId, system);

                // 隐藏加载界面
                hideLoading();

                // 显示游戏运行状态
                updateGameStatus(gameId, '运行中');

                // 显示游戏启动成功消息
                showGameStarted(gameId, system);

            } catch (error) {
                hideLoading();
                alert('游戏启动失败: ' + error.message);
            }
        }

        // 模拟游戏启动过程
        async function simulateGameStart(gameId, system) {
            const steps = [
                { message: '正在加载ROM文件...', delay: 1000 },
                { message: '正在应用金手指配置...', delay: 800 },
                { message: '正在加载存档数据...', delay: 600 },
                { message: '正在启动模拟器...', delay: 1200 },
                { message: '游戏启动完成！', delay: 500 }
            ];

            for (let i = 0; i < steps.length; i++) {
                const step = steps[i];
                updateLoadingMessage(step.message);
                await new Promise(resolve => setTimeout(resolve, step.delay));
            }
        }

        // 显示加载界面
        function showLoading() {
            const overlay = document.getElementById('loading-overlay');
            overlay.style.display = 'flex';
            updateLoadingMessage('正在初始化游戏...');
        }

        // 隐藏加载界面
        function hideLoading() {
            const overlay = document.getElementById('loading-overlay');
            overlay.style.display = 'none';
        }

        // 更新加载消息
        function updateLoadingMessage(message) {
            const messageElement = document.getElementById('loading-message');
            messageElement.textContent = message;
        }

        // 更新游戏状态
        function updateGameStatus(gameId, status) {
            const statusElement = document.getElementById(`status-${gameId}`);
            if (statusElement) {
                statusElement.textContent = status;
                const gameCard = statusElement.closest('.game-card');
                if (status === '运行中') {
                    gameCard.classList.add('playing');
                } else {
                    gameCard.classList.remove('playing');
                }
            }
        }

        // 显示游戏启动成功消息
        async function showGameStarted(gameId, system) {
            // 获取游戏信息
            const response = await fetch(`/api/game/${system}/${gameId}`);
            const gameInfo = await response.json();

            // 获取存档信息
            const saveResponse = await fetch(`/api/saves/${gameId}`);
            const saveData = await saveResponse.json();

            // 获取金手指信息
            const cheatResponse = await fetch(`/api/cheats/${gameId}`);
            const cheatData = await cheatResponse.json();

            let message = `🎮 ${gameInfo.name} 启动成功！\\n\\n`;
            message += `📁 游戏文件: ${gameInfo.file}\\n`;
            message += `🎯 已启用金手指: ${cheatData.enabled.length}个\\n`;
            message += `💾 可用存档: ${saveData.slots}个\\n\\n`;

            if (saveData.latest) {
                message += `📂 最新存档:\\n`;
                message += `• 位置: ${saveData.latest.level || saveData.latest.area}\\n`;
                message += `• 时间: ${saveData.latest.timestamp}\\n\\n`;
            }

            message += `🎯 自动启用的金手指:\\n`;
            cheatData.enabled.forEach(cheat => {
                message += `• ${cheat.name}\\n`;
            });

            message += `\\n🎮 游戏现在正在Docker容器中运行！`;

            alert(message);
        }

        function showCheats() {
            alert('🎯 自动金手指系统\\n\\n功能特性：\\n• 无限生命 - 永不死亡\\n• 无敌模式 - 免疫伤害\\n• 最大能力 - 属性最大化\\n• 关卡选择 - 任意关卡跳转\\n• 自动启用 - 游戏启动时自动开启\\n\\n让游戏变得更有趣！');
        }

        function showSettings() {
            alert('⚙️ 系统设置\\n\\n配置选项：\\n• 显示设置 - 分辨率、全屏模式\\n• 音频设置 - 音量、音效\\n• 控制器设置 - 按键映射\\n• 性能设置 - 帧率、优化\\n• 模拟器设置 - 各系统专用配置\\n\\n完全可定制的游戏体验！');
        }

        function showStatus() {
            alert('📊 系统状态\\n\\n当前状态：\\n• Docker容器: 运行正常\\n• Web服务器: Flask (端口 {{ port }})\\n• 依赖检查: 100% 通过\\n• ROM文件: 已加载\\n• 金手指: 已激活\\n• 设置: 已配置\\n\\n系统完全就绪！');
        }

        function showSaveData() {
            alert('💾 存档管理系统\\n\\n功能特性：\\n• 自动存档 - 游戏进度自动保存\\n• 多存档槽 - 支持多个存档位置\\n• 快速加载 - 一键加载存档\\n• 存档备份 - 云端同步存档\\n• 存档预览 - 显示存档详情\\n\\n永不丢失游戏进度！');
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载游戏数据
            loadGames();

            // 设置系统标签
            setupSystemTabs();

            // 添加动态效果
            const cards = document.querySelectorAll('.feature-card, .stat-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = (index * 0.1) + 's';
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });
        
        // CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE, port=request.environ.get('SERVER_PORT', '3000'))

@app.route('/api/status')
def api_status():
    """API状态"""
    return jsonify({
        'status': 'running',
        'version': 'v4.0.0',
        'environment': 'docker',
        'features': {
            'game_systems': 8,
            'rom_count': '100+',
            'cheat_types': 24,
            'test_coverage': '100%'
        }
    })

@app.route('/api/games')
def api_games():
    """游戏列表API"""
    return jsonify(GAMES_DATABASE)

@app.route('/api/game/<system>/<game_id>')
def api_game_info(system, game_id):
    """获取特定游戏信息"""
    games = GAMES_DATABASE.get(system, [])
    game = next((g for g in games if g['id'] == game_id), None)

    if not game:
        return jsonify({'error': 'Game not found'}), 404

    return jsonify(game)

@app.route('/api/saves/<game_id>')
def api_saves(game_id):
    """获取游戏存档信息"""
    saves = SAVE_DATA.get(game_id, {})

    # 找到最新存档
    latest_save = None
    latest_time = None

    for slot, save_data in saves.items():
        if 'timestamp' in save_data:
            if latest_time is None or save_data['timestamp'] > latest_time:
                latest_time = save_data['timestamp']
                latest_save = save_data

    return jsonify({
        'slots': len(saves),
        'saves': saves,
        'latest': latest_save
    })

@app.route('/api/cheats/<game_id>')
def api_game_cheats(game_id):
    """获取游戏金手指信息"""
    # 从游戏数据库中找到游戏
    game = None
    for system_games in GAMES_DATABASE.values():
        for g in system_games:
            if g['id'] == game_id:
                game = g
                break
        if game:
            break

    if not game:
        return jsonify({'error': 'Game not found'}), 404

    # 获取游戏支持的金手指
    game_cheats = game.get('cheats', [])

    # 构建金手指信息
    enabled_cheats = []
    available_cheats = []

    for cheat_id in game_cheats:
        cheat_config = CHEAT_CONFIGS.get(cheat_id, {})
        cheat_info = {
            'id': cheat_id,
            'name': cheat_config.get('name', cheat_id),
            'code': cheat_config.get('code', ''),
            'enabled': cheat_config.get('enabled', False),
            'auto': cheat_config.get('auto', False)
        }

        if cheat_config.get('auto', False):
            enabled_cheats.append(cheat_info)
        else:
            available_cheats.append(cheat_info)

    return jsonify({
        'enabled': enabled_cheats,
        'available': available_cheats,
        'total': len(game_cheats)
    })

@app.route('/api/cheats')
def api_cheats():
    """金手指系统API"""
    return jsonify({
        'configs': CHEAT_CONFIGS,
        'auto_enabled': [k for k, v in CHEAT_CONFIGS.items() if v.get('auto', False)],
        'available': list(CHEAT_CONFIGS.keys()),
        'systems_supported': ['nes', 'snes', 'gameboy', 'gba', 'genesis']
    })

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 3000))
    print(f"🎮 GamePlayer-Raspberry Demo Server")
    print(f"🌐 启动Web服务器在端口 {port}")
    print(f"🔗 访问地址: http://localhost:{port}")
    print(f"📱 Docker演示模式已激活")
    
    app.run(host='0.0.0.0', port=port, debug=False)
