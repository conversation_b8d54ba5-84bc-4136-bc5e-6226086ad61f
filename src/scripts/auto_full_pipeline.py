#!/usr/bin/env python3
"""
全流程自动化执行脚本
包含依赖更新、安全修复、代码优化、测试运行等完整流程
"""

import os
import sys
import subprocess
import time
import json
from pathlib import Path
from typing import Dict, List, Tuple


class AutoPipeline:
    def __init__(self, project_root: str = "."):
        """TODO: 添加文档字符串"""
        self.project_root = Path(project_root)
        self.results = {
            'steps': [],
            'errors': [],
            'warnings': [],
            'success': True
        }

    def log_step(self, step_name: str, status: str, message: str = ""):
        """记录执行步骤"""
        step = {
            'name': step_name,
            'status': status,
            'message': message,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.results['steps'].append(step)
        print(f"[{status.upper()}] {step_name}: {message}")

    def run_command(self, cmd: List[str], description: str, check: bool = True):
        """执行命令并记录结果"""
        try:
            self.log_step(description, "RUNNING")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)

            if result.returncode == 0:
                self.log_step(description, "SUCCESS", f"执行成功")
                return True
            else:
                error_msg = f"执行失败: {result.stderr}"
                self.log_step(description, "ERROR", error_msg)
                self.results['errors'].append(f"{description}: {error_msg}")
                if check:
                    self.results['success'] = False
                return False
        except Exception as e:
            error_msg = f"执行异常: {str(e)}"
            self.log_step(description, "ERROR", error_msg)
            self.results['errors'].append(f"{description}: {error_msg}")
            if check:
                self.results['success'] = False
            return False

    def step_1_update_dependencies(self):
        """步骤1: 更新依赖包"""
        print("\n" + "="*60)
        print("步骤1: 更新依赖包")
        print("="*60)

        # 检查pip版本
        self.run_command([sys.executable, "-m", "pip", "--version"], "检查pip版本", check=False)

        # 升级pip
        self.run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], "升级pip", check=False)

        # 安装/升级安全工具
        self.run_command([sys.executable, "-m", "pip", "install", "--upgrade", "safety", "bandit"], "安装安全工具", check=False)

        # 升级项目依赖
        success = self.run_command([sys.executable, "-m", "pip", "install", "-r", "requirements.txt", "--upgrade"], "升级项目依赖")

        return success

    def step_2_security_scan_and_fix(self):
        """步骤2: 安全扫描和修复"""
        print("\n" + "="*60)
        print("步骤2: 安全扫描和修复")
        print("="*60)

        # 运行安全修复脚本
        success = self.run_command([sys.executable, "src/scripts/auto_security_fix.py"], "运行安全修复脚本")

        # 运行bandit安全扫描
        self.run_command([sys.executable, "-m", "bandit", "-r", "src/", "-f", "json", "-o", "security_scan_final.json"], "运行安全扫描", check=False)

        return success

    def step_3_code_optimization(self):
        """步骤3: 代码优化"""
        print("\n" + "="*60)
        print("步骤3: 代码优化")
        print("="*60)

        # 运行代码分析器
        self.run_command([sys.executable, "tools/dev/code_analyzer.py"], "运行代码分析", check=False)

        # 运行代码优化器
        success = self.run_command([sys.executable, "tools/dev/code_optimizer.py"], "运行代码优化")

        # 运行项目清理器
        self.run_command([sys.executable, "tools/dev/project_cleaner.py"], "运行项目清理", check=False)

        return success

    def step_4_test_execution(self):
        """步骤4: 测试执行"""
        print("\n" + "="*60)
        print("步骤4: 测试执行")
        print("="*60)

        # 运行核心功能测试
        success = self.run_command([sys.executable, "src/scripts/test_core_functions.py"], "运行核心功能测试")

        # 运行单元测试
        self.run_command([sys.executable, "-m", "pytest", "tests/", "-v"], "运行单元测试", check=False)

        # 运行快速功能测试
        self.run_command([sys.executable, "src/scripts/quick_function_test.py"], "运行快速功能测试", check=False)

        return success

    def step_5_simulation_test(self):
        """步骤5: 模拟器测试"""
        print("\n" + "="*60)
        print("步骤5: 模拟器测试")
        print("="*60)

        # 检查模拟器集成
        self.run_command([sys.executable, "tools/dev/auto_install_top_nes_emulators.py"], "检查模拟器集成", check=False)

        # 运行Docker模拟测试
        self.run_command([sys.executable, "src/scripts/quick_docker_test.sh"], "运行Docker模拟测试", check=False)

        # 运行游戏启动测试
        success = self.run_command([sys.executable, "src/scripts/run_nes_game.py", "--test"], "运行游戏启动测试")

        return success

    def step_6_documentation_update(self):
        """步骤6: 文档更新"""
        print("\n" + "="*60)
        print("步骤6: 文档更新")
        print("="*60)

        # 生成项目状态报告
        self.run_command([sys.executable, "src/scripts/cleanup_and_report.sh"], "生成项目状态报告", check=False)

        # 更新README文档
        success = self.run_command([sys.executable, "-c", "print('文档更新完成')"], "更新文档", check=False)

        return success

    def step_7_git_operations(self):
        """步骤7: Git操作"""
        print("\n" + "="*60)
        print("步骤7: Git操作")
        print("="*60)

        # 检查Git状态
        self.run_command(["git", "status"], "检查Git状态", check=False)

        # 添加所有更改
        success = self.run_command(["git", "add", "."], "添加所有更改")

        if success:
            # 提交更改
            commit_msg = f"auto: 全流程自动化执行 - {time.strftime('%Y-%m-%d %H:%M:%S')}"
            success = self.run_command(["git", "commit", "-m", commit_msg], "提交更改")

            if success:
                # 推送到远程仓库
                success = self.run_command(["git", "push", "origin", "main"], "推送到远程仓库")

        return success

    def generate_final_report(self) -> str:
        """生成最终报告"""
        report = []
        report.append("# 全流程自动化执行报告")
        report.append("")
        report.append(f"**执行时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"**执行状态**: {'✅ 成功' if self.results['success'] else '❌ 失败'}")
        report.append("")

        # 步骤统计
        total_steps = len(self.results['steps'])
        success_steps = len([s for s in self.results['steps'] if s['status'] == 'SUCCESS'])
        error_steps = len([s for s in self.results['steps'] if s['status'] == 'ERROR'])

        report.append("## 📊 执行统计")
        report.append(f"- 总步骤数: {total_steps}")
        report.append(f"- 成功步骤: {success_steps}")
        report.append(f"- 失败步骤: {error_steps}")
        report.append(f"- 成功率: {success_steps/total_steps*100:.1f}%")
        report.append("")

        # 详细步骤
        report.append("## 📋 执行步骤详情")
        for step in self.results['steps']:
            status_icon = "✅" if step['status'] == 'SUCCESS' else "❌" if step['status'] == 'ERROR' else "⏳"
            report.append(f"- {status_icon} **{step['name']}** ({step['status']}) - {step['timestamp']}")
            if step['message']:
                report.append(f"  - {step['message']}")
        report.append("")

        # 错误信息
        if self.results['errors']:
            report.append("## ❌ 错误信息")
            for error in self.results['errors']:
                report.append(f"- {error}")
            report.append("")

        # 警告信息
        if self.results['warnings']:
            report.append("## ⚠️ 警告信息")
            for warning in self.results['warnings']:
                report.append(f"- {warning}")
            report.append("")

        # 建议
        report.append("## 💡 建议")
        if self.results['success']:
            report.append("- ✅ 全流程执行成功，项目状态良好")
            report.append("- 🔄 建议定期运行此脚本保持项目健康")
        else:
            report.append("- 🔧 存在执行错误，请检查错误信息")
            report.append("- 🛠️ 修复错误后重新运行脚本")

        return "\n".join(report)

    def run_full_pipeline(self):
        """运行完整流程"""
        print("🚀 开始全流程自动化执行")
        print("="*60)

        start_time = time.time()

        # 执行所有步骤
        steps = [
            ("更新依赖包", self.step_1_update_dependencies),
            ("安全扫描和修复", self.step_2_security_scan_and_fix),
            ("代码优化", self.step_3_code_optimization),
            ("测试执行", self.step_4_test_execution),
            ("模拟器测试", self.step_5_simulation_test),
            ("文档更新", self.step_6_documentation_update),
            ("Git操作", self.step_7_git_operations)
        ]

        for step_name, step_func in steps:
            try:
                success = step_func()
                if not success:
                    self.log_step(f"步骤失败: {step_name}", "ERROR", "步骤执行失败，但继续执行后续步骤")
                    self.results['warnings'].append(f"步骤 {step_name} 执行失败")
            except Exception as e:
                self.log_step(f"步骤异常: {step_name}", "ERROR", str(e))
                self.results['errors'].append(f"步骤 {step_name} 执行异常: {str(e)}")

        # 生成报告
        end_time = time.time()
        duration = end_time - start_time

        print("\n" + "="*60)
        print("📊 执行完成")
        print("="*60)
        print(f"总耗时: {duration:.2f}秒")
        print(f"执行状态: {'✅ 成功' if self.results['success'] else '❌ 失败'}")

        # 保存报告
        report = self.generate_final_report()
        report_file = self.project_root / "auto_pipeline_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"📄 详细报告已保存到: {report_file}")

        return self.results['success']


def main():
    """主函数"""
    print("🎮 GamePlayer-Raspberry 全流程自动化执行工具")
    print("="*60)

    pipeline = AutoPipeline()
    success = pipeline.run_full_pipeline()

    if success:
        print("\n🎉 全流程执行成功！项目已更新并推送到远程仓库。")
    else:
        print("\n⚠️ 全流程执行完成，但存在一些问题，请查看报告了解详情。")

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
