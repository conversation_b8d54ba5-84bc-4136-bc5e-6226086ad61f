window.pdocSearch = (function(){
/** elasticlunr - http://weixsong.github.io * Copyright (C) 2017 Oliver Nightingale * Copyright (C) 2017 <PERSON> Song * MIT Licensed */!function(){function e(e){if(null===e||"object"!=typeof e)return e;var t=e.constructor();for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}var t=function(e){var n=new t.Index;return n.pipeline.add(t.trimmer,t.stop<PERSON><PERSON>er,t.stemmer),e&&e.call(n,n),n};t.version="0.9.5",lunr=t,t.utils={},t.utils.warn=function(e){return function(t){e.console&&console.warn&&console.warn(t)}}(this),t.utils.toString=function(e){return void 0===e||null===e?"":e.toString()},t.EventEmitter=function(){this.events={}},t.EventEmitter.prototype.addListener=function(){var e=Array.prototype.slice.call(arguments),t=e.pop(),n=e;if("function"!=typeof t)throw new TypeError("last argument must be a function");n.forEach(function(e){this.hasHandler(e)||(this.events[e]=[]),this.events[e].push(t)},this)},t.EventEmitter.prototype.removeListener=function(e,t){if(this.hasHandler(e)){var n=this.events[e].indexOf(t);-1!==n&&(this.events[e].splice(n,1),0==this.events[e].length&&delete this.events[e])}},t.EventEmitter.prototype.emit=function(e){if(this.hasHandler(e)){var t=Array.prototype.slice.call(arguments,1);this.events[e].forEach(function(e){e.apply(void 0,t)},this)}},t.EventEmitter.prototype.hasHandler=function(e){return e in this.events},t.tokenizer=function(e){if(!arguments.length||null===e||void 0===e)return[];if(Array.isArray(e)){var n=e.filter(function(e){return null===e||void 0===e?!1:!0});n=n.map(function(e){return t.utils.toString(e).toLowerCase()});var i=[];return n.forEach(function(e){var n=e.split(t.tokenizer.seperator);i=i.concat(n)},this),i}return e.toString().trim().toLowerCase().split(t.tokenizer.seperator)},t.tokenizer.defaultSeperator=/[\s\-]+/,t.tokenizer.seperator=t.tokenizer.defaultSeperator,t.tokenizer.setSeperator=function(e){null!==e&&void 0!==e&&"object"==typeof e&&(t.tokenizer.seperator=e)},t.tokenizer.resetSeperator=function(){t.tokenizer.seperator=t.tokenizer.defaultSeperator},t.tokenizer.getSeperator=function(){return t.tokenizer.seperator},t.Pipeline=function(){this._queue=[]},t.Pipeline.registeredFunctions={},t.Pipeline.registerFunction=function(e,n){n in t.Pipeline.registeredFunctions&&t.utils.warn("Overwriting existing registered function: "+n),e.label=n,t.Pipeline.registeredFunctions[n]=e},t.Pipeline.getRegisteredFunction=function(e){return e in t.Pipeline.registeredFunctions!=!0?null:t.Pipeline.registeredFunctions[e]},t.Pipeline.warnIfFunctionNotRegistered=function(e){var n=e.label&&e.label in this.registeredFunctions;n||t.utils.warn("Function is not registered with pipeline. This may cause problems when serialising the index.\n",e)},t.Pipeline.load=function(e){var n=new t.Pipeline;return e.forEach(function(e){var i=t.Pipeline.getRegisteredFunction(e);if(!i)throw new Error("Cannot load un-registered function: "+e);n.add(i)}),n},t.Pipeline.prototype.add=function(){var e=Array.prototype.slice.call(arguments);e.forEach(function(e){t.Pipeline.warnIfFunctionNotRegistered(e),this._queue.push(e)},this)},t.Pipeline.prototype.after=function(e,n){t.Pipeline.warnIfFunctionNotRegistered(n);var i=this._queue.indexOf(e);if(-1===i)throw new Error("Cannot find existingFn");this._queue.splice(i+1,0,n)},t.Pipeline.prototype.before=function(e,n){t.Pipeline.warnIfFunctionNotRegistered(n);var i=this._queue.indexOf(e);if(-1===i)throw new Error("Cannot find existingFn");this._queue.splice(i,0,n)},t.Pipeline.prototype.remove=function(e){var t=this._queue.indexOf(e);-1!==t&&this._queue.splice(t,1)},t.Pipeline.prototype.run=function(e){for(var t=[],n=e.length,i=this._queue.length,o=0;n>o;o++){for(var r=e[o],s=0;i>s&&(r=this._queue[s](r,o,e),void 0!==r&&null!==r);s++);void 0!==r&&null!==r&&t.push(r)}return t},t.Pipeline.prototype.reset=function(){this._queue=[]},t.Pipeline.prototype.get=function(){return this._queue},t.Pipeline.prototype.toJSON=function(){return this._queue.map(function(e){return t.Pipeline.warnIfFunctionNotRegistered(e),e.label})},t.Index=function(){this._fields=[],this._ref="id",this.pipeline=new t.Pipeline,this.documentStore=new t.DocumentStore,this.index={},this.eventEmitter=new t.EventEmitter,this._idfCache={},this.on("add","remove","update",function(){this._idfCache={}}.bind(this))},t.Index.prototype.on=function(){var e=Array.prototype.slice.call(arguments);return this.eventEmitter.addListener.apply(this.eventEmitter,e)},t.Index.prototype.off=function(e,t){return this.eventEmitter.removeListener(e,t)},t.Index.load=function(e){e.version!==t.version&&t.utils.warn("version mismatch: current "+t.version+" importing "+e.version);var n=new this;n._fields=e.fields,n._ref=e.ref,n.documentStore=t.DocumentStore.load(e.documentStore),n.pipeline=t.Pipeline.load(e.pipeline),n.index={};for(var i in e.index)n.index[i]=t.InvertedIndex.load(e.index[i]);return n},t.Index.prototype.addField=function(e){return this._fields.push(e),this.index[e]=new t.InvertedIndex,this},t.Index.prototype.setRef=function(e){return this._ref=e,this},t.Index.prototype.saveDocument=function(e){return this.documentStore=new t.DocumentStore(e),this},t.Index.prototype.addDoc=function(e,n){if(e){var n=void 0===n?!0:n,i=e[this._ref];this.documentStore.addDoc(i,e),this._fields.forEach(function(n){var o=this.pipeline.run(t.tokenizer(e[n]));this.documentStore.addFieldLength(i,n,o.length);var r={};o.forEach(function(e){e in r?r[e]+=1:r[e]=1},this);for(var s in r){var u=r[s];u=Math.sqrt(u),this.index[n].addToken(s,{ref:i,tf:u})}},this),n&&this.eventEmitter.emit("add",e,this)}},t.Index.prototype.removeDocByRef=function(e){if(e&&this.documentStore.isDocStored()!==!1&&this.documentStore.hasDoc(e)){var t=this.documentStore.getDoc(e);this.removeDoc(t,!1)}},t.Index.prototype.removeDoc=function(e,n){if(e){var n=void 0===n?!0:n,i=e[this._ref];this.documentStore.hasDoc(i)&&(this.documentStore.removeDoc(i),this._fields.forEach(function(n){var o=this.pipeline.run(t.tokenizer(e[n]));o.forEach(function(e){this.index[n].removeToken(e,i)},this)},this),n&&this.eventEmitter.emit("remove",e,this))}},t.Index.prototype.updateDoc=function(e,t){var t=void 0===t?!0:t;this.removeDocByRef(e[this._ref],!1),this.addDoc(e,!1),t&&this.eventEmitter.emit("update",e,this)},t.Index.prototype.idf=function(e,t){var n="@"+t+"/"+e;if(Object.prototype.hasOwnProperty.call(this._idfCache,n))return this._idfCache[n];var i=this.index[t].getDocFreq(e),o=1+Math.log(this.documentStore.length/(i+1));return this._idfCache[n]=o,o},t.Index.prototype.getFields=function(){return this._fields.slice()},t.Index.prototype.search=function(e,n){if(!e)return[];e="string"==typeof e?{any:e}:JSON.parse(JSON.stringify(e));var i=null;null!=n&&(i=JSON.stringify(n));for(var o=new t.Configuration(i,this.getFields()).get(),r={},s=Object.keys(e),u=0;u<s.length;u++){var a=s[u];r[a]=this.pipeline.run(t.tokenizer(e[a]))}var l={};for(var c in o){var d=r[c]||r.any;if(d){var f=this.fieldSearch(d,c,o),h=o[c].boost;for(var p in f)f[p]=f[p]*h;for(var p in f)p in l?l[p]+=f[p]:l[p]=f[p]}}var v,g=[];for(var p in l)v={ref:p,score:l[p]},this.documentStore.hasDoc(p)&&(v.doc=this.documentStore.getDoc(p)),g.push(v);return g.sort(function(e,t){return t.score-e.score}),g},t.Index.prototype.fieldSearch=function(e,t,n){var i=n[t].bool,o=n[t].expand,r=n[t].boost,s=null,u={};return 0!==r?(e.forEach(function(e){var n=[e];1==o&&(n=this.index[t].expandToken(e));var r={};n.forEach(function(n){var o=this.index[t].getDocs(n),a=this.idf(n,t);if(s&&"AND"==i){var l={};for(var c in s)c in o&&(l[c]=o[c]);o=l}n==e&&this.fieldSearchStats(u,n,o);for(var c in o){var d=this.index[t].getTermFrequency(n,c),f=this.documentStore.getFieldLength(c,t),h=1;0!=f&&(h=1/Math.sqrt(f));var p=1;n!=e&&(p=.15*(1-(n.length-e.length)/n.length));var v=d*a*h*p;c in r?r[c]+=v:r[c]=v}},this),s=this.mergeScores(s,r,i)},this),s=this.coordNorm(s,u,e.length)):void 0},t.Index.prototype.mergeScores=function(e,t,n){if(!e)return t;if("AND"==n){var i={};for(var o in t)o in e&&(i[o]=e[o]+t[o]);return i}for(var o in t)o in e?e[o]+=t[o]:e[o]=t[o];return e},t.Index.prototype.fieldSearchStats=function(e,t,n){for(var i in n)i in e?e[i].push(t):e[i]=[t]},t.Index.prototype.coordNorm=function(e,t,n){for(var i in e)if(i in t){var o=t[i].length;e[i]=e[i]*o/n}return e},t.Index.prototype.toJSON=function(){var e={};return this._fields.forEach(function(t){e[t]=this.index[t].toJSON()},this),{version:t.version,fields:this._fields,ref:this._ref,documentStore:this.documentStore.toJSON(),index:e,pipeline:this.pipeline.toJSON()}},t.Index.prototype.use=function(e){var t=Array.prototype.slice.call(arguments,1);t.unshift(this),e.apply(this,t)},t.DocumentStore=function(e){this._save=null===e||void 0===e?!0:e,this.docs={},this.docInfo={},this.length=0},t.DocumentStore.load=function(e){var t=new this;return t.length=e.length,t.docs=e.docs,t.docInfo=e.docInfo,t._save=e.save,t},t.DocumentStore.prototype.isDocStored=function(){return this._save},t.DocumentStore.prototype.addDoc=function(t,n){this.hasDoc(t)||this.length++,this.docs[t]=this._save===!0?e(n):null},t.DocumentStore.prototype.getDoc=function(e){return this.hasDoc(e)===!1?null:this.docs[e]},t.DocumentStore.prototype.hasDoc=function(e){return e in this.docs},t.DocumentStore.prototype.removeDoc=function(e){this.hasDoc(e)&&(delete this.docs[e],delete this.docInfo[e],this.length--)},t.DocumentStore.prototype.addFieldLength=function(e,t,n){null!==e&&void 0!==e&&0!=this.hasDoc(e)&&(this.docInfo[e]||(this.docInfo[e]={}),this.docInfo[e][t]=n)},t.DocumentStore.prototype.updateFieldLength=function(e,t,n){null!==e&&void 0!==e&&0!=this.hasDoc(e)&&this.addFieldLength(e,t,n)},t.DocumentStore.prototype.getFieldLength=function(e,t){return null===e||void 0===e?0:e in this.docs&&t in this.docInfo[e]?this.docInfo[e][t]:0},t.DocumentStore.prototype.toJSON=function(){return{docs:this.docs,docInfo:this.docInfo,length:this.length,save:this._save}},t.stemmer=function(){var e={ational:"ate",tional:"tion",enci:"ence",anci:"ance",izer:"ize",bli:"ble",alli:"al",entli:"ent",eli:"e",ousli:"ous",ization:"ize",ation:"ate",ator:"ate",alism:"al",iveness:"ive",fulness:"ful",ousness:"ous",aliti:"al",iviti:"ive",biliti:"ble",logi:"log"},t={icate:"ic",ative:"",alize:"al",iciti:"ic",ical:"ic",ful:"",ness:""},n="[^aeiou]",i="[aeiouy]",o=n+"[^aeiouy]*",r=i+"[aeiou]*",s="^("+o+")?"+r+o,u="^("+o+")?"+r+o+"("+r+")?$",a="^("+o+")?"+r+o+r+o,l="^("+o+")?"+i,c=new RegExp(s),d=new RegExp(a),f=new RegExp(u),h=new RegExp(l),p=/^(.+?)(ss|i)es$/,v=/^(.+?)([^s])s$/,g=/^(.+?)eed$/,m=/^(.+?)(ed|ing)$/,y=/.$/,S=/(at|bl|iz)$/,x=new RegExp("([^aeiouylsz])\\1$"),w=new RegExp("^"+o+i+"[^aeiouwxy]$"),I=/^(.+?[^aeiou])y$/,b=/^(.+?)(ational|tional|enci|anci|izer|bli|alli|entli|eli|ousli|ization|ation|ator|alism|iveness|fulness|ousness|aliti|iviti|biliti|logi)$/,E=/^(.+?)(icate|ative|alize|iciti|ical|ful|ness)$/,D=/^(.+?)(al|ance|ence|er|ic|able|ible|ant|ement|ment|ent|ou|ism|ate|iti|ous|ive|ize)$/,F=/^(.+?)(s|t)(ion)$/,_=/^(.+?)e$/,P=/ll$/,k=new RegExp("^"+o+i+"[^aeiouwxy]$"),z=function(n){var i,o,r,s,u,a,l;if(n.length<3)return n;if(r=n.substr(0,1),"y"==r&&(n=r.toUpperCase()+n.substr(1)),s=p,u=v,s.test(n)?n=n.replace(s,"$1$2"):u.test(n)&&(n=n.replace(u,"$1$2")),s=g,u=m,s.test(n)){var z=s.exec(n);s=c,s.test(z[1])&&(s=y,n=n.replace(s,""))}else if(u.test(n)){var z=u.exec(n);i=z[1],u=h,u.test(i)&&(n=i,u=S,a=x,l=w,u.test(n)?n+="e":a.test(n)?(s=y,n=n.replace(s,"")):l.test(n)&&(n+="e"))}if(s=I,s.test(n)){var z=s.exec(n);i=z[1],n=i+"i"}if(s=b,s.test(n)){var z=s.exec(n);i=z[1],o=z[2],s=c,s.test(i)&&(n=i+e[o])}if(s=E,s.test(n)){var z=s.exec(n);i=z[1],o=z[2],s=c,s.test(i)&&(n=i+t[o])}if(s=D,u=F,s.test(n)){var z=s.exec(n);i=z[1],s=d,s.test(i)&&(n=i)}else if(u.test(n)){var z=u.exec(n);i=z[1]+z[2],u=d,u.test(i)&&(n=i)}if(s=_,s.test(n)){var z=s.exec(n);i=z[1],s=d,u=f,a=k,(s.test(i)||u.test(i)&&!a.test(i))&&(n=i)}return s=P,u=d,s.test(n)&&u.test(n)&&(s=y,n=n.replace(s,"")),"y"==r&&(n=r.toLowerCase()+n.substr(1)),n};return z}(),t.Pipeline.registerFunction(t.stemmer,"stemmer"),t.stopWordFilter=function(e){return e&&t.stopWordFilter.stopWords[e]!==!0?e:void 0},t.clearStopWords=function(){t.stopWordFilter.stopWords={}},t.addStopWords=function(e){null!=e&&Array.isArray(e)!==!1&&e.forEach(function(e){t.stopWordFilter.stopWords[e]=!0},this)},t.resetStopWords=function(){t.stopWordFilter.stopWords=t.defaultStopWords},t.defaultStopWords={"":!0,a:!0,able:!0,about:!0,across:!0,after:!0,all:!0,almost:!0,also:!0,am:!0,among:!0,an:!0,and:!0,any:!0,are:!0,as:!0,at:!0,be:!0,because:!0,been:!0,but:!0,by:!0,can:!0,cannot:!0,could:!0,dear:!0,did:!0,"do":!0,does:!0,either:!0,"else":!0,ever:!0,every:!0,"for":!0,from:!0,get:!0,got:!0,had:!0,has:!0,have:!0,he:!0,her:!0,hers:!0,him:!0,his:!0,how:!0,however:!0,i:!0,"if":!0,"in":!0,into:!0,is:!0,it:!0,its:!0,just:!0,least:!0,let:!0,like:!0,likely:!0,may:!0,me:!0,might:!0,most:!0,must:!0,my:!0,neither:!0,no:!0,nor:!0,not:!0,of:!0,off:!0,often:!0,on:!0,only:!0,or:!0,other:!0,our:!0,own:!0,rather:!0,said:!0,say:!0,says:!0,she:!0,should:!0,since:!0,so:!0,some:!0,than:!0,that:!0,the:!0,their:!0,them:!0,then:!0,there:!0,these:!0,they:!0,"this":!0,tis:!0,to:!0,too:!0,twas:!0,us:!0,wants:!0,was:!0,we:!0,were:!0,what:!0,when:!0,where:!0,which:!0,"while":!0,who:!0,whom:!0,why:!0,will:!0,"with":!0,would:!0,yet:!0,you:!0,your:!0},t.stopWordFilter.stopWords=t.defaultStopWords,t.Pipeline.registerFunction(t.stopWordFilter,"stopWordFilter"),t.trimmer=function(e){if(null===e||void 0===e)throw new Error("token should not be undefined");return e.replace(/^\W+/,"").replace(/\W+$/,"")},t.Pipeline.registerFunction(t.trimmer,"trimmer"),t.InvertedIndex=function(){this.root={docs:{},df:0}},t.InvertedIndex.load=function(e){var t=new this;return t.root=e.root,t},t.InvertedIndex.prototype.addToken=function(e,t,n){for(var n=n||this.root,i=0;i<=e.length-1;){var o=e[i];o in n||(n[o]={docs:{},df:0}),i+=1,n=n[o]}var r=t.ref;n.docs[r]?n.docs[r]={tf:t.tf}:(n.docs[r]={tf:t.tf},n.df+=1)},t.InvertedIndex.prototype.hasToken=function(e){if(!e)return!1;for(var t=this.root,n=0;n<e.length;n++){if(!t[e[n]])return!1;t=t[e[n]]}return!0},t.InvertedIndex.prototype.getNode=function(e){if(!e)return null;for(var t=this.root,n=0;n<e.length;n++){if(!t[e[n]])return null;t=t[e[n]]}return t},t.InvertedIndex.prototype.getDocs=function(e){var t=this.getNode(e);return null==t?{}:t.docs},t.InvertedIndex.prototype.getTermFrequency=function(e,t){var n=this.getNode(e);return null==n?0:t in n.docs?n.docs[t].tf:0},t.InvertedIndex.prototype.getDocFreq=function(e){var t=this.getNode(e);return null==t?0:t.df},t.InvertedIndex.prototype.removeToken=function(e,t){if(e){var n=this.getNode(e);null!=n&&t in n.docs&&(delete n.docs[t],n.df-=1)}},t.InvertedIndex.prototype.expandToken=function(e,t,n){if(null==e||""==e)return[];var t=t||[];if(void 0==n&&(n=this.getNode(e),null==n))return t;n.df>0&&t.push(e);for(var i in n)"docs"!==i&&"df"!==i&&this.expandToken(e+i,t,n[i]);return t},t.InvertedIndex.prototype.toJSON=function(){return{root:this.root}},t.Configuration=function(e,n){var e=e||"";if(void 0==n||null==n)throw new Error("fields should not be null");this.config={};var i;try{i=JSON.parse(e),this.buildUserConfig(i,n)}catch(o){t.utils.warn("user configuration parse failed, will use default configuration"),this.buildDefaultConfig(n)}},t.Configuration.prototype.buildDefaultConfig=function(e){this.reset(),e.forEach(function(e){this.config[e]={boost:1,bool:"OR",expand:!1}},this)},t.Configuration.prototype.buildUserConfig=function(e,n){var i="OR",o=!1;if(this.reset(),"bool"in e&&(i=e.bool||i),"expand"in e&&(o=e.expand||o),"fields"in e)for(var r in e.fields)if(n.indexOf(r)>-1){var s=e.fields[r],u=o;void 0!=s.expand&&(u=s.expand),this.config[r]={boost:s.boost||0===s.boost?s.boost:1,bool:s.bool||i,expand:u}}else t.utils.warn("field name in user configuration not found in index instance fields");else this.addAllFields2UserConfig(i,o,n)},t.Configuration.prototype.addAllFields2UserConfig=function(e,t,n){n.forEach(function(n){this.config[n]={boost:1,bool:e,expand:t}},this)},t.Configuration.prototype.get=function(){return this.config},t.Configuration.prototype.reset=function(){this.config={}},lunr.SortedSet=function(){this.length=0,this.elements=[]},lunr.SortedSet.load=function(e){var t=new this;return t.elements=e,t.length=e.length,t},lunr.SortedSet.prototype.add=function(){var e,t;for(e=0;e<arguments.length;e++)t=arguments[e],~this.indexOf(t)||this.elements.splice(this.locationFor(t),0,t);this.length=this.elements.length},lunr.SortedSet.prototype.toArray=function(){return this.elements.slice()},lunr.SortedSet.prototype.map=function(e,t){return this.elements.map(e,t)},lunr.SortedSet.prototype.forEach=function(e,t){return this.elements.forEach(e,t)},lunr.SortedSet.prototype.indexOf=function(e){for(var t=0,n=this.elements.length,i=n-t,o=t+Math.floor(i/2),r=this.elements[o];i>1;){if(r===e)return o;e>r&&(t=o),r>e&&(n=o),i=n-t,o=t+Math.floor(i/2),r=this.elements[o]}return r===e?o:-1},lunr.SortedSet.prototype.locationFor=function(e){for(var t=0,n=this.elements.length,i=n-t,o=t+Math.floor(i/2),r=this.elements[o];i>1;)e>r&&(t=o),r>e&&(n=o),i=n-t,o=t+Math.floor(i/2),r=this.elements[o];return r>e?o:e>r?o+1:void 0},lunr.SortedSet.prototype.intersect=function(e){for(var t=new lunr.SortedSet,n=0,i=0,o=this.length,r=e.length,s=this.elements,u=e.elements;;){if(n>o-1||i>r-1)break;s[n]!==u[i]?s[n]<u[i]?n++:s[n]>u[i]&&i++:(t.add(s[n]),n++,i++)}return t},lunr.SortedSet.prototype.clone=function(){var e=new lunr.SortedSet;return e.elements=this.toArray(),e.length=e.elements.length,e},lunr.SortedSet.prototype.union=function(e){var t,n,i;this.length>=e.length?(t=this,n=e):(t=e,n=this),i=t.clone();for(var o=0,r=n.toArray();o<r.length;o++)i.add(r[o]);return i},lunr.SortedSet.prototype.toJSON=function(){return this.toArray()},function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.elasticlunr=t()}(this,function(){return t})}();
    /** pdoc search index */const docs = {"version": "0.9.5", "fields": ["qualname", "fullname", "annotation", "default_value", "signature", "bases", "doc"], "ref": "fullname", "documentStore": {"docs": {"core": {"fullname": "core", "modulename": "core", "kind": "module", "doc": "<p></p>\n"}, "core.hdmi_config": {"fullname": "core.hdmi_config", "modulename": "core.hdmi_config", "kind": "module", "doc": "<p>\u6811\u8393\u6d3eHDMI\u914d\u7f6e\u4f18\u5316\u811a\u672c</p>\n\n<p>\u8fd9\u662f\u4e00\u4e2a\u4e13\u95e8\u7528\u4e8e\u4f18\u5316\u6811\u8393\u6d3eHDMI\u8f93\u51fa\u7684Python\u811a\u672c\u3002\n\u81ea\u52a8\u4fee\u6539 /boot/config.txt \u914d\u7f6e\u6587\u4ef6\uff0c\u5b9e\u73b0\u6700\u4f73\u7684\u663e\u793a\u6548\u679c\u3002</p>\n\n<p>\u4e3b\u8981\u529f\u80fd\uff1a</p>\n\n<ul>\n<li>\u5f3a\u5236HDMI\u8f93\u51fa\u4e3a1080p@60Hz</li>\n<li>\u7981\u7528\u8fc7\u626b\u63cf\uff08overscan\uff09\u5b9e\u73b0\u5168\u5c4f\u663e\u793a</li>\n<li>\u589e\u52a0GPU\u663e\u5b58\u81f3256MB\u63d0\u5347\u6027\u80fd</li>\n<li>\u81ea\u52a8\u5907\u4efd\u548c\u6062\u590d\u914d\u7f6e\u6587\u4ef6</li>\n<li>\u652f\u6301\u9884\u89c8\u6a21\u5f0f\u548c\u56de\u6eda\u64cd\u4f5c</li>\n<li>\u5b8c\u6574\u7684\u914d\u7f6e\u9a8c\u8bc1\u548c\u9519\u8bef\u5904\u7406</li>\n</ul>\n\n<p>\u914d\u7f6e\u9879\u8bf4\u660e\uff1a</p>\n\n<ul>\n<li>hdmi_group=1: HDMI\u7ec41\uff08CEA\u6807\u51c6\uff09</li>\n<li>hdmi_mode=16: 1080p@60Hz\u5206\u8fa8\u7387</li>\n<li>hdmi_force_hotplug=1: \u5f3a\u5236HDMI\u70ed\u63d2\u62d4\u68c0\u6d4b</li>\n<li>hdmi_drive=2: HDMI\u9a71\u52a8\u5f3a\u5ea6</li>\n<li>disable_overscan=1: \u7981\u7528\u8fc7\u626b\u63cf</li>\n<li>gpu_mem=256: GPU\u663e\u5b58256MB</li>\n</ul>\n\n<p>\u7cfb\u7edf\u8981\u6c42\uff1a</p>\n\n<ul>\n<li>\u6811\u8393\u6d3e\u7cfb\u7edf</li>\n<li>Python 3.7+</li>\n<li>sudo\u6743\u9650\uff08\u7528\u4e8e\u4fee\u6539/boot/config.txt\uff09</li>\n</ul>\n\n<p>\u4f5c\u8005: AI Assistant\n\u7248\u672c: 2.0.0\n\u8bb8\u53ef\u8bc1: MIT</p>\n"}, "core.hdmi_config.logger": {"fullname": "core.hdmi_config.logger", "modulename": "core.hdmi_config", "qualname": "logger", "kind": "variable", "doc": "<p></p>\n", "default_value": "&lt;Logger hdmi_config (INFO)&gt;"}, "core.hdmi_config.HDMIConfigurator": {"fullname": "core.hdmi_config.HDMIConfigurator", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator", "kind": "class", "doc": "<p>HDMI\u914d\u7f6e\u5668\u7c7b</p>\n\n<p>\u63d0\u4f9b\u5b8c\u6574\u7684\u6811\u8393\u6d3eHDMI\u914d\u7f6e\u4f18\u5316\u529f\u80fd\uff0c\u5305\u62ec\uff1a</p>\n\n<ul>\n<li>\u914d\u7f6e\u6587\u4ef6\u8bfb\u53d6\u548c\u89e3\u6790</li>\n<li>\u81ea\u52a8\u5907\u4efd\u548c\u6062\u590d</li>\n<li>HDMI\u53c2\u6570\u4f18\u5316</li>\n<li>\u914d\u7f6e\u9a8c\u8bc1\u548c\u9884\u89c8</li>\n<li>\u9519\u8bef\u5904\u7406\u548c\u56de\u6eda</li>\n</ul>\n\n<p>\u5c5e\u6027:\n    config_path (Path): \u914d\u7f6e\u6587\u4ef6\u8def\u5f84\n    backup_path (Path): \u5907\u4efd\u6587\u4ef6\u8def\u5f84\n    hdmi_configs (Dict): HDMI\u914d\u7f6e\u9879\u5b57\u5178</p>\n"}, "core.hdmi_config.HDMIConfigurator.__init__": {"fullname": "core.hdmi_config.HDMIConfigurator.__init__", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.__init__", "kind": "function", "doc": "<p>\u521d\u59cb\u5316HDMI\u914d\u7f6e\u5668</p>\n\n<p>Args:\n    config_path (str): \u914d\u7f6e\u6587\u4ef6\u8def\u5f84</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"n\">config_path</span><span class=\"p\">:</span> <span class=\"nb\">str</span> <span class=\"o\">=</span> <span class=\"s1\">&#39;/boot/config.txt&#39;</span></span>)</span>"}, "core.hdmi_config.HDMIConfigurator.config_path": {"fullname": "core.hdmi_config.HDMIConfigurator.config_path", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.config_path", "kind": "variable", "doc": "<p></p>\n"}, "core.hdmi_config.HDMIConfigurator.backup_path": {"fullname": "core.hdmi_config.HDMIConfigurator.backup_path", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.backup_path", "kind": "variable", "doc": "<p></p>\n"}, "core.hdmi_config.HDMIConfigurator.hdmi_configs": {"fullname": "core.hdmi_config.HDMIConfigurator.hdmi_configs", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.hdmi_configs", "kind": "variable", "doc": "<p></p>\n"}, "core.hdmi_config.HDMIConfigurator.check_permissions": {"fullname": "core.hdmi_config.HDMIConfigurator.check_permissions", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.check_permissions", "kind": "function", "doc": "<p>\u68c0\u67e5\u6587\u4ef6\u6743\u9650</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span></span><span class=\"return-annotation\">) -> <span class=\"nb\">bool</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.HDMIConfigurator.backup_config": {"fullname": "core.hdmi_config.HDMIConfigurator.backup_config", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.backup_config", "kind": "function", "doc": "<p>\u5907\u4efd\u539f\u59cb\u914d\u7f6e\u6587\u4ef6</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span></span><span class=\"return-annotation\">) -> <span class=\"nb\">bool</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.HDMIConfigurator.read_config": {"fullname": "core.hdmi_config.HDMIConfigurator.read_config", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.read_config", "kind": "function", "doc": "<p>\u8bfb\u53d6\u914d\u7f6e\u6587\u4ef6</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span></span><span class=\"return-annotation\">) -> <span class=\"n\">List</span><span class=\"p\">[</span><span class=\"nb\">str</span><span class=\"p\">]</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.HDMIConfigurator.write_config": {"fullname": "core.hdmi_config.HDMIConfigurator.write_config", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.write_config", "kind": "function", "doc": "<p>\u5199\u5165\u914d\u7f6e\u6587\u4ef6</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span>, </span><span class=\"param\"><span class=\"n\">lines</span><span class=\"p\">:</span> <span class=\"n\">List</span><span class=\"p\">[</span><span class=\"nb\">str</span><span class=\"p\">]</span></span><span class=\"return-annotation\">) -> <span class=\"nb\">bool</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.HDMIConfigurator.parse_config_line": {"fullname": "core.hdmi_config.HDMIConfigurator.parse_config_line", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.parse_config_line", "kind": "function", "doc": "<p>\u89e3\u6790\u914d\u7f6e\u884c</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span>, </span><span class=\"param\"><span class=\"n\">line</span><span class=\"p\">:</span> <span class=\"nb\">str</span></span><span class=\"return-annotation\">) -> <span class=\"n\">Optional</span><span class=\"p\">[</span><span class=\"n\">Dict</span><span class=\"p\">[</span><span class=\"nb\">str</span><span class=\"p\">,</span> <span class=\"nb\">str</span><span class=\"p\">]]</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"fullname": "core.hdmi_config.HDMIConfigurator.find_config_line", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.find_config_line", "kind": "function", "doc": "<p>\u67e5\u627e\u914d\u7f6e\u9879\u7684\u884c\u53f7</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span>, </span><span class=\"param\"><span class=\"n\">lines</span><span class=\"p\">:</span> <span class=\"n\">List</span><span class=\"p\">[</span><span class=\"nb\">str</span><span class=\"p\">]</span>, </span><span class=\"param\"><span class=\"n\">key</span><span class=\"p\">:</span> <span class=\"nb\">str</span></span><span class=\"return-annotation\">) -> <span class=\"n\">Optional</span><span class=\"p\">[</span><span class=\"nb\">int</span><span class=\"p\">]</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.HDMIConfigurator.update_config": {"fullname": "core.hdmi_config.HDMIConfigurator.update_config", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.update_config", "kind": "function", "doc": "<p>\u66f4\u65b0\u914d\u7f6e\u9879</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span>, </span><span class=\"param\"><span class=\"n\">lines</span><span class=\"p\">:</span> <span class=\"n\">List</span><span class=\"p\">[</span><span class=\"nb\">str</span><span class=\"p\">]</span>, </span><span class=\"param\"><span class=\"n\">key</span><span class=\"p\">:</span> <span class=\"nb\">str</span>, </span><span class=\"param\"><span class=\"n\">value</span><span class=\"p\">:</span> <span class=\"nb\">str</span></span><span class=\"return-annotation\">) -> <span class=\"n\">List</span><span class=\"p\">[</span><span class=\"nb\">str</span><span class=\"p\">]</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"fullname": "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.apply_hdmi_configs", "kind": "function", "doc": "<p>\u5e94\u7528HDMI\u914d\u7f6e</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span></span><span class=\"return-annotation\">) -> <span class=\"nb\">bool</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"fullname": "core.hdmi_config.HDMIConfigurator.restore_backup", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.restore_backup", "kind": "function", "doc": "<p>\u6062\u590d\u5907\u4efd\u914d\u7f6e</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span></span><span class=\"return-annotation\">) -> <span class=\"nb\">bool</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.HDMIConfigurator.show_current_config": {"fullname": "core.hdmi_config.HDMIConfigurator.show_current_config", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.show_current_config", "kind": "function", "doc": "<p>\u663e\u793a\u5f53\u524d\u914d\u7f6e</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span></span><span class=\"return-annotation\">) -> <span class=\"kc\">None</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.HDMIConfigurator.show_changes": {"fullname": "core.hdmi_config.HDMIConfigurator.show_changes", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.show_changes", "kind": "function", "doc": "<p>\u663e\u793a\u5c06\u8981\u5e94\u7528\u7684\u66f4\u6539</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span></span><span class=\"return-annotation\">) -> <span class=\"kc\">None</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.HDMIConfigurator.validate_config": {"fullname": "core.hdmi_config.HDMIConfigurator.validate_config", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.validate_config", "kind": "function", "doc": "<p>\u9a8c\u8bc1\u914d\u7f6e</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span></span><span class=\"return-annotation\">) -> <span class=\"nb\">bool</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.HDMIConfigurator.run": {"fullname": "core.hdmi_config.HDMIConfigurator.run", "modulename": "core.hdmi_config", "qualname": "HDMIConfigurator.run", "kind": "function", "doc": "<p>\u8fd0\u884c\u914d\u7f6e\u7a0b\u5e8f</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"param\"><span class=\"bp\">self</span>, </span><span class=\"param\"><span class=\"n\">dry_run</span><span class=\"p\">:</span> <span class=\"nb\">bool</span> <span class=\"o\">=</span> <span class=\"kc\">False</span>, </span><span class=\"param\"><span class=\"n\">restore</span><span class=\"p\">:</span> <span class=\"nb\">bool</span> <span class=\"o\">=</span> <span class=\"kc\">False</span></span><span class=\"return-annotation\">) -> <span class=\"nb\">bool</span>:</span></span>", "funcdef": "def"}, "core.hdmi_config.main": {"fullname": "core.hdmi_config.main", "modulename": "core.hdmi_config", "qualname": "main", "kind": "function", "doc": "<p>\u4e3b\u51fd\u6570</p>\n", "signature": "<span class=\"signature pdoc-code condensed\">(<span class=\"return-annotation\">):</span></span>", "funcdef": "def"}}, "docInfo": {"core": {"qualname": 0, "fullname": 1, "annotation": 0, "default_value": 0, "signature": 0, "bases": 0, "doc": 3}, "core.hdmi_config": {"qualname": 0, "fullname": 3, "annotation": 0, "default_value": 0, "signature": 0, "bases": 0, "doc": 109}, "core.hdmi_config.logger": {"qualname": 1, "fullname": 4, "annotation": 0, "default_value": 8, "signature": 0, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator": {"qualname": 1, "fullname": 4, "annotation": 0, "default_value": 0, "signature": 0, "bases": 0, "doc": 43}, "core.hdmi_config.HDMIConfigurator.__init__": {"qualname": 3, "fullname": 6, "annotation": 0, "default_value": 0, "signature": 27, "bases": 0, "doc": 11}, "core.hdmi_config.HDMIConfigurator.config_path": {"qualname": 3, "fullname": 6, "annotation": 0, "default_value": 0, "signature": 0, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.backup_path": {"qualname": 3, "fullname": 6, "annotation": 0, "default_value": 0, "signature": 0, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.hdmi_configs": {"qualname": 3, "fullname": 6, "annotation": 0, "default_value": 0, "signature": 0, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.check_permissions": {"qualname": 3, "fullname": 6, "annotation": 0, "default_value": 0, "signature": 14, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.backup_config": {"qualname": 3, "fullname": 6, "annotation": 0, "default_value": 0, "signature": 14, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.read_config": {"qualname": 3, "fullname": 6, "annotation": 0, "default_value": 0, "signature": 20, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.write_config": {"qualname": 3, "fullname": 6, "annotation": 0, "default_value": 0, "signature": 30, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.parse_config_line": {"qualname": 4, "fullname": 7, "annotation": 0, "default_value": 0, "signature": 40, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"qualname": 4, "fullname": 7, "annotation": 0, "default_value": 0, "signature": 46, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.update_config": {"qualname": 3, "fullname": 6, "annotation": 0, "default_value": 0, "signature": 56, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"qualname": 4, "fullname": 7, "annotation": 0, "default_value": 0, "signature": 14, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"qualname": 3, "fullname": 6, "annotation": 0, "default_value": 0, "signature": 14, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.show_current_config": {"qualname": 4, "fullname": 7, "annotation": 0, "default_value": 0, "signature": 14, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.show_changes": {"qualname": 3, "fullname": 6, "annotation": 0, "default_value": 0, "signature": 14, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.validate_config": {"qualname": 3, "fullname": 6, "annotation": 0, "default_value": 0, "signature": 14, "bases": 0, "doc": 3}, "core.hdmi_config.HDMIConfigurator.run": {"qualname": 2, "fullname": 5, "annotation": 0, "default_value": 0, "signature": 49, "bases": 0, "doc": 3}, "core.hdmi_config.main": {"qualname": 1, "fullname": 4, "annotation": 0, "default_value": 0, "signature": 7, "bases": 0, "doc": 3}}, "length": 22, "save": true}, "index": {"qualname": {"root": {"docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}}, "df": 1, "l": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "g": {"docs": {}, "df": 0, "g": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "r": {"docs": {"core.hdmi_config.logger": {"tf": 1}}, "df": 1}}}}}, "i": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}}, "df": 2}}}}, "h": {"docs": {}, "df": 0, "d": {"docs": {}, "df": 0, "m": {"docs": {}, "df": 0, "i": {"docs": {"core.hdmi_config.HDMIConfigurator.hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1}}, "df": 2, "c": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "f": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "g": {"docs": {}, "df": 0, "u": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "r": {"docs": {"core.hdmi_config.HDMIConfigurator": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.config_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.read_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.validate_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.run": {"tf": 1}}, "df": 18}}}}}}}}}}}}}}}}, "i": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}}, "df": 1}}}}, "c": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "f": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "g": {"docs": {"core.hdmi_config.HDMIConfigurator.config_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.read_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.validate_config": {"tf": 1}}, "df": 9, "s": {"docs": {"core.hdmi_config.HDMIConfigurator.hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1}}, "df": 2}}}}}}, "h": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "c": {"docs": {}, "df": 0, "k": {"docs": {"core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 1}}, "df": 1}}}, "a": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "g": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "s": {"docs": {"core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 1}}, "df": 1}}}}}}, "u": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1}}, "df": 1}}}}}}}, "p": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "h": {"docs": {"core.hdmi_config.HDMIConfigurator.config_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_path": {"tf": 1}}, "df": 2}}, "r": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}}, "df": 1}}}}, "e": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "m": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "s": {"docs": {"core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 1}}, "df": 1}}}}}}}}}}}, "b": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "c": {"docs": {}, "df": 0, "k": {"docs": {}, "df": 0, "u": {"docs": {}, "df": 0, "p": {"docs": {"core.hdmi_config.HDMIConfigurator.backup_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 1}}, "df": 3}}}}}}, "r": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "d": {"docs": {"core.hdmi_config.HDMIConfigurator.read_config": {"tf": 1}}, "df": 1}}, "s": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 1}}, "df": 1}}}}}}, "u": {"docs": {}, "df": 0, "n": {"docs": {"core.hdmi_config.HDMIConfigurator.run": {"tf": 1}}, "df": 1}}}, "w": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1}}, "df": 1}}}}}, "f": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "d": {"docs": {"core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}}, "df": 1}}}}, "u": {"docs": {}, "df": 0, "p": {"docs": {}, "df": 0, "d": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1}}, "df": 1}}}}}}, "a": {"docs": {}, "df": 0, "p": {"docs": {}, "df": 0, "p": {"docs": {}, "df": 0, "l": {"docs": {}, "df": 0, "y": {"docs": {"core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1}}, "df": 1}}}}}, "s": {"docs": {}, "df": 0, "h": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "w": {"docs": {"core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 1}}, "df": 2}}}}, "v": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "l": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "d": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.validate_config": {"tf": 1}}, "df": 1}}}}}}}}, "m": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "n": {"docs": {"core.hdmi_config.main": {"tf": 1}}, "df": 1}}}}}}, "fullname": {"root": {"docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}}, "df": 1, "c": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "e": {"docs": {"core": {"tf": 1}, "core.hdmi_config": {"tf": 1}, "core.hdmi_config.logger": {"tf": 1}, "core.hdmi_config.HDMIConfigurator": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.config_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.read_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.validate_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.run": {"tf": 1}, "core.hdmi_config.main": {"tf": 1}}, "df": 22}}, "n": {"docs": {}, "df": 0, "f": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "g": {"docs": {"core.hdmi_config": {"tf": 1}, "core.hdmi_config.logger": {"tf": 1}, "core.hdmi_config.HDMIConfigurator": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.config_path": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.backup_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_config": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.read_config": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.validate_config": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.run": {"tf": 1}, "core.hdmi_config.main": {"tf": 1}}, "df": 21, "s": {"docs": {"core.hdmi_config.HDMIConfigurator.hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1}}, "df": 2}}}}}}, "h": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "c": {"docs": {}, "df": 0, "k": {"docs": {"core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 1}}, "df": 1}}}, "a": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "g": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "s": {"docs": {"core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 1}}, "df": 1}}}}}}, "u": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1}}, "df": 1}}}}}}}, "h": {"docs": {}, "df": 0, "d": {"docs": {}, "df": 0, "m": {"docs": {}, "df": 0, "i": {"docs": {"core.hdmi_config": {"tf": 1}, "core.hdmi_config.logger": {"tf": 1}, "core.hdmi_config.HDMIConfigurator": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.config_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.hdmi_configs": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.read_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.validate_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.run": {"tf": 1}, "core.hdmi_config.main": {"tf": 1}}, "df": 21, "c": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "f": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "g": {"docs": {}, "df": 0, "u": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "r": {"docs": {"core.hdmi_config.HDMIConfigurator": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.config_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.read_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.validate_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.run": {"tf": 1}}, "df": 18}}}}}}}}}}}}}}}}, "l": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "g": {"docs": {}, "df": 0, "g": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "r": {"docs": {"core.hdmi_config.logger": {"tf": 1}}, "df": 1}}}}}, "i": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}}, "df": 2}}}}, "i": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}}, "df": 1}}}}, "p": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "h": {"docs": {"core.hdmi_config.HDMIConfigurator.config_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_path": {"tf": 1}}, "df": 2}}, "r": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}}, "df": 1}}}}, "e": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "m": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "s": {"docs": {"core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 1}}, "df": 1}}}}}}}}}}}, "b": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "c": {"docs": {}, "df": 0, "k": {"docs": {}, "df": 0, "u": {"docs": {}, "df": 0, "p": {"docs": {"core.hdmi_config.HDMIConfigurator.backup_path": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 1}}, "df": 3}}}}}}, "r": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "d": {"docs": {"core.hdmi_config.HDMIConfigurator.read_config": {"tf": 1}}, "df": 1}}, "s": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 1}}, "df": 1}}}}}}, "u": {"docs": {}, "df": 0, "n": {"docs": {"core.hdmi_config.HDMIConfigurator.run": {"tf": 1}}, "df": 1}}}, "w": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1}}, "df": 1}}}}}, "f": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "d": {"docs": {"core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}}, "df": 1}}}}, "u": {"docs": {}, "df": 0, "p": {"docs": {}, "df": 0, "d": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1}}, "df": 1}}}}}}, "a": {"docs": {}, "df": 0, "p": {"docs": {}, "df": 0, "p": {"docs": {}, "df": 0, "l": {"docs": {}, "df": 0, "y": {"docs": {"core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1}}, "df": 1}}}}}, "s": {"docs": {}, "df": 0, "h": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "w": {"docs": {"core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 1}}, "df": 2}}}}, "v": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "l": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "d": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.validate_config": {"tf": 1}}, "df": 1}}}}}}}}, "m": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "n": {"docs": {"core.hdmi_config.main": {"tf": 1}}, "df": 1}}}}}}, "annotation": {"root": {"docs": {}, "df": 0}}, "default_value": {"root": {"docs": {"core.hdmi_config.logger": {"tf": 1.4142135623730951}}, "df": 1, "l": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config.logger": {"tf": 1}}, "df": 1}, "o": {"docs": {}, "df": 0, "g": {"docs": {}, "df": 0, "g": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "r": {"docs": {"core.hdmi_config.logger": {"tf": 1}}, "df": 1}}}}}}, "h": {"docs": {}, "df": 0, "d": {"docs": {}, "df": 0, "m": {"docs": {}, "df": 0, "i": {"docs": {"core.hdmi_config.logger": {"tf": 1}}, "df": 1}}}}, "c": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "f": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "g": {"docs": {"core.hdmi_config.logger": {"tf": 1}}, "df": 1}}}}}}, "i": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "f": {"docs": {}, "df": 0, "o": {"docs": {"core.hdmi_config.logger": {"tf": 1}}, "df": 1}}}}, "g": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config.logger": {"tf": 1}}, "df": 1}}}}, "signature": {"root": {"3": {"9": {"docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1.4142135623730951}}, "df": 1}, "docs": {}, "df": 0}, "docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 4.47213595499958}, "core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 3.4641016151377544}, "core.hdmi_config.HDMIConfigurator.backup_config": {"tf": 3.4641016151377544}, "core.hdmi_config.HDMIConfigurator.read_config": {"tf": 4.123105625617661}, "core.hdmi_config.HDMIConfigurator.write_config": {"tf": 5}, "core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 5.744562646538029}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 6.164414002968976}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 6.782329983125268}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 3.4641016151377544}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 3.4641016151377544}, "core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 3.4641016151377544}, "core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 3.4641016151377544}, "core.hdmi_config.HDMIConfigurator.validate_config": {"tf": 3.4641016151377544}, "core.hdmi_config.HDMIConfigurator.run": {"tf": 6.324555320336759}, "core.hdmi_config.main": {"tf": 2.6457513110645907}}, "df": 15, "c": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "f": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "g": {"docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}}, "df": 1}}}}}}, "p": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "h": {"docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}}, "df": 1}}}}, "s": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "r": {"docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.read_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 2}}, "df": 6}}, "e": {"docs": {}, "df": 0, "l": {"docs": {}, "df": 0, "f": {"docs": {"core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.read_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.validate_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.run": {"tf": 1}}, "df": 13}}}}, "b": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "/": {"docs": {}, "df": 0, "c": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "f": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "g": {"docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}}, "df": 1}}}}}}}}, "l": {"docs": {"core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.backup_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.validate_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.run": {"tf": 1.7320508075688772}}, "df": 7}}}}, "t": {"docs": {}, "df": 0, "x": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}}, "df": 1}}}, "l": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config.HDMIConfigurator.read_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1.4142135623730951}}, "df": 4}}, "n": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}}, "df": 1, "s": {"docs": {"core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1}}, "df": 3}}}}}, "o": {"docs": {}, "df": 0, "p": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "l": {"docs": {"core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}}, "df": 2}}}}}}}}, "d": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "c": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1}}, "df": 1}}}, "r": {"docs": {}, "df": 0, "y": {"docs": {"core.hdmi_config.HDMIConfigurator.run": {"tf": 1}}, "df": 1}}}, "k": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "y": {"docs": {"core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1}}, "df": 2}}}, "i": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1}}, "df": 1}}}, "v": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "l": {"docs": {}, "df": 0, "u": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1}}, "df": 1}}}}}, "n": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 1}}, "df": 2}}}}, "r": {"docs": {}, "df": 0, "u": {"docs": {}, "df": 0, "n": {"docs": {"core.hdmi_config.HDMIConfigurator.run": {"tf": 1}}, "df": 1}}, "e": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.run": {"tf": 1}}, "df": 1}}}}}}}, "f": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "l": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config.HDMIConfigurator.run": {"tf": 1.4142135623730951}}, "df": 1}}}}}}}, "bases": {"root": {"docs": {}, "df": 0}}, "doc": {"root": {"0": {"docs": {"core.hdmi_config": {"tf": 1.4142135623730951}}, "df": 1}, "1": {"0": {"8": {"0": {"docs": {}, "df": 0, "p": {"docs": {}, "df": 0, "@": {"6": {"0": {"docs": {}, "df": 0, "h": {"docs": {}, "df": 0, "z": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}, "docs": {}, "df": 0}, "docs": {}, "df": 0}}}, "docs": {}, "df": 0}, "docs": {}, "df": 0}, "6": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}, "docs": {"core.hdmi_config": {"tf": 1.7320508075688772}}, "df": 1}, "2": {"5": {"6": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}, "docs": {}, "df": 0}, "docs": {"core.hdmi_config": {"tf": 1.4142135623730951}}, "df": 1}, "3": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}, "7": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}, "docs": {"core": {"tf": 1.7320508075688772}, "core.hdmi_config": {"tf": 8.18535277187245}, "core.hdmi_config.logger": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator": {"tf": 5.477225575051661}, "core.hdmi_config.HDMIConfigurator.__init__": {"tf": 2.449489742783178}, "core.hdmi_config.HDMIConfigurator.config_path": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.backup_path": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.hdmi_configs": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.check_permissions": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.backup_config": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.read_config": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.write_config": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.parse_config_line": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.find_config_line": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.update_config": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1.4142135623730951}, "core.hdmi_config.HDMIConfigurator.restore_backup": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.show_current_config": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.show_changes": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.validate_config": {"tf": 1.7320508075688772}, "core.hdmi_config.HDMIConfigurator.run": {"tf": 1.7320508075688772}, "core.hdmi_config.main": {"tf": 1.7320508075688772}}, "df": 22, "h": {"docs": {}, "df": 0, "d": {"docs": {}, "df": 0, "m": {"docs": {}, "df": 0, "i": {"docs": {"core.hdmi_config": {"tf": 2.6457513110645907}, "core.hdmi_config.HDMIConfigurator": {"tf": 2.23606797749979}, "core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.apply_hdmi_configs": {"tf": 1}}, "df": 4, "\u8f93": {"docs": {}, "df": 0, "\u51fa": {"docs": {}, "df": 0, "\u7684": {"docs": {}, "df": 0, "p": {"docs": {}, "df": 0, "y": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "h": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}}}}}, "\u4e3a": {"1": {"0": {"8": {"0": {"docs": {}, "df": 0, "p": {"docs": {}, "df": 0, "@": {"6": {"0": {"docs": {}, "df": 0, "h": {"docs": {}, "df": 0, "z": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}, "docs": {}, "df": 0}, "docs": {}, "df": 0}}}, "docs": {}, "df": 0}, "docs": {}, "df": 0}, "docs": {}, "df": 0}, "docs": {}, "df": 0}}}, "\u7ec4": {"1": {"docs": {}, "df": 0, "\uff08": {"docs": {}, "df": 0, "c": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "a": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}}}, "docs": {}, "df": 0}}}}, "o": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "p": {"docs": {}, "df": 0, "l": {"docs": {}, "df": 0, "u": {"docs": {}, "df": 0, "g": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}}}}}, "b": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "/": {"docs": {}, "df": 0, "c": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "f": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "g": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}}}}}}}}, "a": {"docs": {}, "df": 0, "c": {"docs": {}, "df": 0, "k": {"docs": {}, "df": 0, "u": {"docs": {}, "df": 0, "p": {"docs": {"core.hdmi_config.HDMIConfigurator": {"tf": 1}}, "df": 1}}}}}}, "t": {"docs": {}, "df": 0, "x": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config": {"tf": 1.4142135623730951}}, "df": 1}}}, "o": {"docs": {}, "df": 0, "v": {"docs": {}, "df": 0, "e": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "c": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "n": {"docs": {"core.hdmi_config": {"tf": 1.4142135623730951}}, "df": 1}}}}}}}}, "g": {"docs": {}, "df": 0, "p": {"docs": {}, "df": 0, "u": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1, "\u663e": {"docs": {}, "df": 0, "\u5b58": {"2": {"5": {"6": {"docs": {}, "df": 0, "m": {"docs": {}, "df": 0, "b": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}, "docs": {}, "df": 0}, "docs": {}, "df": 0}, "docs": {}, "df": 0, "\u81f3": {"2": {"5": {"6": {"docs": {}, "df": 0, "m": {"docs": {}, "df": 0, "b": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}, "docs": {}, "df": 0}, "docs": {}, "df": 0}, "docs": {}, "df": 0}}}}}, "r": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "u": {"docs": {}, "df": 0, "p": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}}}, "m": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "d": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}, "e": {"docs": {}, "df": 0, "m": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}, "i": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}, "f": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "c": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}}}, "d": {"docs": {}, "df": 0, "r": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "v": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}}, "i": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "b": {"docs": {}, "df": 0, "l": {"docs": {}, "df": 0, "e": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}}}, "c": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config.HDMIConfigurator": {"tf": 1}}, "df": 1}}}}, "p": {"docs": {}, "df": 0, "y": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "h": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}}}, "a": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "h": {"docs": {"core.hdmi_config.HDMIConfigurator": {"tf": 2}, "core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}}, "df": 2}}}}, "s": {"docs": {}, "df": 0, "u": {"docs": {}, "df": 0, "d": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "\u6743": {"docs": {}, "df": 0, "\u9650": {"docs": {}, "df": 0, "\uff08": {"docs": {}, "df": 0, "\u7528": {"docs": {}, "df": 0, "\u4e8e": {"docs": {}, "df": 0, "\u4fee": {"docs": {}, "df": 0, "\u6539": {"docs": {}, "df": 0, "/": {"docs": {}, "df": 0, "b": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "/": {"docs": {}, "df": 0, "c": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "f": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "g": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}}}}}}}}}}}}}}}}}}}}, "t": {"docs": {}, "df": 0, "r": {"docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}}, "df": 1}}}, "a": {"docs": {}, "df": 0, "i": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}, "s": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "s": {"docs": {}, "df": 0, "t": {"docs": {}, "df": 0, "a": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "t": {"docs": {"core.hdmi_config": {"tf": 1}}, "df": 1}}}}}}}}, "r": {"docs": {}, "df": 0, "g": {"docs": {}, "df": 0, "s": {"docs": {"core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}}, "df": 1}}}}, "c": {"docs": {}, "df": 0, "o": {"docs": {}, "df": 0, "n": {"docs": {}, "df": 0, "f": {"docs": {}, "df": 0, "i": {"docs": {}, "df": 0, "g": {"docs": {"core.hdmi_config.HDMIConfigurator": {"tf": 1}, "core.hdmi_config.HDMIConfigurator.__init__": {"tf": 1}}, "df": 2, "s": {"docs": {"core.hdmi_config.HDMIConfigurator": {"tf": 1}}, "df": 1}}}}}}}}}}, "pipeline": ["trimmer"], "_isPrebuiltIndex": true};

    // mirrored in build-search-index.js (part 1)
    // Also split on html tags. this is a cheap heuristic, but good enough.
    elasticlunr.tokenizer.setSeperator(/[\s\-.;&_'"=,()]+|<[^>]*>/);

    let searchIndex;
    if (docs._isPrebuiltIndex) {
        console.info("using precompiled search index");
        searchIndex = elasticlunr.Index.load(docs);
    } else {
        console.time("building search index");
        // mirrored in build-search-index.js (part 2)
        searchIndex = elasticlunr(function () {
            this.pipeline.remove(elasticlunr.stemmer);
            this.pipeline.remove(elasticlunr.stopWordFilter);
            this.addField("qualname");
            this.addField("fullname");
            this.addField("annotation");
            this.addField("default_value");
            this.addField("signature");
            this.addField("bases");
            this.addField("doc");
            this.setRef("fullname");
        });
        for (let doc of docs) {
            searchIndex.addDoc(doc);
        }
        console.timeEnd("building search index");
    }

    return (term) => searchIndex.search(term, {
        fields: {
            qualname: {boost: 4},
            fullname: {boost: 2},
            annotation: {boost: 2},
            default_value: {boost: 2},
            signature: {boost: 2},
            bases: {boost: 2},
            doc: {boost: 1},
        },
        expand: true
    });
})();