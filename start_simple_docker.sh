#!/bin/bash

echo "🎮 GamePlayer-Raspberry 简化Docker启动器"
echo "========================================"

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker compose -f docker-compose.simple.yml down 2>/dev/null || true

# 构建并启动
echo "🔨 构建并启动容器..."
docker compose -f docker-compose.simple.yml up --build -d

# 等待启动
echo "⏳ 等待服务启动..."
sleep 10

# 显示状态
echo "📊 容器状态:"
docker compose -f docker-compose.simple.yml ps

echo ""
echo "🎉 启动完成！"
echo "🌐 Web界面: http://localhost:3020"
echo "🖥️ VNC连接: localhost:5900 (无密码)"
echo ""
echo "🔧 管理命令:"
echo "  查看日志: docker compose -f docker-compose.simple.yml logs -f"
echo "  停止服务: docker compose -f docker-compose.simple.yml down"
echo "  进入容器: docker exec -it gameplayer-simple bash"
