# GamePlayer-Raspberry 部署包

## 📦 包含内容

- **核心代码**: core/ 目录包含所有核心功能
- **脚本工具**: scripts/ 目录包含各种实用脚本
- **配置文件**: config/ 目录包含配置模板
- **测试套件**: tests/ 目录包含完整测试
- **镜像文件**: *.img.gz 预构建的树莓派镜像

## 🚀 快速开始

### 方式一：使用预构建镜像（推荐）

1. 使用 Raspberry Pi Imager 烧录镜像文件到SD卡
2. 插入树莓派启动
3. 系统会自动配置和启动

### 方式二：手动安装

1. 运行安装脚本：
   ```bash
   chmod +x install.sh
   ./install.sh
   ```

2. 按照提示完成安装

## 📋 系统要求

- Python 3.7+
- 树莓派 3B+ 或更新型号
- 16GB+ SD卡
- 网络连接

## 🔐 校验文件

使用以下命令验证镜像文件完整性：
```bash
sha256sum -c *.sha256
```

## 📞 技术支持

如有问题，请查看项目主页或提交Issue。

构建时间: Thu Jun 26 15:47:50 CST 2025
版本: 1.0.0
