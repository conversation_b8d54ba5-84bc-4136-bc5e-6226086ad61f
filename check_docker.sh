#!/bin/bash

# Docker状态检查和启动指导脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${PURPLE}"
echo "🐳 Docker状态检查器"
echo "===================="
echo -e "${NC}"

# 检查Docker是否安装
echo -e "${BLUE}1. 检查Docker安装状态...${NC}"
if command -v docker &> /dev/null; then
    DOCKER_VERSION=$(docker --version)
    echo -e "✅ Docker已安装: ${GREEN}$DOCKER_VERSION${NC}"
else
    echo -e "${RED}❌ Docker未安装${NC}"
    echo "请访问 https://www.docker.com/get-started 下载安装Docker"
    exit 1
fi

# 检查Docker守护进程状态
echo -e "${BLUE}2. 检查Docker守护进程状态...${NC}"
if docker info &> /dev/null; then
    echo -e "✅ Docker守护进程正在运行"
    
    # 显示Docker信息
    echo -e "${CYAN}Docker系统信息:${NC}"
    echo "  版本: $(docker version --format '{{.Server.Version}}' 2>/dev/null || echo '未知')"
    echo "  存储驱动: $(docker info --format '{{.Driver}}' 2>/dev/null || echo '未知')"
    echo "  容器数量: $(docker info --format '{{.Containers}}' 2>/dev/null || echo '0')"
    echo "  镜像数量: $(docker info --format '{{.Images}}' 2>/dev/null || echo '0')"
    
else
    echo -e "${RED}❌ Docker守护进程未运行${NC}"
    echo ""
    echo -e "${YELLOW}启动Docker的方法:${NC}"
    
    # 检测操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macOS系统:"
        echo "  1. 启动Docker Desktop应用程序"
        echo "  2. 或运行命令: open -a Docker"
        echo "  3. 等待菜单栏出现Docker图标"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "Linux系统:"
        echo "  sudo systemctl start docker"
        echo "  sudo systemctl enable docker  # 设置开机自启"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        echo "Windows系统:"
        echo "  启动Docker Desktop应用程序"
    fi
    
    echo ""
    echo "启动Docker后，重新运行此脚本进行检查"
    exit 1
fi

# 检查Docker Compose
echo -e "${BLUE}3. 检查Docker Compose状态...${NC}"
if docker compose version &> /dev/null; then
    COMPOSE_VERSION=$(docker compose version --short 2>/dev/null || echo "未知")
    echo -e "✅ Docker Compose可用: ${GREEN}$COMPOSE_VERSION${NC}"
elif command -v docker-compose &> /dev/null; then
    COMPOSE_VERSION=$(docker-compose --version 2>/dev/null || echo "未知")
    echo -e "✅ Docker Compose (旧版)可用: ${GREEN}$COMPOSE_VERSION${NC}"
else
    echo -e "${RED}❌ Docker Compose不可用${NC}"
    echo "请安装Docker Compose或更新Docker到最新版本"
fi

# 检查端口占用
echo -e "${BLUE}4. 检查端口占用状态...${NC}"
PORTS=(3020 5900 6080 8080 9000)
for port in "${PORTS[@]}"; do
    if lsof -i :$port &> /dev/null || netstat -an 2>/dev/null | grep ":$port " &> /dev/null; then
        echo -e "⚠️  端口 $port 已被占用"
    else
        echo -e "✅ 端口 $port 可用"
    fi
done

# 检查磁盘空间
echo -e "${BLUE}5. 检查磁盘空间...${NC}"
AVAILABLE_SPACE=$(df -h . | awk 'NR==2 {print $4}')
echo -e "✅ 当前目录可用空间: ${GREEN}$AVAILABLE_SPACE${NC}"

# 检查内存
echo -e "${BLUE}6. 检查系统内存...${NC}"
if [[ "$OSTYPE" == "darwin"* ]]; then
    TOTAL_MEM=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024/1024)"GB"}')
    echo -e "✅ 系统总内存: ${GREEN}$TOTAL_MEM${NC}"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    TOTAL_MEM=$(free -h | awk 'NR==2{print $2}')
    echo -e "✅ 系统总内存: ${GREEN}$TOTAL_MEM${NC}"
fi

# 检查现有GamePlayer容器
echo -e "${BLUE}7. 检查现有GamePlayer容器...${NC}"
EXISTING_CONTAINERS=$(docker ps -a --filter "name=gameplayer" --format "table {{.Names}}\t{{.Status}}" 2>/dev/null)
if [ -n "$EXISTING_CONTAINERS" ]; then
    echo -e "${YELLOW}发现现有容器:${NC}"
    echo "$EXISTING_CONTAINERS"
else
    echo -e "✅ 没有现有的GamePlayer容器"
fi

echo ""
echo -e "${GREEN}🎉 Docker环境检查完成！${NC}"
echo "===================="

# 提供下一步建议
if docker info &> /dev/null; then
    echo -e "${CYAN}🚀 可以开始启动GamePlayer-Raspberry:${NC}"
    echo ""
    echo "选择启动方式:"
    echo "  1. 完整图形化环境: ./start_docker_gui.sh"
    echo "  2. 简化环境: ./start_simple_docker.sh"
    echo ""
    echo "首次运行需要下载和构建镜像，请耐心等待..."
else
    echo -e "${YELLOW}⚠️ 请先启动Docker守护进程，然后重新运行此脚本${NC}"
fi
