<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="generator" content="pdoc 15.0.4"/>
    <title>core.hdmi_config API documentation</title>

    <style>/*! * Bootstrap Reboot v5.0.0 (https://getbootstrap.com/) * Copyright 2011-2021 The Bootstrap Authors * Copyright 2011-2021 Twitter, Inc. * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE) * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md) */*,::after,::before{box-sizing:border-box}@media (prefers-reduced-motion:no-preference){:root{scroll-behavior:smooth}}body{margin:0;font-family:system-ui,-apple-system,"Segoe UI",<PERSON><PERSON>,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans","Liberation Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-size:1rem;font-weight:400;line-height:1.5;color:#212529;background-color:#fff;-webkit-text-size-adjust:100%;-webkit-tap-highlight-color:transparent}hr{margin:1rem 0;color:inherit;background-color:currentColor;border:0;opacity:.25}hr:not([size]){height:1px}h1,h2,h3,h4,h5,h6{margin-top:0;margin-bottom:.5rem;font-weight:500;line-height:1.2}h1{font-size:calc(1.375rem + 1.5vw)}@media (min-width:1200px){h1{font-size:2.5rem}}h2{font-size:calc(1.325rem + .9vw)}@media (min-width:1200px){h2{font-size:2rem}}h3{font-size:calc(1.3rem + .6vw)}@media (min-width:1200px){h3{font-size:1.75rem}}h4{font-size:calc(1.275rem + .3vw)}@media (min-width:1200px){h4{font-size:1.5rem}}h5{font-size:1.25rem}h6{font-size:1rem}p{margin-top:0;margin-bottom:1rem}abbr[data-bs-original-title],abbr[title]{-webkit-text-decoration:underline dotted;text-decoration:underline dotted;cursor:help;-webkit-text-decoration-skip-ink:none;text-decoration-skip-ink:none}address{margin-bottom:1rem;font-style:normal;line-height:inherit}ol,ul{padding-left:2rem}dl,ol,ul{margin-top:0;margin-bottom:1rem}ol ol,ol ul,ul ol,ul ul{margin-bottom:0}dt{font-weight:700}dd{margin-bottom:.5rem;margin-left:0}blockquote{margin:0 0 1rem}b,strong{font-weight:bolder}small{font-size:.875em}mark{padding:.2em;background-color:#fcf8e3}sub,sup{position:relative;font-size:.75em;line-height:0;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}a{color:#0d6efd;text-decoration:underline}a:hover{color:#0a58ca}a:not([href]):not([class]),a:not([href]):not([class]):hover{color:inherit;text-decoration:none}code,kbd,pre,samp{font-family:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;font-size:1em;direction:ltr;unicode-bidi:bidi-override}pre{display:block;margin-top:0;margin-bottom:1rem;overflow:auto;font-size:.875em}pre code{font-size:inherit;color:inherit;word-break:normal}code{font-size:.875em;color:#d63384;word-wrap:break-word}a>code{color:inherit}kbd{padding:.2rem .4rem;font-size:.875em;color:#fff;background-color:#212529;border-radius:.2rem}kbd kbd{padding:0;font-size:1em;font-weight:700}figure{margin:0 0 1rem}img,svg{vertical-align:middle}table{caption-side:bottom;border-collapse:collapse}caption{padding-top:.5rem;padding-bottom:.5rem;color:#6c757d;text-align:left}th{text-align:inherit;text-align:-webkit-match-parent}tbody,td,tfoot,th,thead,tr{border-color:inherit;border-style:solid;border-width:0}label{display:inline-block}button{border-radius:0}button:focus:not(:focus-visible){outline:0}button,input,optgroup,select,textarea{margin:0;font-family:inherit;font-size:inherit;line-height:inherit}button,select{text-transform:none}[role=button]{cursor:pointer}select{word-wrap:normal}select:disabled{opacity:1}[list]::-webkit-calendar-picker-indicator{display:none}[type=button],[type=reset],[type=submit],button{-webkit-appearance:button}[type=button]:not(:disabled),[type=reset]:not(:disabled),[type=submit]:not(:disabled),button:not(:disabled){cursor:pointer}::-moz-focus-inner{padding:0;border-style:none}textarea{resize:vertical}fieldset{min-width:0;padding:0;margin:0;border:0}legend{float:left;width:100%;padding:0;margin-bottom:.5rem;font-size:calc(1.275rem + .3vw);line-height:inherit}@media (min-width:1200px){legend{font-size:1.5rem}}legend+*{clear:left}::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-fields-wrapper,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-text,::-webkit-datetime-edit-year-field{padding:0}::-webkit-inner-spin-button{height:auto}[type=search]{outline-offset:-2px;-webkit-appearance:textfield}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-color-swatch-wrapper{padding:0}::file-selector-button{font:inherit}::-webkit-file-upload-button{font:inherit;-webkit-appearance:button}output{display:inline-block}iframe{border:0}summary{display:list-item;cursor:pointer}progress{vertical-align:baseline}[hidden]{display:none!important}</style>
    <style>/*! syntax-highlighting.css */pre{line-height:125%;}span.linenos{color:inherit; background-color:transparent; padding-left:5px; padding-right:20px;}.pdoc-code .hll{background-color:#ffffcc}.pdoc-code{background:#f8f8f8;}.pdoc-code .c{color:#3D7B7B; font-style:italic}.pdoc-code .err{border:1px solid #FF0000}.pdoc-code .k{color:#008000; font-weight:bold}.pdoc-code .o{color:#666666}.pdoc-code .ch{color:#3D7B7B; font-style:italic}.pdoc-code .cm{color:#3D7B7B; font-style:italic}.pdoc-code .cp{color:#9C6500}.pdoc-code .cpf{color:#3D7B7B; font-style:italic}.pdoc-code .c1{color:#3D7B7B; font-style:italic}.pdoc-code .cs{color:#3D7B7B; font-style:italic}.pdoc-code .gd{color:#A00000}.pdoc-code .ge{font-style:italic}.pdoc-code .gr{color:#E40000}.pdoc-code .gh{color:#000080; font-weight:bold}.pdoc-code .gi{color:#008400}.pdoc-code .go{color:#717171}.pdoc-code .gp{color:#000080; font-weight:bold}.pdoc-code .gs{font-weight:bold}.pdoc-code .gu{color:#800080; font-weight:bold}.pdoc-code .gt{color:#0044DD}.pdoc-code .kc{color:#008000; font-weight:bold}.pdoc-code .kd{color:#008000; font-weight:bold}.pdoc-code .kn{color:#008000; font-weight:bold}.pdoc-code .kp{color:#008000}.pdoc-code .kr{color:#008000; font-weight:bold}.pdoc-code .kt{color:#B00040}.pdoc-code .m{color:#666666}.pdoc-code .s{color:#BA2121}.pdoc-code .na{color:#687822}.pdoc-code .nb{color:#008000}.pdoc-code .nc{color:#0000FF; font-weight:bold}.pdoc-code .no{color:#880000}.pdoc-code .nd{color:#AA22FF}.pdoc-code .ni{color:#717171; font-weight:bold}.pdoc-code .ne{color:#CB3F38; font-weight:bold}.pdoc-code .nf{color:#0000FF}.pdoc-code .nl{color:#767600}.pdoc-code .nn{color:#0000FF; font-weight:bold}.pdoc-code .nt{color:#008000; font-weight:bold}.pdoc-code .nv{color:#19177C}.pdoc-code .ow{color:#AA22FF; font-weight:bold}.pdoc-code .w{color:#bbbbbb}.pdoc-code .mb{color:#666666}.pdoc-code .mf{color:#666666}.pdoc-code .mh{color:#666666}.pdoc-code .mi{color:#666666}.pdoc-code .mo{color:#666666}.pdoc-code .sa{color:#BA2121}.pdoc-code .sb{color:#BA2121}.pdoc-code .sc{color:#BA2121}.pdoc-code .dl{color:#BA2121}.pdoc-code .sd{color:#BA2121; font-style:italic}.pdoc-code .s2{color:#BA2121}.pdoc-code .se{color:#AA5D1F; font-weight:bold}.pdoc-code .sh{color:#BA2121}.pdoc-code .si{color:#A45A77; font-weight:bold}.pdoc-code .sx{color:#008000}.pdoc-code .sr{color:#A45A77}.pdoc-code .s1{color:#BA2121}.pdoc-code .ss{color:#19177C}.pdoc-code .bp{color:#008000}.pdoc-code .fm{color:#0000FF}.pdoc-code .vc{color:#19177C}.pdoc-code .vg{color:#19177C}.pdoc-code .vi{color:#19177C}.pdoc-code .vm{color:#19177C}.pdoc-code .il{color:#666666}</style>
    <style>/*! theme.css */:root{--pdoc-background:#fff;}.pdoc{--text:#212529;--muted:#6c757d;--link:#3660a5;--link-hover:#1659c5;--code:#f8f8f8;--active:#fff598;--accent:#eee;--accent2:#c1c1c1;--nav-hover:rgba(255, 255, 255, 0.5);--name:#0066BB;--def:#008800;--annotation:#007020;}</style>
    <style>/*! layout.css */html, body{width:100%;height:100%;}html, main{scroll-behavior:smooth;}body{background-color:var(--pdoc-background);}@media (max-width:769px){#navtoggle{cursor:pointer;position:absolute;width:50px;height:40px;top:1rem;right:1rem;border-color:var(--text);color:var(--text);display:flex;opacity:0.8;z-index:999;}#navtoggle:hover{opacity:1;}#togglestate + div{display:none;}#togglestate:checked + div{display:inherit;}main, header{padding:2rem 3vw;}header + main{margin-top:-3rem;}.git-button{display:none !important;}nav input[type="search"]{max-width:77%;}nav input[type="search"]:first-child{margin-top:-6px;}nav input[type="search"]:valid ~ *{display:none !important;}}@media (min-width:770px){:root{--sidebar-width:clamp(12.5rem, 28vw, 22rem);}nav{position:fixed;overflow:auto;height:100vh;width:var(--sidebar-width);}main, header{padding:3rem 2rem 3rem calc(var(--sidebar-width) + 3rem);width:calc(54rem + var(--sidebar-width));max-width:100%;}header + main{margin-top:-4rem;}#navtoggle{display:none;}}#togglestate{position:absolute;height:0;opacity:0;}nav.pdoc{--pad:clamp(0.5rem, 2vw, 1.75rem);--indent:1.5rem;background-color:var(--accent);border-right:1px solid var(--accent2);box-shadow:0 0 20px rgba(50, 50, 50, .2) inset;padding:0 0 0 var(--pad);overflow-wrap:anywhere;scrollbar-width:thin; scrollbar-color:var(--accent2) transparent; z-index:1}nav.pdoc::-webkit-scrollbar{width:.4rem; }nav.pdoc::-webkit-scrollbar-thumb{background-color:var(--accent2); }nav.pdoc > div{padding:var(--pad) 0;}nav.pdoc .module-list-button{display:inline-flex;align-items:center;color:var(--text);border-color:var(--muted);margin-bottom:1rem;}nav.pdoc .module-list-button:hover{border-color:var(--text);}nav.pdoc input[type=search]{display:block;outline-offset:0;width:calc(100% - var(--pad));}nav.pdoc .logo{max-width:calc(100% - var(--pad));max-height:35vh;display:block;margin:0 auto 1rem;transform:translate(calc(-.5 * var(--pad)), 0);}nav.pdoc ul{list-style:none;padding-left:0;}nav.pdoc > div > ul{margin-left:calc(0px - var(--pad));}nav.pdoc li a{padding:.2rem 0 .2rem calc(var(--pad) + var(--indent));}nav.pdoc > div > ul > li > a{padding-left:var(--pad);}nav.pdoc li{transition:all 100ms;}nav.pdoc li:hover{background-color:var(--nav-hover);}nav.pdoc a, nav.pdoc a:hover{color:var(--text);}nav.pdoc a{display:block;}nav.pdoc > h2:first-of-type{margin-top:1.5rem;}nav.pdoc .class:before{content:"class ";color:var(--muted);}nav.pdoc .function:after{content:"()";color:var(--muted);}nav.pdoc footer:before{content:"";display:block;width:calc(100% - var(--pad));border-top:solid var(--accent2) 1px;margin-top:1.5rem;padding-top:.5rem;}nav.pdoc footer{font-size:small;}</style>
    <style>/*! content.css */.pdoc{color:var(--text);box-sizing:border-box;line-height:1.5;background:none;}.pdoc .pdoc-button{cursor:pointer;display:inline-block;border:solid black 1px;border-radius:2px;font-size:.75rem;padding:calc(0.5em - 1px) 1em;transition:100ms all;}.pdoc .alert{padding:1rem 1rem 1rem calc(1.5rem + 24px);border:1px solid transparent;border-radius:.25rem;background-repeat:no-repeat;background-position:.75rem center;margin-bottom:1rem;}.pdoc .alert > em{display:none;}.pdoc .alert > *:last-child{margin-bottom:0;}.pdoc .alert.note{color:#084298;background-color:#cfe2ff;border-color:#b6d4fe;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23084298%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M8%2016A8%208%200%201%200%208%200a8%208%200%200%200%200%2016zm.93-9.412-1%204.705c-.07.34.029.533.304.533.194%200%20.487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703%200-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381%202.29-.287zM8%205.5a1%201%200%201%201%200-2%201%201%200%200%201%200%202z%22/%3E%3C/svg%3E");}.pdoc .alert.tip{color:#0a3622;background-color:#d1e7dd;border-color:#a3cfbb;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%230a3622%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M2%206a6%206%200%201%201%2010.174%204.31c-.203.196-.359.4-.453.619l-.762%201.769A.5.5%200%200%201%2010.5%2013a.5.5%200%200%201%200%201%20.5.5%200%200%201%200%201l-.224.447a1%201%200%200%201-.894.553H6.618a1%201%200%200%201-.894-.553L5.5%2015a.5.5%200%200%201%200-1%20.5.5%200%200%201%200-1%20.5.5%200%200%201-.46-.302l-.761-1.77a2%202%200%200%200-.453-.618A5.98%205.98%200%200%201%202%206m6-5a5%205%200%200%200-3.479%208.592c.263.254.514.564.676.941L5.83%2012h4.342l.632-1.467c.162-.377.413-.687.676-.941A5%205%200%200%200%208%201%22/%3E%3C/svg%3E");}.pdoc .alert.important{color:#055160;background-color:#cff4fc;border-color:#9eeaf9;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23055160%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M2%200a2%202%200%200%200-2%202v12a2%202%200%200%200%202%202h12a2%202%200%200%200%202-2V2a2%202%200%200%200-2-2zm6%204c.535%200%20.954.462.9.995l-.35%203.507a.552.552%200%200%201-1.1%200L7.1%204.995A.905.905%200%200%201%208%204m.002%206a1%201%200%201%201%200%202%201%201%200%200%201%200-2%22/%3E%3C/svg%3E");}.pdoc .alert.warning{color:#664d03;background-color:#fff3cd;border-color:#ffecb5;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23664d03%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M8.982%201.566a1.13%201.13%200%200%200-1.96%200L.165%2013.233c-.457.778.091%201.767.98%201.767h13.713c.889%200%201.438-.99.98-1.767L8.982%201.566zM8%205c.535%200%20.954.462.9.995l-.35%203.507a.552.552%200%200%201-1.1%200L7.1%205.995A.905.905%200%200%201%208%205zm.002%206a1%201%200%201%201%200%202%201%201%200%200%201%200-2z%22/%3E%3C/svg%3E");}.pdoc .alert.caution{color:#842029;background-color:#f8d7da;border-color:#f5c2c7;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23842029%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M11.46.146A.5.5%200%200%200%2011.107%200H4.893a.5.5%200%200%200-.353.146L.146%204.54A.5.5%200%200%200%200%204.893v6.214a.5.5%200%200%200%20.146.353l4.394%204.394a.5.5%200%200%200%20.353.146h6.214a.5.5%200%200%200%20.353-.146l4.394-4.394a.5.5%200%200%200%20.146-.353V4.893a.5.5%200%200%200-.146-.353zM8%204c.535%200%20.954.462.9.995l-.35%203.507a.552.552%200%200%201-1.1%200L7.1%204.995A.905.905%200%200%201%208%204m.002%206a1%201%200%201%201%200%202%201%201%200%200%201%200-2%22/%3E%3C/svg%3E");}.pdoc .alert.danger{color:#842029;background-color:#f8d7da;border-color:#f5c2c7;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23842029%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M5.52.359A.5.5%200%200%201%206%200h4a.5.5%200%200%201%20.474.658L8.694%206H12.5a.5.5%200%200%201%20.395.807l-7%209a.5.5%200%200%201-.873-.454L6.823%209.5H3.5a.5.5%200%200%201-.48-.641l2.5-8.5z%22/%3E%3C/svg%3E");}.pdoc .visually-hidden{position:absolute !important;width:1px !important;height:1px !important;padding:0 !important;margin:-1px !important;overflow:hidden !important;clip:rect(0, 0, 0, 0) !important;white-space:nowrap !important;border:0 !important;}.pdoc h1, .pdoc h2, .pdoc h3{font-weight:300;margin:.3em 0;padding:.2em 0;}.pdoc > section:not(.module-info) h1{font-size:1.5rem;font-weight:500;}.pdoc > section:not(.module-info) h2{font-size:1.4rem;font-weight:500;}.pdoc > section:not(.module-info) h3{font-size:1.3rem;font-weight:500;}.pdoc > section:not(.module-info) h4{font-size:1.2rem;}.pdoc > section:not(.module-info) h5{font-size:1.1rem;}.pdoc a{text-decoration:none;color:var(--link);}.pdoc a:hover{color:var(--link-hover);}.pdoc blockquote{margin-left:2rem;}.pdoc pre{border-top:1px solid var(--accent2);border-bottom:1px solid var(--accent2);margin-top:0;margin-bottom:1em;padding:.5rem 0 .5rem .5rem;overflow-x:auto;background-color:var(--code);}.pdoc code{color:var(--text);padding:.2em .4em;margin:0;font-size:85%;background-color:var(--accent);border-radius:6px;}.pdoc a > code{color:inherit;}.pdoc pre > code{display:inline-block;font-size:inherit;background:none;border:none;padding:0;}.pdoc > section:not(.module-info){margin-bottom:1.5rem;}.pdoc .modulename{margin-top:0;font-weight:bold;}.pdoc .modulename a{color:var(--link);transition:100ms all;}.pdoc .git-button{float:right;border:solid var(--link) 1px;}.pdoc .git-button:hover{background-color:var(--link);color:var(--pdoc-background);}.view-source-toggle-state,.view-source-toggle-state ~ .pdoc-code{display:none;}.view-source-toggle-state:checked ~ .pdoc-code{display:block;}.view-source-button{display:inline-block;float:right;font-size:.75rem;line-height:1.5rem;color:var(--muted);padding:0 .4rem 0 1.3rem;cursor:pointer;text-indent:-2px;}.view-source-button > span{visibility:hidden;}.module-info .view-source-button{float:none;display:flex;justify-content:flex-end;margin:-1.2rem .4rem -.2rem 0;}.view-source-button::before{position:absolute;content:"View Source";display:list-item;list-style-type:disclosure-closed;}.view-source-toggle-state:checked ~ .attr .view-source-button::before,.view-source-toggle-state:checked ~ .view-source-button::before{list-style-type:disclosure-open;}.pdoc .docstring{margin-bottom:1.5rem;}.pdoc section:not(.module-info) .docstring{margin-left:clamp(0rem, 5vw - 2rem, 1rem);}.pdoc .docstring .pdoc-code{margin-left:1em;margin-right:1em;}.pdoc h1:target,.pdoc h2:target,.pdoc h3:target,.pdoc h4:target,.pdoc h5:target,.pdoc h6:target,.pdoc .pdoc-code > pre > span:target{background-color:var(--active);box-shadow:-1rem 0 0 0 var(--active);}.pdoc .pdoc-code > pre > span:target{display:block;}.pdoc div:target > .attr,.pdoc section:target > .attr,.pdoc dd:target > a{background-color:var(--active);}.pdoc *{scroll-margin:2rem;}.pdoc .pdoc-code .linenos{user-select:none;}.pdoc .attr:hover{filter:contrast(0.95);}.pdoc section, .pdoc .classattr{position:relative;}.pdoc .headerlink{--width:clamp(1rem, 3vw, 2rem);position:absolute;top:0;left:calc(0rem - var(--width));transition:all 100ms ease-in-out;opacity:0;}.pdoc .headerlink::before{content:"#";display:block;text-align:center;width:var(--width);height:2.3rem;line-height:2.3rem;font-size:1.5rem;}.pdoc .attr:hover ~ .headerlink,.pdoc *:target > .headerlink,.pdoc .headerlink:hover{opacity:1;}.pdoc .attr{display:block;margin:.5rem 0 .5rem;padding:.4rem .4rem .4rem 1rem;background-color:var(--accent);overflow-x:auto;}.pdoc .classattr{margin-left:2rem;}.pdoc .decorator-deprecated{color:#842029;}.pdoc .decorator-deprecated ~ span{filter:grayscale(1) opacity(0.8);}.pdoc .name{color:var(--name);font-weight:bold;}.pdoc .def{color:var(--def);font-weight:bold;}.pdoc .signature{background-color:transparent;}.pdoc .param, .pdoc .return-annotation{white-space:pre;}.pdoc .signature.multiline .param{display:block;}.pdoc .signature.condensed .param{display:inline-block;}.pdoc .annotation{color:var(--annotation);}.pdoc .view-value-toggle-state,.pdoc .view-value-toggle-state ~ .default_value{display:none;}.pdoc .view-value-toggle-state:checked ~ .default_value{display:inherit;}.pdoc .view-value-button{font-size:.5rem;vertical-align:middle;border-style:dashed;margin-top:-0.1rem;}.pdoc .view-value-button:hover{background:white;}.pdoc .view-value-button::before{content:"show";text-align:center;width:2.2em;display:inline-block;}.pdoc .view-value-toggle-state:checked ~ .view-value-button::before{content:"hide";}.pdoc .inherited{margin-left:2rem;}.pdoc .inherited dt{font-weight:700;}.pdoc .inherited dt, .pdoc .inherited dd{display:inline;margin-left:0;margin-bottom:.5rem;}.pdoc .inherited dd:not(:last-child):after{content:", ";}.pdoc .inherited .class:before{content:"class ";}.pdoc .inherited .function a:after{content:"()";}.pdoc .search-result .docstring{overflow:auto;max-height:25vh;}.pdoc .search-result.focused > .attr{background-color:var(--active);}.pdoc .attribution{margin-top:2rem;display:block;opacity:0.5;transition:all 200ms;filter:grayscale(100%);}.pdoc .attribution:hover{opacity:1;filter:grayscale(0%);}.pdoc .attribution img{margin-left:5px;height:35px;vertical-align:middle;width:70px;transition:all 200ms;}.pdoc table{display:block;width:max-content;max-width:100%;overflow:auto;margin-bottom:1rem;}.pdoc table th{font-weight:600;}.pdoc table th, .pdoc table td{padding:6px 13px;border:1px solid var(--accent2);}</style>
    <style>/*! custom.css */</style></head>
<body>
    <nav class="pdoc">
        <label id="navtoggle" for="togglestate" class="pdoc-button"><svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke-linecap='round' stroke="currentColor" stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg></label>
        <input id="togglestate" type="checkbox" aria-hidden="true" tabindex="-1">
        <div>            <a class="pdoc-button module-list-button" href="../core.html">
<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-box-arrow-in-left" viewBox="0 0 16 16">
  <path fill-rule="evenodd" d="M10 3.5a.5.5 0 0 0-.5-.5h-8a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5h8a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 1 1 0v2A1.5 1.5 0 0 1 9.5 14h-8A1.5 1.5 0 0 1 0 12.5v-9A1.5 1.5 0 0 1 1.5 2h8A1.5 1.5 0 0 1 11 3.5v2a.5.5 0 0 1-1 0v-2z"/>
  <path fill-rule="evenodd" d="M4.146 8.354a.5.5 0 0 1 0-.708l3-3a.5.5 0 1 1 .708.708L5.707 7.5H14.5a.5.5 0 0 1 0 1H5.707l2.147 2.146a.5.5 0 0 1-.708.708l-3-3z"/>
</svg>                &nbsp;core</a>


            <input type="search" placeholder="Search..." role="searchbox" aria-label="search"
                   pattern=".+" required>



            <h2>API Documentation</h2>
                <ul class="memberlist">
            <li>
                    <a class="variable" href="#logger">logger</a>
            </li>
            <li>
                    <a class="class" href="#HDMIConfigurator">HDMIConfigurator</a>
                            <ul class="memberlist">
                        <li>
                                <a class="function" href="#HDMIConfigurator.__init__">HDMIConfigurator</a>
                        </li>
                        <li>
                                <a class="variable" href="#HDMIConfigurator.config_path">config_path</a>
                        </li>
                        <li>
                                <a class="variable" href="#HDMIConfigurator.backup_path">backup_path</a>
                        </li>
                        <li>
                                <a class="variable" href="#HDMIConfigurator.hdmi_configs">hdmi_configs</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.check_permissions">check_permissions</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.backup_config">backup_config</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.read_config">read_config</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.write_config">write_config</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.parse_config_line">parse_config_line</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.find_config_line">find_config_line</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.update_config">update_config</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.apply_hdmi_configs">apply_hdmi_configs</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.restore_backup">restore_backup</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.show_current_config">show_current_config</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.show_changes">show_changes</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.validate_config">validate_config</a>
                        </li>
                        <li>
                                <a class="function" href="#HDMIConfigurator.run">run</a>
                        </li>
                </ul>

            </li>
            <li>
                    <a class="function" href="#main">main</a>
            </li>
    </ul>



        <a class="attribution" title="pdoc: Python API documentation generator" href="https://pdoc.dev" target="_blank">
            built with <span class="visually-hidden">pdoc</span><img
                alt="pdoc logo"
                src="data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20role%3D%22img%22%20aria-label%3D%22pdoc%20logo%22%20width%3D%22300%22%20height%3D%22150%22%20viewBox%3D%22-1%200%2060%2030%22%3E%3Ctitle%3Epdoc%3C/title%3E%3Cpath%20d%3D%22M29.621%2021.293c-.011-.273-.214-.475-.511-.481a.5.5%200%200%200-.489.503l-.044%201.393c-.097.551-.695%201.215-1.566%201.704-.577.428-1.306.486-2.193.182-1.426-.617-2.467-1.654-3.304-2.487l-.173-.172a3.43%203.43%200%200%200-.365-.306.49.49%200%200%200-.286-.196c-1.718-1.06-4.931-1.47-7.353.191l-.219.15c-1.707%201.187-3.413%202.131-4.328%201.03-.02-.027-.49-.685-.141-1.763.233-.721.546-2.408.772-4.076.042-.09.067-.187.046-.288.166-1.347.277-2.625.241-3.351%201.378-1.008%202.271-2.586%202.271-4.362%200-.976-.272-1.935-.788-2.774-.057-.094-.122-.18-.184-.268.033-.167.052-.339.052-.516%200-1.477-1.202-2.679-2.679-2.679-.791%200-1.496.352-1.987.9a6.3%206.3%200%200%200-1.001.029c-.492-.564-1.207-.929-2.012-.929-1.477%200-2.679%201.202-2.679%202.679A2.65%202.65%200%200%200%20.97%206.554c-.383.747-.595%201.572-.595%202.41%200%202.311%201.507%204.29%203.635%205.107-.037.699-.147%202.27-.423%203.294l-.137.461c-.622%202.042-2.515%208.257%201.727%2010.643%201.614.908%203.06%201.248%204.317%201.248%202.665%200%204.492-1.524%205.322-2.401%201.476-1.559%202.886-1.854%206.491.82%201.877%201.393%203.514%201.753%204.861%201.068%202.223-1.713%202.811-3.867%203.399-6.374.077-.846.056-1.469.054-1.537zm-4.835%204.313c-.054.305-.156.586-.242.629-.034-.007-.131-.022-.307-.157-.145-.111-.314-.478-.456-.908.221.121.432.25.675.355.115.039.219.051.33.081zm-2.251-1.238c-.05.33-.158.648-.252.694-.022.001-.125-.018-.307-.157-.217-.166-.488-.906-.639-1.573.358.344.754.693%201.198%201.036zm-3.887-2.337c-.006-.116-.018-.231-.041-.342.635.145%201.189.368%201.599.625.097.231.166.481.174.642-.03.049-.055.101-.067.158-.046.013-.128.026-.298.004-.278-.037-.901-.57-1.367-1.087zm-1.127-.497c.116.306.176.625.12.71-.019.014-.117.045-.345.016-.206-.027-.604-.332-.986-.695.41-.051.816-.056%201.211-.031zm-4.535%201.535c.209.22.379.47.358.598-.006.041-.088.138-.351.234-.144.055-.539-.063-.979-.259a11.66%2011.66%200%200%200%20.972-.573zm.983-.664c.359-.237.738-.418%201.126-.554.25.237.479.548.457.694-.006.042-.087.138-.351.235-.174.064-.694-.105-1.232-.375zm-3.381%201.794c-.022.145-.061.29-.149.401-.133.166-.358.248-.69.251h-.002c-.133%200-.306-.26-.45-.621.417.091.854.07%201.291-.031zm-2.066-8.077a4.78%204.78%200%200%201-.775-.584c.172-.115.505-.254.88-.378l-.105.962zm-.331%202.302a10.32%2010.32%200%200%201-.828-.502c.202-.143.576-.328.984-.49l-.156.992zm-.45%202.157l-.701-.403c.214-.115.536-.249.891-.376a11.57%2011.57%200%200%201-.19.779zm-.181%201.716c.064.398.194.702.298.893-.194-.051-.435-.162-.736-.398.061-.119.224-.3.438-.495zM8.87%204.141c0%20.152-.123.276-.276.276s-.275-.124-.275-.276.123-.276.276-.276.275.124.275.276zm-.735-.389a1.15%201.15%200%200%200-.314.783%201.16%201.16%200%200%200%201.162%201.162c.457%200%20.842-.27%201.032-.653.026.117.042.238.042.362a1.68%201.68%200%200%201-1.679%201.679%201.68%201.68%200%200%201-1.679-1.679c0-.843.626-1.535%201.436-1.654zM5.059%205.406A1.68%201.68%200%200%201%203.38%207.085a1.68%201.68%200%200%201-1.679-1.679c0-.037.009-.072.011-.109.21.3.541.508.935.508a1.16%201.16%200%200%200%201.162-1.162%201.14%201.14%200%200%200-.474-.912c.015%200%20.03-.005.045-.005.926.001%201.679.754%201.679%201.68zM3.198%204.141c0%20.152-.123.276-.276.276s-.275-.124-.275-.276.123-.276.276-.276.275.124.275.276zM1.375%208.964c0-.52.103-1.035.288-1.52.466.394%201.06.64%201.717.64%201.144%200%202.116-.725%202.499-1.738.383%201.012%201.355%201.738%202.499%201.738.867%200%201.631-.421%202.121-1.062.307.605.478%201.267.478%201.942%200%202.486-2.153%204.51-4.801%204.51s-4.801-2.023-4.801-4.51zm24.342%2019.349c-.985.498-2.267.168-3.813-.979-3.073-2.281-5.453-3.199-7.813-.705-1.315%201.391-4.163%203.365-8.423.97-3.174-1.786-2.239-6.266-1.261-9.479l.146-.492c.276-1.02.395-2.457.444-3.268a6.11%206.11%200%200%200%201.18.115%206.01%206.01%200%200%200%202.536-.562l-.006.175c-.802.215-1.848.612-2.021%201.25-.079.295.021.601.274.837.219.203.415.364.598.501-.667.304-1.243.698-1.311%201.179-.02.144-.022.507.393.787.213.144.395.26.564.365-1.285.521-1.361.96-1.381%201.126-.018.142-.011.496.427.746l.854.489c-.473.389-.971.914-.999%201.429-.018.278.095.532.316.713.675.556%201.231.721%201.653.721.059%200%20.104-.014.158-.02.207.707.641%201.64%201.513%201.64h.013c.8-.008%201.236-.345%201.462-.626.173-.216.268-.457.325-.692.424.195.93.374%201.372.374.151%200%20.294-.021.423-.068.732-.27.944-.704.993-1.021.009-.061.003-.119.002-.179.266.086.538.147.789.147.15%200%20.294-.021.423-.069.542-.2.797-.489.914-.754.237.147.478.258.704.288.106.014.205.021.296.021.356%200%20.595-.101.767-.229.438.435%201.094.992%201.656%201.067.106.014.205.021.296.021a1.56%201.56%200%200%200%20.323-.035c.17.575.453%201.289.866%201.605.358.273.665.362.914.362a.99.99%200%200%200%20.421-.093%201.03%201.03%200%200%200%20.245-.164c.168.428.39.846.68%201.068.358.273.665.362.913.362a.99.99%200%200%200%20.421-.093c.317-.148.512-.448.639-.762.251.157.495.257.726.257.127%200%20.25-.024.37-.071.427-.17.706-.617.841-1.314.022-.015.047-.022.068-.038.067-.051.133-.104.196-.159-.443%201.486-1.107%202.761-2.086%203.257zM8.66%209.925a.5.5%200%201%200-1%200c0%20.653-.818%201.205-1.787%201.205s-1.787-.552-1.787-1.205a.5.5%200%201%200-1%200c0%201.216%201.25%202.205%202.787%202.205s2.787-.989%202.787-2.205zm4.4%2015.965l-.208.097c-2.661%201.258-4.708%201.436-6.086.527-1.542-1.017-1.88-3.19-1.844-4.198a.4.4%200%200%200-.385-.414c-.242-.029-.406.164-.414.385-.046%201.249.367%203.686%202.202%204.896.708.467%201.547.7%202.51.7%201.248%200%202.706-.392%204.362-1.174l.185-.086a.4.4%200%200%200%20.205-.527c-.089-.204-.326-.291-.527-.206zM9.547%202.292c.093.077.205.114.317.114a.5.5%200%200%200%20.318-.886L8.817.397a.5.5%200%200%200-.703.068.5.5%200%200%200%20.069.703l1.364%201.124zm-7.661-.065c.086%200%20.173-.022.253-.068l1.523-.893a.5.5%200%200%200-.506-.863l-1.523.892a.5.5%200%200%200-.179.685c.094.158.261.247.432.247z%22%20transform%3D%22matrix%28-1%200%200%201%2058%200%29%22%20fill%3D%22%233bb300%22/%3E%3Cpath%20d%3D%22M.3%2021.86V10.18q0-.46.02-.68.04-.22.18-.5.28-.54%201.34-.54%201.06%200%201.42.28.38.26.44.78.76-1.04%202.38-1.04%201.64%200%203.1%201.54%201.46%201.54%201.46%203.58%200%202.04-1.46%203.58-1.44%201.54-3.08%201.54-1.64%200-2.38-.92v4.04q0%20.46-.04.68-.02.22-.18.5-.14.3-.5.42-.36.12-.98.12-.62%200-1-.12-.36-.12-.52-.4-.14-.28-.18-.5-.02-.22-.02-.68zm3.96-9.42q-.46.54-.46%201.18%200%20.64.46%201.18.48.52%201.2.52.74%200%201.24-.52.52-.52.52-1.18%200-.66-.48-1.18-.48-.54-1.26-.54-.76%200-1.22.54zm14.741-8.36q.16-.3.54-.42.38-.12%201-.12.64%200%201.02.12.38.12.52.42.16.3.18.54.04.22.04.68v11.94q0%20.46-.04.7-.02.22-.18.5-.3.54-1.7.54-1.38%200-1.54-.98-.84.96-2.34.96-1.8%200-3.28-1.56-1.48-1.58-1.48-3.66%200-2.1%201.48-3.68%201.5-1.58%203.28-1.58%201.48%200%202.3%201v-4.2q0-.46.02-.68.04-.24.18-.52zm-3.24%2010.86q.52.54%201.26.54.74%200%201.22-.54.5-.54.5-1.18%200-.66-.48-1.22-.46-.56-1.26-.56-.8%200-1.28.56-.48.54-.48%201.2%200%20.66.52%201.2zm7.833-1.2q0-2.4%201.68-3.96%201.68-1.56%203.84-1.56%202.16%200%203.82%201.56%201.66%201.54%201.66%203.94%200%201.66-.86%202.96-.86%201.28-2.1%201.9-1.22.6-2.54.6-1.32%200-2.56-.64-1.24-.66-2.1-1.92-.84-1.28-.84-2.88zm4.18%201.44q.64.48%201.3.48.66%200%201.32-.5.66-.5.66-1.48%200-.98-.62-1.46-.62-.48-1.34-.48-.72%200-1.34.5-.62.5-.62%201.48%200%20.96.64%201.46zm11.412-1.44q0%20.84.56%201.32.56.46%201.18.46.64%200%201.18-.36.56-.38.9-.38.6%200%201.46%201.06.46.58.46%201.04%200%20.76-1.1%201.42-1.14.8-2.8.8-1.86%200-3.58-1.34-.82-.64-1.34-1.7-.52-1.08-.52-2.36%200-1.3.52-2.34.52-1.06%201.34-1.7%201.66-1.32%203.54-1.32.76%200%201.48.22.72.2%201.06.4l.32.2q.36.24.56.38.52.4.52.92%200%20.5-.42%201.14-.72%201.1-1.38%201.1-.38%200-1.08-.44-.36-.34-1.04-.34-.66%200-1.24.48-.58.48-.58%201.34z%22%20fill%3D%22green%22/%3E%3C/svg%3E"/>
        </a>
</div>
    </nav>
    <main class="pdoc">
            <section class="module-info">
                    <h1 class="modulename">
<a href="./../core.html">core</a><wbr>.hdmi_config    </h1>

                        <div class="docstring"><p>树莓派HDMI配置优化脚本</p>

<p>这是一个专门用于优化树莓派HDMI输出的Python脚本。
自动修改 /boot/config.txt 配置文件，实现最佳的显示效果。</p>

<p>主要功能：</p>

<ul>
<li>强制HDMI输出为1080p@60Hz</li>
<li>禁用过扫描（overscan）实现全屏显示</li>
<li>增加GPU显存至256MB提升性能</li>
<li>自动备份和恢复配置文件</li>
<li>支持预览模式和回滚操作</li>
<li>完整的配置验证和错误处理</li>
</ul>

<p>配置项说明：</p>

<ul>
<li>hdmi_group=1: HDMI组1（CEA标准）</li>
<li>hdmi_mode=16: 1080p@60Hz分辨率</li>
<li>hdmi_force_hotplug=1: 强制HDMI热插拔检测</li>
<li>hdmi_drive=2: HDMI驱动强度</li>
<li>disable_overscan=1: 禁用过扫描</li>
<li>gpu_mem=256: GPU显存256MB</li>
</ul>

<p>系统要求：</p>

<ul>
<li>树莓派系统</li>
<li>Python 3.7+</li>
<li>sudo权限（用于修改/boot/config.txt）</li>
</ul>

<p>作者: AI Assistant
版本: 2.0.0
许可证: MIT</p>
</div>

                        <input id="mod-hdmi_config-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">

                        <label class="view-source-button" for="mod-hdmi_config-view-source"><span>View Source</span></label>

                        <div class="pdoc-code codehilite"><pre><span></span><span id="L-1"><a href="#L-1"><span class="linenos">  1</span></a><span class="ch">#!/usr/bin/env python3</span>
</span><span id="L-2"><a href="#L-2"><span class="linenos">  2</span></a><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-3"><a href="#L-3"><span class="linenos">  3</span></a><span class="sd">树莓派HDMI配置优化脚本</span>
</span><span id="L-4"><a href="#L-4"><span class="linenos">  4</span></a>
</span><span id="L-5"><a href="#L-5"><span class="linenos">  5</span></a><span class="sd">这是一个专门用于优化树莓派HDMI输出的Python脚本。</span>
</span><span id="L-6"><a href="#L-6"><span class="linenos">  6</span></a><span class="sd">自动修改 /boot/config.txt 配置文件，实现最佳的显示效果。</span>
</span><span id="L-7"><a href="#L-7"><span class="linenos">  7</span></a>
</span><span id="L-8"><a href="#L-8"><span class="linenos">  8</span></a><span class="sd">主要功能：</span>
</span><span id="L-9"><a href="#L-9"><span class="linenos">  9</span></a><span class="sd">- 强制HDMI输出为1080p@60Hz</span>
</span><span id="L-10"><a href="#L-10"><span class="linenos"> 10</span></a><span class="sd">- 禁用过扫描（overscan）实现全屏显示</span>
</span><span id="L-11"><a href="#L-11"><span class="linenos"> 11</span></a><span class="sd">- 增加GPU显存至256MB提升性能</span>
</span><span id="L-12"><a href="#L-12"><span class="linenos"> 12</span></a><span class="sd">- 自动备份和恢复配置文件</span>
</span><span id="L-13"><a href="#L-13"><span class="linenos"> 13</span></a><span class="sd">- 支持预览模式和回滚操作</span>
</span><span id="L-14"><a href="#L-14"><span class="linenos"> 14</span></a><span class="sd">- 完整的配置验证和错误处理</span>
</span><span id="L-15"><a href="#L-15"><span class="linenos"> 15</span></a>
</span><span id="L-16"><a href="#L-16"><span class="linenos"> 16</span></a><span class="sd">配置项说明：</span>
</span><span id="L-17"><a href="#L-17"><span class="linenos"> 17</span></a><span class="sd">- hdmi_group=1: HDMI组1（CEA标准）</span>
</span><span id="L-18"><a href="#L-18"><span class="linenos"> 18</span></a><span class="sd">- hdmi_mode=16: 1080p@60Hz分辨率</span>
</span><span id="L-19"><a href="#L-19"><span class="linenos"> 19</span></a><span class="sd">- hdmi_force_hotplug=1: 强制HDMI热插拔检测</span>
</span><span id="L-20"><a href="#L-20"><span class="linenos"> 20</span></a><span class="sd">- hdmi_drive=2: HDMI驱动强度</span>
</span><span id="L-21"><a href="#L-21"><span class="linenos"> 21</span></a><span class="sd">- disable_overscan=1: 禁用过扫描</span>
</span><span id="L-22"><a href="#L-22"><span class="linenos"> 22</span></a><span class="sd">- gpu_mem=256: GPU显存256MB</span>
</span><span id="L-23"><a href="#L-23"><span class="linenos"> 23</span></a>
</span><span id="L-24"><a href="#L-24"><span class="linenos"> 24</span></a><span class="sd">系统要求：</span>
</span><span id="L-25"><a href="#L-25"><span class="linenos"> 25</span></a><span class="sd">- 树莓派系统</span>
</span><span id="L-26"><a href="#L-26"><span class="linenos"> 26</span></a><span class="sd">- Python 3.7+</span>
</span><span id="L-27"><a href="#L-27"><span class="linenos"> 27</span></a><span class="sd">- sudo权限（用于修改/boot/config.txt）</span>
</span><span id="L-28"><a href="#L-28"><span class="linenos"> 28</span></a>
</span><span id="L-29"><a href="#L-29"><span class="linenos"> 29</span></a><span class="sd">作者: AI Assistant</span>
</span><span id="L-30"><a href="#L-30"><span class="linenos"> 30</span></a><span class="sd">版本: 2.0.0</span>
</span><span id="L-31"><a href="#L-31"><span class="linenos"> 31</span></a><span class="sd">许可证: MIT</span>
</span><span id="L-32"><a href="#L-32"><span class="linenos"> 32</span></a><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-33"><a href="#L-33"><span class="linenos"> 33</span></a>
</span><span id="L-34"><a href="#L-34"><span class="linenos"> 34</span></a><span class="kn">import</span> <span class="nn">os</span>
</span><span id="L-35"><a href="#L-35"><span class="linenos"> 35</span></a><span class="kn">import</span> <span class="nn">sys</span>
</span><span id="L-36"><a href="#L-36"><span class="linenos"> 36</span></a><span class="kn">import</span> <span class="nn">shutil</span>
</span><span id="L-37"><a href="#L-37"><span class="linenos"> 37</span></a><span class="kn">import</span> <span class="nn">argparse</span>
</span><span id="L-38"><a href="#L-38"><span class="linenos"> 38</span></a><span class="kn">from</span> <span class="nn">pathlib</span> <span class="kn">import</span> <span class="n">Path</span>
</span><span id="L-39"><a href="#L-39"><span class="linenos"> 39</span></a><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Optional</span>
</span><span id="L-40"><a href="#L-40"><span class="linenos"> 40</span></a><span class="n">sys</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">insert</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">abspath</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">dirname</span><span class="p">(</span><span class="vm">__file__</span><span class="p">),</span> <span class="s1">&#39;../../retropie/core&#39;</span><span class="p">)))</span>
</span><span id="L-41"><a href="#L-41"><span class="linenos"> 41</span></a><span class="kn">from</span> <span class="nn">logger_config</span> <span class="kn">import</span> <span class="n">get_logger</span>
</span><span id="L-42"><a href="#L-42"><span class="linenos"> 42</span></a>
</span><span id="L-43"><a href="#L-43"><span class="linenos"> 43</span></a><span class="c1"># 配置日志</span>
</span><span id="L-44"><a href="#L-44"><span class="linenos"> 44</span></a><span class="n">logger</span> <span class="o">=</span> <span class="n">get_logger</span><span class="p">(</span><span class="s2">&quot;hdmi_config&quot;</span><span class="p">,</span> <span class="s2">&quot;hdmi_config.log&quot;</span><span class="p">)</span>
</span><span id="L-45"><a href="#L-45"><span class="linenos"> 45</span></a>
</span><span id="L-46"><a href="#L-46"><span class="linenos"> 46</span></a><span class="k">class</span> <span class="nc">HDMIConfigurator</span><span class="p">:</span>
</span><span id="L-47"><a href="#L-47"><span class="linenos"> 47</span></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-48"><a href="#L-48"><span class="linenos"> 48</span></a><span class="sd">    HDMI配置器类</span>
</span><span id="L-49"><a href="#L-49"><span class="linenos"> 49</span></a><span class="sd">    </span>
</span><span id="L-50"><a href="#L-50"><span class="linenos"> 50</span></a><span class="sd">    提供完整的树莓派HDMI配置优化功能，包括：</span>
</span><span id="L-51"><a href="#L-51"><span class="linenos"> 51</span></a><span class="sd">    - 配置文件读取和解析</span>
</span><span id="L-52"><a href="#L-52"><span class="linenos"> 52</span></a><span class="sd">    - 自动备份和恢复</span>
</span><span id="L-53"><a href="#L-53"><span class="linenos"> 53</span></a><span class="sd">    - HDMI参数优化</span>
</span><span id="L-54"><a href="#L-54"><span class="linenos"> 54</span></a><span class="sd">    - 配置验证和预览</span>
</span><span id="L-55"><a href="#L-55"><span class="linenos"> 55</span></a><span class="sd">    - 错误处理和回滚</span>
</span><span id="L-56"><a href="#L-56"><span class="linenos"> 56</span></a><span class="sd">    </span>
</span><span id="L-57"><a href="#L-57"><span class="linenos"> 57</span></a><span class="sd">    属性:</span>
</span><span id="L-58"><a href="#L-58"><span class="linenos"> 58</span></a><span class="sd">        config_path (Path): 配置文件路径</span>
</span><span id="L-59"><a href="#L-59"><span class="linenos"> 59</span></a><span class="sd">        backup_path (Path): 备份文件路径</span>
</span><span id="L-60"><a href="#L-60"><span class="linenos"> 60</span></a><span class="sd">        hdmi_configs (Dict): HDMI配置项字典</span>
</span><span id="L-61"><a href="#L-61"><span class="linenos"> 61</span></a><span class="sd">    &quot;&quot;&quot;</span>
</span><span id="L-62"><a href="#L-62"><span class="linenos"> 62</span></a>    
</span><span id="L-63"><a href="#L-63"><span class="linenos"> 63</span></a>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">config_path</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;/boot/config.txt&quot;</span><span class="p">):</span>
</span><span id="L-64"><a href="#L-64"><span class="linenos"> 64</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-65"><a href="#L-65"><span class="linenos"> 65</span></a><span class="sd">        初始化HDMI配置器</span>
</span><span id="L-66"><a href="#L-66"><span class="linenos"> 66</span></a><span class="sd">        </span>
</span><span id="L-67"><a href="#L-67"><span class="linenos"> 67</span></a><span class="sd">        Args:</span>
</span><span id="L-68"><a href="#L-68"><span class="linenos"> 68</span></a><span class="sd">            config_path (str): 配置文件路径</span>
</span><span id="L-69"><a href="#L-69"><span class="linenos"> 69</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="L-70"><a href="#L-70"><span class="linenos"> 70</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">config_path</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">config_path</span><span class="p">)</span>
</span><span id="L-71"><a href="#L-71"><span class="linenos"> 71</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="o">.</span><span class="n">with_suffix</span><span class="p">(</span><span class="s1">&#39;.txt.backup&#39;</span><span class="p">)</span>
</span><span id="L-72"><a href="#L-72"><span class="linenos"> 72</span></a>        
</span><span id="L-73"><a href="#L-73"><span class="linenos"> 73</span></a>        <span class="c1"># HDMI配置项</span>
</span><span id="L-74"><a href="#L-74"><span class="linenos"> 74</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">hdmi_configs</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="L-75"><a href="#L-75"><span class="linenos"> 75</span></a>            <span class="c1"># 强制HDMI输出为1080p@60Hz</span>
</span><span id="L-76"><a href="#L-76"><span class="linenos"> 76</span></a>            <span class="s2">&quot;hdmi_group&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="L-77"><a href="#L-77"><span class="linenos"> 77</span></a>            <span class="s2">&quot;hdmi_mode&quot;</span><span class="p">:</span> <span class="s2">&quot;16&quot;</span><span class="p">,</span>
</span><span id="L-78"><a href="#L-78"><span class="linenos"> 78</span></a>            <span class="s2">&quot;hdmi_force_hotplug&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="L-79"><a href="#L-79"><span class="linenos"> 79</span></a>            <span class="s2">&quot;hdmi_drive&quot;</span><span class="p">:</span> <span class="s2">&quot;2&quot;</span><span class="p">,</span>
</span><span id="L-80"><a href="#L-80"><span class="linenos"> 80</span></a>            
</span><span id="L-81"><a href="#L-81"><span class="linenos"> 81</span></a>            <span class="c1"># 禁用过扫描</span>
</span><span id="L-82"><a href="#L-82"><span class="linenos"> 82</span></a>            <span class="s2">&quot;disable_overscan&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="L-83"><a href="#L-83"><span class="linenos"> 83</span></a>            
</span><span id="L-84"><a href="#L-84"><span class="linenos"> 84</span></a>            <span class="c1"># GPU显存配置</span>
</span><span id="L-85"><a href="#L-85"><span class="linenos"> 85</span></a>            <span class="s2">&quot;gpu_mem&quot;</span><span class="p">:</span> <span class="s2">&quot;256&quot;</span><span class="p">,</span>
</span><span id="L-86"><a href="#L-86"><span class="linenos"> 86</span></a>            
</span><span id="L-87"><a href="#L-87"><span class="linenos"> 87</span></a>            <span class="c1"># 其他优化配置</span>
</span><span id="L-88"><a href="#L-88"><span class="linenos"> 88</span></a>            <span class="s2">&quot;hdmi_ignore_cec_init&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="L-89"><a href="#L-89"><span class="linenos"> 89</span></a>            <span class="s2">&quot;hdmi_ignore_cec&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="L-90"><a href="#L-90"><span class="linenos"> 90</span></a>            <span class="s2">&quot;config_hdmi_boost&quot;</span><span class="p">:</span> <span class="s2">&quot;4&quot;</span>
</span><span id="L-91"><a href="#L-91"><span class="linenos"> 91</span></a>        <span class="p">}</span>
</span><span id="L-92"><a href="#L-92"><span class="linenos"> 92</span></a>        
</span><span id="L-93"><a href="#L-93"><span class="linenos"> 93</span></a>    <span class="k">def</span> <span class="nf">check_permissions</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-94"><a href="#L-94"><span class="linenos"> 94</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;检查文件权限&quot;&quot;&quot;</span>
</span><span id="L-95"><a href="#L-95"><span class="linenos"> 95</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-96"><a href="#L-96"><span class="linenos"> 96</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置文件不存在: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-97"><a href="#L-97"><span class="linenos"> 97</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-98"><a href="#L-98"><span class="linenos"> 98</span></a>        
</span><span id="L-99"><a href="#L-99"><span class="linenos"> 99</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">os</span><span class="o">.</span><span class="n">access</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">R_OK</span> <span class="o">|</span> <span class="n">os</span><span class="o">.</span><span class="n">W_OK</span><span class="p">):</span>
</span><span id="L-100"><a href="#L-100"><span class="linenos">100</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;没有配置文件读写权限: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-101"><a href="#L-101"><span class="linenos">101</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;请使用 sudo 运行此脚本&quot;</span><span class="p">)</span>
</span><span id="L-102"><a href="#L-102"><span class="linenos">102</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-103"><a href="#L-103"><span class="linenos">103</span></a>        
</span><span id="L-104"><a href="#L-104"><span class="linenos">104</span></a>        <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-105"><a href="#L-105"><span class="linenos">105</span></a>    
</span><span id="L-106"><a href="#L-106"><span class="linenos">106</span></a>    <span class="k">def</span> <span class="nf">backup_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-107"><a href="#L-107"><span class="linenos">107</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;备份原始配置文件&quot;&quot;&quot;</span>
</span><span id="L-108"><a href="#L-108"><span class="linenos">108</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-109"><a href="#L-109"><span class="linenos">109</span></a>            <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-110"><a href="#L-110"><span class="linenos">110</span></a>                <span class="n">shutil</span><span class="o">.</span><span class="n">copy2</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="p">)</span>
</span><span id="L-111"><a href="#L-111"><span class="linenos">111</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置文件已备份到: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-112"><a href="#L-112"><span class="linenos">112</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="L-113"><a href="#L-113"><span class="linenos">113</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;备份文件已存在: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-114"><a href="#L-114"><span class="linenos">114</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-115"><a href="#L-115"><span class="linenos">115</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-116"><a href="#L-116"><span class="linenos">116</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;备份配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-117"><a href="#L-117"><span class="linenos">117</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-118"><a href="#L-118"><span class="linenos">118</span></a>    
</span><span id="L-119"><a href="#L-119"><span class="linenos">119</span></a>    <span class="k">def</span> <span class="nf">read_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="L-120"><a href="#L-120"><span class="linenos">120</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;读取配置文件&quot;&quot;&quot;</span>
</span><span id="L-121"><a href="#L-121"><span class="linenos">121</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-122"><a href="#L-122"><span class="linenos">122</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">,</span> <span class="s1">&#39;r&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="L-123"><a href="#L-123"><span class="linenos">123</span></a>                <span class="k">return</span> <span class="n">f</span><span class="o">.</span><span class="n">readlines</span><span class="p">()</span>
</span><span id="L-124"><a href="#L-124"><span class="linenos">124</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-125"><a href="#L-125"><span class="linenos">125</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;读取配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-126"><a href="#L-126"><span class="linenos">126</span></a>            <span class="k">return</span> <span class="p">[]</span>
</span><span id="L-127"><a href="#L-127"><span class="linenos">127</span></a>    
</span><span id="L-128"><a href="#L-128"><span class="linenos">128</span></a>    <span class="k">def</span> <span class="nf">write_config</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lines</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-129"><a href="#L-129"><span class="linenos">129</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;写入配置文件&quot;&quot;&quot;</span>
</span><span id="L-130"><a href="#L-130"><span class="linenos">130</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-131"><a href="#L-131"><span class="linenos">131</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">,</span> <span class="s1">&#39;w&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="L-132"><a href="#L-132"><span class="linenos">132</span></a>                <span class="n">f</span><span class="o">.</span><span class="n">writelines</span><span class="p">(</span><span class="n">lines</span><span class="p">)</span>
</span><span id="L-133"><a href="#L-133"><span class="linenos">133</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;配置文件写入成功&quot;</span><span class="p">)</span>
</span><span id="L-134"><a href="#L-134"><span class="linenos">134</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-135"><a href="#L-135"><span class="linenos">135</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-136"><a href="#L-136"><span class="linenos">136</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;写入配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-137"><a href="#L-137"><span class="linenos">137</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-138"><a href="#L-138"><span class="linenos">138</span></a>    
</span><span id="L-139"><a href="#L-139"><span class="linenos">139</span></a>    <span class="k">def</span> <span class="nf">parse_config_line</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">line</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]:</span>
</span><span id="L-140"><a href="#L-140"><span class="linenos">140</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;解析配置行&quot;&quot;&quot;</span>
</span><span id="L-141"><a href="#L-141"><span class="linenos">141</span></a>        <span class="n">line</span> <span class="o">=</span> <span class="n">line</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>
</span><span id="L-142"><a href="#L-142"><span class="linenos">142</span></a>        
</span><span id="L-143"><a href="#L-143"><span class="linenos">143</span></a>        <span class="c1"># 跳过注释和空行</span>
</span><span id="L-144"><a href="#L-144"><span class="linenos">144</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">line</span> <span class="ow">or</span> <span class="n">line</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;#&#39;</span><span class="p">):</span>
</span><span id="L-145"><a href="#L-145"><span class="linenos">145</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-146"><a href="#L-146"><span class="linenos">146</span></a>        
</span><span id="L-147"><a href="#L-147"><span class="linenos">147</span></a>        <span class="c1"># 解析 key=value 格式</span>
</span><span id="L-148"><a href="#L-148"><span class="linenos">148</span></a>        <span class="k">if</span> <span class="s1">&#39;=&#39;</span> <span class="ow">in</span> <span class="n">line</span><span class="p">:</span>
</span><span id="L-149"><a href="#L-149"><span class="linenos">149</span></a>            <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="o">=</span> <span class="n">line</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;=&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
</span><span id="L-150"><a href="#L-150"><span class="linenos">150</span></a>            <span class="k">return</span> <span class="p">{</span>
</span><span id="L-151"><a href="#L-151"><span class="linenos">151</span></a>                <span class="s1">&#39;key&#39;</span><span class="p">:</span> <span class="n">key</span><span class="o">.</span><span class="n">strip</span><span class="p">(),</span>
</span><span id="L-152"><a href="#L-152"><span class="linenos">152</span></a>                <span class="s1">&#39;value&#39;</span><span class="p">:</span> <span class="n">value</span><span class="o">.</span><span class="n">strip</span><span class="p">(),</span>
</span><span id="L-153"><a href="#L-153"><span class="linenos">153</span></a>                <span class="s1">&#39;line&#39;</span><span class="p">:</span> <span class="n">line</span>
</span><span id="L-154"><a href="#L-154"><span class="linenos">154</span></a>            <span class="p">}</span>
</span><span id="L-155"><a href="#L-155"><span class="linenos">155</span></a>        
</span><span id="L-156"><a href="#L-156"><span class="linenos">156</span></a>        <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-157"><a href="#L-157"><span class="linenos">157</span></a>    
</span><span id="L-158"><a href="#L-158"><span class="linenos">158</span></a>    <span class="k">def</span> <span class="nf">find_config_line</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lines</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="n">key</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]:</span>
</span><span id="L-159"><a href="#L-159"><span class="linenos">159</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;查找配置项的行号&quot;&quot;&quot;</span>
</span><span id="L-160"><a href="#L-160"><span class="linenos">160</span></a>        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">line</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">lines</span><span class="p">):</span>
</span><span id="L-161"><a href="#L-161"><span class="linenos">161</span></a>            <span class="n">parsed</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parse_config_line</span><span class="p">(</span><span class="n">line</span><span class="p">)</span>
</span><span id="L-162"><a href="#L-162"><span class="linenos">162</span></a>            <span class="k">if</span> <span class="n">parsed</span> <span class="ow">and</span> <span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;key&#39;</span><span class="p">]</span> <span class="o">==</span> <span class="n">key</span><span class="p">:</span>
</span><span id="L-163"><a href="#L-163"><span class="linenos">163</span></a>                <span class="k">return</span> <span class="n">i</span>
</span><span id="L-164"><a href="#L-164"><span class="linenos">164</span></a>        <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-165"><a href="#L-165"><span class="linenos">165</span></a>    
</span><span id="L-166"><a href="#L-166"><span class="linenos">166</span></a>    <span class="k">def</span> <span class="nf">update_config</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lines</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="n">key</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="L-167"><a href="#L-167"><span class="linenos">167</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;更新配置项&quot;&quot;&quot;</span>
</span><span id="L-168"><a href="#L-168"><span class="linenos">168</span></a>        <span class="n">line_index</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_config_line</span><span class="p">(</span><span class="n">lines</span><span class="p">,</span> <span class="n">key</span><span class="p">)</span>
</span><span id="L-169"><a href="#L-169"><span class="linenos">169</span></a>        
</span><span id="L-170"><a href="#L-170"><span class="linenos">170</span></a>        <span class="k">if</span> <span class="n">line_index</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="L-171"><a href="#L-171"><span class="linenos">171</span></a>            <span class="c1"># 更新现有配置</span>
</span><span id="L-172"><a href="#L-172"><span class="linenos">172</span></a>            <span class="n">lines</span><span class="p">[</span><span class="n">line_index</span><span class="p">]</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="se">\n</span><span class="s2">&quot;</span>
</span><span id="L-173"><a href="#L-173"><span class="linenos">173</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;更新配置: </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-174"><a href="#L-174"><span class="linenos">174</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="L-175"><a href="#L-175"><span class="linenos">175</span></a>            <span class="c1"># 添加新配置</span>
</span><span id="L-176"><a href="#L-176"><span class="linenos">176</span></a>            <span class="n">lines</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-177"><a href="#L-177"><span class="linenos">177</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;添加配置: </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-178"><a href="#L-178"><span class="linenos">178</span></a>        
</span><span id="L-179"><a href="#L-179"><span class="linenos">179</span></a>        <span class="k">return</span> <span class="n">lines</span>
</span><span id="L-180"><a href="#L-180"><span class="linenos">180</span></a>    
</span><span id="L-181"><a href="#L-181"><span class="linenos">181</span></a>    <span class="k">def</span> <span class="nf">apply_hdmi_configs</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-182"><a href="#L-182"><span class="linenos">182</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;应用HDMI配置&quot;&quot;&quot;</span>
</span><span id="L-183"><a href="#L-183"><span class="linenos">183</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;开始应用HDMI配置...&quot;</span><span class="p">)</span>
</span><span id="L-184"><a href="#L-184"><span class="linenos">184</span></a>        
</span><span id="L-185"><a href="#L-185"><span class="linenos">185</span></a>        <span class="c1"># 读取现有配置</span>
</span><span id="L-186"><a href="#L-186"><span class="linenos">186</span></a>        <span class="n">lines</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_config</span><span class="p">()</span>
</span><span id="L-187"><a href="#L-187"><span class="linenos">187</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="L-188"><a href="#L-188"><span class="linenos">188</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-189"><a href="#L-189"><span class="linenos">189</span></a>        
</span><span id="L-190"><a href="#L-190"><span class="linenos">190</span></a>        <span class="c1"># 应用每个配置项</span>
</span><span id="L-191"><a href="#L-191"><span class="linenos">191</span></a>        <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">hdmi_configs</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><span id="L-192"><a href="#L-192"><span class="linenos">192</span></a>            <span class="n">lines</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">update_config</span><span class="p">(</span><span class="n">lines</span><span class="p">,</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
</span><span id="L-193"><a href="#L-193"><span class="linenos">193</span></a>        
</span><span id="L-194"><a href="#L-194"><span class="linenos">194</span></a>        <span class="c1"># 写入配置文件</span>
</span><span id="L-195"><a href="#L-195"><span class="linenos">195</span></a>        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">write_config</span><span class="p">(</span><span class="n">lines</span><span class="p">)</span>
</span><span id="L-196"><a href="#L-196"><span class="linenos">196</span></a>    
</span><span id="L-197"><a href="#L-197"><span class="linenos">197</span></a>    <span class="k">def</span> <span class="nf">restore_backup</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-198"><a href="#L-198"><span class="linenos">198</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;恢复备份配置&quot;&quot;&quot;</span>
</span><span id="L-199"><a href="#L-199"><span class="linenos">199</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-200"><a href="#L-200"><span class="linenos">200</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;备份文件不存在&quot;</span><span class="p">)</span>
</span><span id="L-201"><a href="#L-201"><span class="linenos">201</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-202"><a href="#L-202"><span class="linenos">202</span></a>        
</span><span id="L-203"><a href="#L-203"><span class="linenos">203</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-204"><a href="#L-204"><span class="linenos">204</span></a>            <span class="n">shutil</span><span class="o">.</span><span class="n">copy2</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">)</span>
</span><span id="L-205"><a href="#L-205"><span class="linenos">205</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;已恢复原始配置&quot;</span><span class="p">)</span>
</span><span id="L-206"><a href="#L-206"><span class="linenos">206</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-207"><a href="#L-207"><span class="linenos">207</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-208"><a href="#L-208"><span class="linenos">208</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;恢复配置失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-209"><a href="#L-209"><span class="linenos">209</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-210"><a href="#L-210"><span class="linenos">210</span></a>    
</span><span id="L-211"><a href="#L-211"><span class="linenos">211</span></a>    <span class="k">def</span> <span class="nf">show_current_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="L-212"><a href="#L-212"><span class="linenos">212</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;显示当前配置&quot;&quot;&quot;</span>
</span><span id="L-213"><a href="#L-213"><span class="linenos">213</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;当前HDMI相关配置:&quot;</span><span class="p">)</span>
</span><span id="L-214"><a href="#L-214"><span class="linenos">214</span></a>        
</span><span id="L-215"><a href="#L-215"><span class="linenos">215</span></a>        <span class="n">lines</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_config</span><span class="p">()</span>
</span><span id="L-216"><a href="#L-216"><span class="linenos">216</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="L-217"><a href="#L-217"><span class="linenos">217</span></a>            <span class="k">return</span>
</span><span id="L-218"><a href="#L-218"><span class="linenos">218</span></a>        
</span><span id="L-219"><a href="#L-219"><span class="linenos">219</span></a>        <span class="n">hdmi_keys</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">hdmi_configs</span><span class="o">.</span><span class="n">keys</span><span class="p">())</span>
</span><span id="L-220"><a href="#L-220"><span class="linenos">220</span></a>        
</span><span id="L-221"><a href="#L-221"><span class="linenos">221</span></a>        <span class="k">for</span> <span class="n">line</span> <span class="ow">in</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="L-222"><a href="#L-222"><span class="linenos">222</span></a>            <span class="n">parsed</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parse_config_line</span><span class="p">(</span><span class="n">line</span><span class="p">)</span>
</span><span id="L-223"><a href="#L-223"><span class="linenos">223</span></a>            <span class="k">if</span> <span class="n">parsed</span> <span class="ow">and</span> <span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;key&#39;</span><span class="p">]</span> <span class="ow">in</span> <span class="n">hdmi_keys</span><span class="p">:</span>
</span><span id="L-224"><a href="#L-224"><span class="linenos">224</span></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  </span><span class="si">{</span><span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;key&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;value&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-225"><a href="#L-225"><span class="linenos">225</span></a>    
</span><span id="L-226"><a href="#L-226"><span class="linenos">226</span></a>    <span class="k">def</span> <span class="nf">show_changes</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="L-227"><a href="#L-227"><span class="linenos">227</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;显示将要应用的更改&quot;&quot;&quot;</span>
</span><span id="L-228"><a href="#L-228"><span class="linenos">228</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;将要应用的HDMI配置:&quot;</span><span class="p">)</span>
</span><span id="L-229"><a href="#L-229"><span class="linenos">229</span></a>        <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">hdmi_configs</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><span id="L-230"><a href="#L-230"><span class="linenos">230</span></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-231"><a href="#L-231"><span class="linenos">231</span></a>        
</span><span id="L-232"><a href="#L-232"><span class="linenos">232</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">配置说明:&quot;</span><span class="p">)</span>
</span><span id="L-233"><a href="#L-233"><span class="linenos">233</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_group=1: HDMI组1（CEA标准）&quot;</span><span class="p">)</span>
</span><span id="L-234"><a href="#L-234"><span class="linenos">234</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_mode=16: 1080p@60Hz&quot;</span><span class="p">)</span>
</span><span id="L-235"><a href="#L-235"><span class="linenos">235</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_force_hotplug=1: 强制HDMI热插拔检测&quot;</span><span class="p">)</span>
</span><span id="L-236"><a href="#L-236"><span class="linenos">236</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_drive=2: HDMI驱动强度&quot;</span><span class="p">)</span>
</span><span id="L-237"><a href="#L-237"><span class="linenos">237</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  disable_overscan=1: 禁用过扫描&quot;</span><span class="p">)</span>
</span><span id="L-238"><a href="#L-238"><span class="linenos">238</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  gpu_mem=256: GPU显存256MB&quot;</span><span class="p">)</span>
</span><span id="L-239"><a href="#L-239"><span class="linenos">239</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_ignore_cec_init=1: 忽略CEC初始化&quot;</span><span class="p">)</span>
</span><span id="L-240"><a href="#L-240"><span class="linenos">240</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_ignore_cec=1: 忽略CEC&quot;</span><span class="p">)</span>
</span><span id="L-241"><a href="#L-241"><span class="linenos">241</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  config_hdmi_boost=4: HDMI信号增强&quot;</span><span class="p">)</span>
</span><span id="L-242"><a href="#L-242"><span class="linenos">242</span></a>    
</span><span id="L-243"><a href="#L-243"><span class="linenos">243</span></a>    <span class="k">def</span> <span class="nf">validate_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-244"><a href="#L-244"><span class="linenos">244</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;验证配置&quot;&quot;&quot;</span>
</span><span id="L-245"><a href="#L-245"><span class="linenos">245</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;验证配置...&quot;</span><span class="p">)</span>
</span><span id="L-246"><a href="#L-246"><span class="linenos">246</span></a>        
</span><span id="L-247"><a href="#L-247"><span class="linenos">247</span></a>        <span class="c1"># 检查关键配置项</span>
</span><span id="L-248"><a href="#L-248"><span class="linenos">248</span></a>        <span class="n">required_configs</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="L-249"><a href="#L-249"><span class="linenos">249</span></a>            <span class="s1">&#39;hdmi_group&#39;</span><span class="p">:</span> <span class="s1">&#39;1&#39;</span><span class="p">,</span>
</span><span id="L-250"><a href="#L-250"><span class="linenos">250</span></a>            <span class="s1">&#39;hdmi_mode&#39;</span><span class="p">:</span> <span class="s1">&#39;16&#39;</span><span class="p">,</span>
</span><span id="L-251"><a href="#L-251"><span class="linenos">251</span></a>            <span class="s1">&#39;gpu_mem&#39;</span><span class="p">:</span> <span class="s1">&#39;256&#39;</span>
</span><span id="L-252"><a href="#L-252"><span class="linenos">252</span></a>        <span class="p">}</span>
</span><span id="L-253"><a href="#L-253"><span class="linenos">253</span></a>        
</span><span id="L-254"><a href="#L-254"><span class="linenos">254</span></a>        <span class="n">lines</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_config</span><span class="p">()</span>
</span><span id="L-255"><a href="#L-255"><span class="linenos">255</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="L-256"><a href="#L-256"><span class="linenos">256</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-257"><a href="#L-257"><span class="linenos">257</span></a>        
</span><span id="L-258"><a href="#L-258"><span class="linenos">258</span></a>        <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">expected_value</span> <span class="ow">in</span> <span class="n">required_configs</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><span id="L-259"><a href="#L-259"><span class="linenos">259</span></a>            <span class="n">line_index</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_config_line</span><span class="p">(</span><span class="n">lines</span><span class="p">,</span> <span class="n">key</span><span class="p">)</span>
</span><span id="L-260"><a href="#L-260"><span class="linenos">260</span></a>            <span class="k">if</span> <span class="n">line_index</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="L-261"><a href="#L-261"><span class="linenos">261</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;缺少配置项: </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-262"><a href="#L-262"><span class="linenos">262</span></a>                <span class="k">continue</span>
</span><span id="L-263"><a href="#L-263"><span class="linenos">263</span></a>            
</span><span id="L-264"><a href="#L-264"><span class="linenos">264</span></a>            <span class="n">parsed</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parse_config_line</span><span class="p">(</span><span class="n">lines</span><span class="p">[</span><span class="n">line_index</span><span class="p">])</span>
</span><span id="L-265"><a href="#L-265"><span class="linenos">265</span></a>            <span class="k">if</span> <span class="n">parsed</span> <span class="ow">and</span> <span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;value&#39;</span><span class="p">]</span> <span class="o">!=</span> <span class="n">expected_value</span><span class="p">:</span>
</span><span id="L-266"><a href="#L-266"><span class="linenos">266</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置项 </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2"> 值不匹配: 期望 </span><span class="si">{</span><span class="n">expected_value</span><span class="si">}</span><span class="s2">, 实际 </span><span class="si">{</span><span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;value&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-267"><a href="#L-267"><span class="linenos">267</span></a>        
</span><span id="L-268"><a href="#L-268"><span class="linenos">268</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;配置验证完成&quot;</span><span class="p">)</span>
</span><span id="L-269"><a href="#L-269"><span class="linenos">269</span></a>        <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-270"><a href="#L-270"><span class="linenos">270</span></a>    
</span><span id="L-271"><a href="#L-271"><span class="linenos">271</span></a>    <span class="k">def</span> <span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">dry_run</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span> <span class="n">restore</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-272"><a href="#L-272"><span class="linenos">272</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;运行配置程序&quot;&quot;&quot;</span>
</span><span id="L-273"><a href="#L-273"><span class="linenos">273</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;=== 树莓派HDMI配置优化工具 ===&quot;</span><span class="p">)</span>
</span><span id="L-274"><a href="#L-274"><span class="linenos">274</span></a>        
</span><span id="L-275"><a href="#L-275"><span class="linenos">275</span></a>        <span class="c1"># 检查权限</span>
</span><span id="L-276"><a href="#L-276"><span class="linenos">276</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">check_permissions</span><span class="p">():</span>
</span><span id="L-277"><a href="#L-277"><span class="linenos">277</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-278"><a href="#L-278"><span class="linenos">278</span></a>        
</span><span id="L-279"><a href="#L-279"><span class="linenos">279</span></a>        <span class="c1"># 恢复备份</span>
</span><span id="L-280"><a href="#L-280"><span class="linenos">280</span></a>        <span class="k">if</span> <span class="n">restore</span><span class="p">:</span>
</span><span id="L-281"><a href="#L-281"><span class="linenos">281</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">restore_backup</span><span class="p">()</span>
</span><span id="L-282"><a href="#L-282"><span class="linenos">282</span></a>        
</span><span id="L-283"><a href="#L-283"><span class="linenos">283</span></a>        <span class="c1"># 显示当前配置</span>
</span><span id="L-284"><a href="#L-284"><span class="linenos">284</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">show_current_config</span><span class="p">()</span>
</span><span id="L-285"><a href="#L-285"><span class="linenos">285</span></a>        
</span><span id="L-286"><a href="#L-286"><span class="linenos">286</span></a>        <span class="c1"># 显示将要应用的更改</span>
</span><span id="L-287"><a href="#L-287"><span class="linenos">287</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">show_changes</span><span class="p">()</span>
</span><span id="L-288"><a href="#L-288"><span class="linenos">288</span></a>        
</span><span id="L-289"><a href="#L-289"><span class="linenos">289</span></a>        <span class="k">if</span> <span class="n">dry_run</span><span class="p">:</span>
</span><span id="L-290"><a href="#L-290"><span class="linenos">290</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;模拟运行模式，不会实际修改配置文件&quot;</span><span class="p">)</span>
</span><span id="L-291"><a href="#L-291"><span class="linenos">291</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-292"><a href="#L-292"><span class="linenos">292</span></a>        
</span><span id="L-293"><a href="#L-293"><span class="linenos">293</span></a>        <span class="c1"># 确认操作</span>
</span><span id="L-294"><a href="#L-294"><span class="linenos">294</span></a>        <span class="n">confirm</span> <span class="o">=</span> <span class="nb">input</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">确认应用这些配置？(y/N): &quot;</span><span class="p">)</span>
</span><span id="L-295"><a href="#L-295"><span class="linenos">295</span></a>        <span class="k">if</span> <span class="n">confirm</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="o">!=</span> <span class="s1">&#39;y&#39;</span><span class="p">:</span>
</span><span id="L-296"><a href="#L-296"><span class="linenos">296</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;操作已取消&quot;</span><span class="p">)</span>
</span><span id="L-297"><a href="#L-297"><span class="linenos">297</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-298"><a href="#L-298"><span class="linenos">298</span></a>        
</span><span id="L-299"><a href="#L-299"><span class="linenos">299</span></a>        <span class="c1"># 备份配置</span>
</span><span id="L-300"><a href="#L-300"><span class="linenos">300</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">backup_config</span><span class="p">():</span>
</span><span id="L-301"><a href="#L-301"><span class="linenos">301</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-302"><a href="#L-302"><span class="linenos">302</span></a>        
</span><span id="L-303"><a href="#L-303"><span class="linenos">303</span></a>        <span class="c1"># 应用配置</span>
</span><span id="L-304"><a href="#L-304"><span class="linenos">304</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">apply_hdmi_configs</span><span class="p">():</span>
</span><span id="L-305"><a href="#L-305"><span class="linenos">305</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-306"><a href="#L-306"><span class="linenos">306</span></a>        
</span><span id="L-307"><a href="#L-307"><span class="linenos">307</span></a>        <span class="c1"># 验证配置</span>
</span><span id="L-308"><a href="#L-308"><span class="linenos">308</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">validate_config</span><span class="p">()</span>
</span><span id="L-309"><a href="#L-309"><span class="linenos">309</span></a>        
</span><span id="L-310"><a href="#L-310"><span class="linenos">310</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;=== 配置完成 ===&quot;</span><span class="p">)</span>
</span><span id="L-311"><a href="#L-311"><span class="linenos">311</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;请重启树莓派以应用新配置&quot;</span><span class="p">)</span>
</span><span id="L-312"><a href="#L-312"><span class="linenos">312</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;重启命令: sudo reboot&quot;</span><span class="p">)</span>
</span><span id="L-313"><a href="#L-313"><span class="linenos">313</span></a>        
</span><span id="L-314"><a href="#L-314"><span class="linenos">314</span></a>        <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-315"><a href="#L-315"><span class="linenos">315</span></a>
</span><span id="L-316"><a href="#L-316"><span class="linenos">316</span></a><span class="k">def</span> <span class="nf">main</span><span class="p">():</span>
</span><span id="L-317"><a href="#L-317"><span class="linenos">317</span></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;主函数&quot;&quot;&quot;</span>
</span><span id="L-318"><a href="#L-318"><span class="linenos">318</span></a>    <span class="n">parser</span> <span class="o">=</span> <span class="n">argparse</span><span class="o">.</span><span class="n">ArgumentParser</span><span class="p">(</span><span class="n">description</span><span class="o">=</span><span class="s2">&quot;树莓派HDMI配置优化工具&quot;</span><span class="p">)</span>
</span><span id="L-319"><a href="#L-319"><span class="linenos">319</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--config&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="s2">&quot;/boot/config.txt&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;配置文件路径&quot;</span><span class="p">)</span>
</span><span id="L-320"><a href="#L-320"><span class="linenos">320</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--dry-run&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;模拟运行，不实际修改文件&quot;</span><span class="p">)</span>
</span><span id="L-321"><a href="#L-321"><span class="linenos">321</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--restore&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;恢复原始配置&quot;</span><span class="p">)</span>
</span><span id="L-322"><a href="#L-322"><span class="linenos">322</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--show&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;显示当前配置&quot;</span><span class="p">)</span>
</span><span id="L-323"><a href="#L-323"><span class="linenos">323</span></a>    
</span><span id="L-324"><a href="#L-324"><span class="linenos">324</span></a>    <span class="n">args</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>
</span><span id="L-325"><a href="#L-325"><span class="linenos">325</span></a>    
</span><span id="L-326"><a href="#L-326"><span class="linenos">326</span></a>    <span class="n">configurator</span> <span class="o">=</span> <span class="n">HDMIConfigurator</span><span class="p">(</span><span class="n">args</span><span class="o">.</span><span class="n">config</span><span class="p">)</span>
</span><span id="L-327"><a href="#L-327"><span class="linenos">327</span></a>    
</span><span id="L-328"><a href="#L-328"><span class="linenos">328</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">show</span><span class="p">:</span>
</span><span id="L-329"><a href="#L-329"><span class="linenos">329</span></a>        <span class="n">configurator</span><span class="o">.</span><span class="n">show_current_config</span><span class="p">()</span>
</span><span id="L-330"><a href="#L-330"><span class="linenos">330</span></a>        <span class="k">return</span>
</span><span id="L-331"><a href="#L-331"><span class="linenos">331</span></a>    
</span><span id="L-332"><a href="#L-332"><span class="linenos">332</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">restore</span><span class="p">:</span>
</span><span id="L-333"><a href="#L-333"><span class="linenos">333</span></a>        <span class="n">configurator</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">restore</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="L-334"><a href="#L-334"><span class="linenos">334</span></a>        <span class="k">return</span>
</span><span id="L-335"><a href="#L-335"><span class="linenos">335</span></a>    
</span><span id="L-336"><a href="#L-336"><span class="linenos">336</span></a>    <span class="n">success</span> <span class="o">=</span> <span class="n">configurator</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">dry_run</span><span class="o">=</span><span class="n">args</span><span class="o">.</span><span class="n">dry_run</span><span class="p">)</span>
</span><span id="L-337"><a href="#L-337"><span class="linenos">337</span></a>    
</span><span id="L-338"><a href="#L-338"><span class="linenos">338</span></a>    <span class="k">if</span> <span class="n">success</span><span class="p">:</span>
</span><span id="L-339"><a href="#L-339"><span class="linenos">339</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">🎉 HDMI配置优化完成！&quot;</span><span class="p">)</span>
</span><span id="L-340"><a href="#L-340"><span class="linenos">340</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">args</span><span class="o">.</span><span class="n">dry_run</span><span class="p">:</span>
</span><span id="L-341"><a href="#L-341"><span class="linenos">341</span></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;请重启树莓派以应用新配置&quot;</span><span class="p">)</span>
</span><span id="L-342"><a href="#L-342"><span class="linenos">342</span></a>    <span class="k">else</span><span class="p">:</span>
</span><span id="L-343"><a href="#L-343"><span class="linenos">343</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">❌ 配置失败，请查看日志文件&quot;</span><span class="p">)</span>
</span><span id="L-344"><a href="#L-344"><span class="linenos">344</span></a>
</span><span id="L-345"><a href="#L-345"><span class="linenos">345</span></a><span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
</span><span id="L-346"><a href="#L-346"><span class="linenos">346</span></a>    <span class="n">main</span><span class="p">()</span> 
</span></pre></div>


            </section>
                <section id="logger">
                    <div class="attr variable">
            <span class="name">logger</span>        =
<span class="default_value">&lt;Logger hdmi_config (INFO)&gt;</span>

        
    </div>
    <a class="headerlink" href="#logger"></a>
    
    

                </section>
                <section id="HDMIConfigurator">
                            <input id="HDMIConfigurator-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr class">
            
    <span class="def">class</span>
    <span class="name">HDMIConfigurator</span>:

                <label class="view-source-button" for="HDMIConfigurator-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator-47"><a href="#HDMIConfigurator-47"><span class="linenos"> 47</span></a><span class="k">class</span> <span class="nc">HDMIConfigurator</span><span class="p">:</span>
</span><span id="HDMIConfigurator-48"><a href="#HDMIConfigurator-48"><span class="linenos"> 48</span></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-49"><a href="#HDMIConfigurator-49"><span class="linenos"> 49</span></a><span class="sd">    HDMI配置器类</span>
</span><span id="HDMIConfigurator-50"><a href="#HDMIConfigurator-50"><span class="linenos"> 50</span></a><span class="sd">    </span>
</span><span id="HDMIConfigurator-51"><a href="#HDMIConfigurator-51"><span class="linenos"> 51</span></a><span class="sd">    提供完整的树莓派HDMI配置优化功能，包括：</span>
</span><span id="HDMIConfigurator-52"><a href="#HDMIConfigurator-52"><span class="linenos"> 52</span></a><span class="sd">    - 配置文件读取和解析</span>
</span><span id="HDMIConfigurator-53"><a href="#HDMIConfigurator-53"><span class="linenos"> 53</span></a><span class="sd">    - 自动备份和恢复</span>
</span><span id="HDMIConfigurator-54"><a href="#HDMIConfigurator-54"><span class="linenos"> 54</span></a><span class="sd">    - HDMI参数优化</span>
</span><span id="HDMIConfigurator-55"><a href="#HDMIConfigurator-55"><span class="linenos"> 55</span></a><span class="sd">    - 配置验证和预览</span>
</span><span id="HDMIConfigurator-56"><a href="#HDMIConfigurator-56"><span class="linenos"> 56</span></a><span class="sd">    - 错误处理和回滚</span>
</span><span id="HDMIConfigurator-57"><a href="#HDMIConfigurator-57"><span class="linenos"> 57</span></a><span class="sd">    </span>
</span><span id="HDMIConfigurator-58"><a href="#HDMIConfigurator-58"><span class="linenos"> 58</span></a><span class="sd">    属性:</span>
</span><span id="HDMIConfigurator-59"><a href="#HDMIConfigurator-59"><span class="linenos"> 59</span></a><span class="sd">        config_path (Path): 配置文件路径</span>
</span><span id="HDMIConfigurator-60"><a href="#HDMIConfigurator-60"><span class="linenos"> 60</span></a><span class="sd">        backup_path (Path): 备份文件路径</span>
</span><span id="HDMIConfigurator-61"><a href="#HDMIConfigurator-61"><span class="linenos"> 61</span></a><span class="sd">        hdmi_configs (Dict): HDMI配置项字典</span>
</span><span id="HDMIConfigurator-62"><a href="#HDMIConfigurator-62"><span class="linenos"> 62</span></a><span class="sd">    &quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-63"><a href="#HDMIConfigurator-63"><span class="linenos"> 63</span></a>    
</span><span id="HDMIConfigurator-64"><a href="#HDMIConfigurator-64"><span class="linenos"> 64</span></a>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">config_path</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;/boot/config.txt&quot;</span><span class="p">):</span>
</span><span id="HDMIConfigurator-65"><a href="#HDMIConfigurator-65"><span class="linenos"> 65</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-66"><a href="#HDMIConfigurator-66"><span class="linenos"> 66</span></a><span class="sd">        初始化HDMI配置器</span>
</span><span id="HDMIConfigurator-67"><a href="#HDMIConfigurator-67"><span class="linenos"> 67</span></a><span class="sd">        </span>
</span><span id="HDMIConfigurator-68"><a href="#HDMIConfigurator-68"><span class="linenos"> 68</span></a><span class="sd">        Args:</span>
</span><span id="HDMIConfigurator-69"><a href="#HDMIConfigurator-69"><span class="linenos"> 69</span></a><span class="sd">            config_path (str): 配置文件路径</span>
</span><span id="HDMIConfigurator-70"><a href="#HDMIConfigurator-70"><span class="linenos"> 70</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-71"><a href="#HDMIConfigurator-71"><span class="linenos"> 71</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">config_path</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">config_path</span><span class="p">)</span>
</span><span id="HDMIConfigurator-72"><a href="#HDMIConfigurator-72"><span class="linenos"> 72</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="o">.</span><span class="n">with_suffix</span><span class="p">(</span><span class="s1">&#39;.txt.backup&#39;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-73"><a href="#HDMIConfigurator-73"><span class="linenos"> 73</span></a>        
</span><span id="HDMIConfigurator-74"><a href="#HDMIConfigurator-74"><span class="linenos"> 74</span></a>        <span class="c1"># HDMI配置项</span>
</span><span id="HDMIConfigurator-75"><a href="#HDMIConfigurator-75"><span class="linenos"> 75</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">hdmi_configs</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="HDMIConfigurator-76"><a href="#HDMIConfigurator-76"><span class="linenos"> 76</span></a>            <span class="c1"># 强制HDMI输出为1080p@60Hz</span>
</span><span id="HDMIConfigurator-77"><a href="#HDMIConfigurator-77"><span class="linenos"> 77</span></a>            <span class="s2">&quot;hdmi_group&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator-78"><a href="#HDMIConfigurator-78"><span class="linenos"> 78</span></a>            <span class="s2">&quot;hdmi_mode&quot;</span><span class="p">:</span> <span class="s2">&quot;16&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator-79"><a href="#HDMIConfigurator-79"><span class="linenos"> 79</span></a>            <span class="s2">&quot;hdmi_force_hotplug&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator-80"><a href="#HDMIConfigurator-80"><span class="linenos"> 80</span></a>            <span class="s2">&quot;hdmi_drive&quot;</span><span class="p">:</span> <span class="s2">&quot;2&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator-81"><a href="#HDMIConfigurator-81"><span class="linenos"> 81</span></a>            
</span><span id="HDMIConfigurator-82"><a href="#HDMIConfigurator-82"><span class="linenos"> 82</span></a>            <span class="c1"># 禁用过扫描</span>
</span><span id="HDMIConfigurator-83"><a href="#HDMIConfigurator-83"><span class="linenos"> 83</span></a>            <span class="s2">&quot;disable_overscan&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator-84"><a href="#HDMIConfigurator-84"><span class="linenos"> 84</span></a>            
</span><span id="HDMIConfigurator-85"><a href="#HDMIConfigurator-85"><span class="linenos"> 85</span></a>            <span class="c1"># GPU显存配置</span>
</span><span id="HDMIConfigurator-86"><a href="#HDMIConfigurator-86"><span class="linenos"> 86</span></a>            <span class="s2">&quot;gpu_mem&quot;</span><span class="p">:</span> <span class="s2">&quot;256&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator-87"><a href="#HDMIConfigurator-87"><span class="linenos"> 87</span></a>            
</span><span id="HDMIConfigurator-88"><a href="#HDMIConfigurator-88"><span class="linenos"> 88</span></a>            <span class="c1"># 其他优化配置</span>
</span><span id="HDMIConfigurator-89"><a href="#HDMIConfigurator-89"><span class="linenos"> 89</span></a>            <span class="s2">&quot;hdmi_ignore_cec_init&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator-90"><a href="#HDMIConfigurator-90"><span class="linenos"> 90</span></a>            <span class="s2">&quot;hdmi_ignore_cec&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator-91"><a href="#HDMIConfigurator-91"><span class="linenos"> 91</span></a>            <span class="s2">&quot;config_hdmi_boost&quot;</span><span class="p">:</span> <span class="s2">&quot;4&quot;</span>
</span><span id="HDMIConfigurator-92"><a href="#HDMIConfigurator-92"><span class="linenos"> 92</span></a>        <span class="p">}</span>
</span><span id="HDMIConfigurator-93"><a href="#HDMIConfigurator-93"><span class="linenos"> 93</span></a>        
</span><span id="HDMIConfigurator-94"><a href="#HDMIConfigurator-94"><span class="linenos"> 94</span></a>    <span class="k">def</span> <span class="nf">check_permissions</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator-95"><a href="#HDMIConfigurator-95"><span class="linenos"> 95</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;检查文件权限&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-96"><a href="#HDMIConfigurator-96"><span class="linenos"> 96</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="HDMIConfigurator-97"><a href="#HDMIConfigurator-97"><span class="linenos"> 97</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置文件不存在: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-98"><a href="#HDMIConfigurator-98"><span class="linenos"> 98</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator-99"><a href="#HDMIConfigurator-99"><span class="linenos"> 99</span></a>        
</span><span id="HDMIConfigurator-100"><a href="#HDMIConfigurator-100"><span class="linenos">100</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">os</span><span class="o">.</span><span class="n">access</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">R_OK</span> <span class="o">|</span> <span class="n">os</span><span class="o">.</span><span class="n">W_OK</span><span class="p">):</span>
</span><span id="HDMIConfigurator-101"><a href="#HDMIConfigurator-101"><span class="linenos">101</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;没有配置文件读写权限: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-102"><a href="#HDMIConfigurator-102"><span class="linenos">102</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;请使用 sudo 运行此脚本&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-103"><a href="#HDMIConfigurator-103"><span class="linenos">103</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator-104"><a href="#HDMIConfigurator-104"><span class="linenos">104</span></a>        
</span><span id="HDMIConfigurator-105"><a href="#HDMIConfigurator-105"><span class="linenos">105</span></a>        <span class="k">return</span> <span class="kc">True</span>
</span><span id="HDMIConfigurator-106"><a href="#HDMIConfigurator-106"><span class="linenos">106</span></a>    
</span><span id="HDMIConfigurator-107"><a href="#HDMIConfigurator-107"><span class="linenos">107</span></a>    <span class="k">def</span> <span class="nf">backup_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator-108"><a href="#HDMIConfigurator-108"><span class="linenos">108</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;备份原始配置文件&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-109"><a href="#HDMIConfigurator-109"><span class="linenos">109</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="HDMIConfigurator-110"><a href="#HDMIConfigurator-110"><span class="linenos">110</span></a>            <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="HDMIConfigurator-111"><a href="#HDMIConfigurator-111"><span class="linenos">111</span></a>                <span class="n">shutil</span><span class="o">.</span><span class="n">copy2</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="p">)</span>
</span><span id="HDMIConfigurator-112"><a href="#HDMIConfigurator-112"><span class="linenos">112</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置文件已备份到: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-113"><a href="#HDMIConfigurator-113"><span class="linenos">113</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="HDMIConfigurator-114"><a href="#HDMIConfigurator-114"><span class="linenos">114</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;备份文件已存在: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-115"><a href="#HDMIConfigurator-115"><span class="linenos">115</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="HDMIConfigurator-116"><a href="#HDMIConfigurator-116"><span class="linenos">116</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="HDMIConfigurator-117"><a href="#HDMIConfigurator-117"><span class="linenos">117</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;备份配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-118"><a href="#HDMIConfigurator-118"><span class="linenos">118</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator-119"><a href="#HDMIConfigurator-119"><span class="linenos">119</span></a>    
</span><span id="HDMIConfigurator-120"><a href="#HDMIConfigurator-120"><span class="linenos">120</span></a>    <span class="k">def</span> <span class="nf">read_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="HDMIConfigurator-121"><a href="#HDMIConfigurator-121"><span class="linenos">121</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;读取配置文件&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-122"><a href="#HDMIConfigurator-122"><span class="linenos">122</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="HDMIConfigurator-123"><a href="#HDMIConfigurator-123"><span class="linenos">123</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">,</span> <span class="s1">&#39;r&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="HDMIConfigurator-124"><a href="#HDMIConfigurator-124"><span class="linenos">124</span></a>                <span class="k">return</span> <span class="n">f</span><span class="o">.</span><span class="n">readlines</span><span class="p">()</span>
</span><span id="HDMIConfigurator-125"><a href="#HDMIConfigurator-125"><span class="linenos">125</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="HDMIConfigurator-126"><a href="#HDMIConfigurator-126"><span class="linenos">126</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;读取配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-127"><a href="#HDMIConfigurator-127"><span class="linenos">127</span></a>            <span class="k">return</span> <span class="p">[]</span>
</span><span id="HDMIConfigurator-128"><a href="#HDMIConfigurator-128"><span class="linenos">128</span></a>    
</span><span id="HDMIConfigurator-129"><a href="#HDMIConfigurator-129"><span class="linenos">129</span></a>    <span class="k">def</span> <span class="nf">write_config</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lines</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator-130"><a href="#HDMIConfigurator-130"><span class="linenos">130</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;写入配置文件&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-131"><a href="#HDMIConfigurator-131"><span class="linenos">131</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="HDMIConfigurator-132"><a href="#HDMIConfigurator-132"><span class="linenos">132</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">,</span> <span class="s1">&#39;w&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="HDMIConfigurator-133"><a href="#HDMIConfigurator-133"><span class="linenos">133</span></a>                <span class="n">f</span><span class="o">.</span><span class="n">writelines</span><span class="p">(</span><span class="n">lines</span><span class="p">)</span>
</span><span id="HDMIConfigurator-134"><a href="#HDMIConfigurator-134"><span class="linenos">134</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;配置文件写入成功&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-135"><a href="#HDMIConfigurator-135"><span class="linenos">135</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="HDMIConfigurator-136"><a href="#HDMIConfigurator-136"><span class="linenos">136</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="HDMIConfigurator-137"><a href="#HDMIConfigurator-137"><span class="linenos">137</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;写入配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-138"><a href="#HDMIConfigurator-138"><span class="linenos">138</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator-139"><a href="#HDMIConfigurator-139"><span class="linenos">139</span></a>    
</span><span id="HDMIConfigurator-140"><a href="#HDMIConfigurator-140"><span class="linenos">140</span></a>    <span class="k">def</span> <span class="nf">parse_config_line</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">line</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]:</span>
</span><span id="HDMIConfigurator-141"><a href="#HDMIConfigurator-141"><span class="linenos">141</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;解析配置行&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-142"><a href="#HDMIConfigurator-142"><span class="linenos">142</span></a>        <span class="n">line</span> <span class="o">=</span> <span class="n">line</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>
</span><span id="HDMIConfigurator-143"><a href="#HDMIConfigurator-143"><span class="linenos">143</span></a>        
</span><span id="HDMIConfigurator-144"><a href="#HDMIConfigurator-144"><span class="linenos">144</span></a>        <span class="c1"># 跳过注释和空行</span>
</span><span id="HDMIConfigurator-145"><a href="#HDMIConfigurator-145"><span class="linenos">145</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">line</span> <span class="ow">or</span> <span class="n">line</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;#&#39;</span><span class="p">):</span>
</span><span id="HDMIConfigurator-146"><a href="#HDMIConfigurator-146"><span class="linenos">146</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="HDMIConfigurator-147"><a href="#HDMIConfigurator-147"><span class="linenos">147</span></a>        
</span><span id="HDMIConfigurator-148"><a href="#HDMIConfigurator-148"><span class="linenos">148</span></a>        <span class="c1"># 解析 key=value 格式</span>
</span><span id="HDMIConfigurator-149"><a href="#HDMIConfigurator-149"><span class="linenos">149</span></a>        <span class="k">if</span> <span class="s1">&#39;=&#39;</span> <span class="ow">in</span> <span class="n">line</span><span class="p">:</span>
</span><span id="HDMIConfigurator-150"><a href="#HDMIConfigurator-150"><span class="linenos">150</span></a>            <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="o">=</span> <span class="n">line</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;=&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
</span><span id="HDMIConfigurator-151"><a href="#HDMIConfigurator-151"><span class="linenos">151</span></a>            <span class="k">return</span> <span class="p">{</span>
</span><span id="HDMIConfigurator-152"><a href="#HDMIConfigurator-152"><span class="linenos">152</span></a>                <span class="s1">&#39;key&#39;</span><span class="p">:</span> <span class="n">key</span><span class="o">.</span><span class="n">strip</span><span class="p">(),</span>
</span><span id="HDMIConfigurator-153"><a href="#HDMIConfigurator-153"><span class="linenos">153</span></a>                <span class="s1">&#39;value&#39;</span><span class="p">:</span> <span class="n">value</span><span class="o">.</span><span class="n">strip</span><span class="p">(),</span>
</span><span id="HDMIConfigurator-154"><a href="#HDMIConfigurator-154"><span class="linenos">154</span></a>                <span class="s1">&#39;line&#39;</span><span class="p">:</span> <span class="n">line</span>
</span><span id="HDMIConfigurator-155"><a href="#HDMIConfigurator-155"><span class="linenos">155</span></a>            <span class="p">}</span>
</span><span id="HDMIConfigurator-156"><a href="#HDMIConfigurator-156"><span class="linenos">156</span></a>        
</span><span id="HDMIConfigurator-157"><a href="#HDMIConfigurator-157"><span class="linenos">157</span></a>        <span class="k">return</span> <span class="kc">None</span>
</span><span id="HDMIConfigurator-158"><a href="#HDMIConfigurator-158"><span class="linenos">158</span></a>    
</span><span id="HDMIConfigurator-159"><a href="#HDMIConfigurator-159"><span class="linenos">159</span></a>    <span class="k">def</span> <span class="nf">find_config_line</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lines</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="n">key</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]:</span>
</span><span id="HDMIConfigurator-160"><a href="#HDMIConfigurator-160"><span class="linenos">160</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;查找配置项的行号&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-161"><a href="#HDMIConfigurator-161"><span class="linenos">161</span></a>        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">line</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">lines</span><span class="p">):</span>
</span><span id="HDMIConfigurator-162"><a href="#HDMIConfigurator-162"><span class="linenos">162</span></a>            <span class="n">parsed</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parse_config_line</span><span class="p">(</span><span class="n">line</span><span class="p">)</span>
</span><span id="HDMIConfigurator-163"><a href="#HDMIConfigurator-163"><span class="linenos">163</span></a>            <span class="k">if</span> <span class="n">parsed</span> <span class="ow">and</span> <span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;key&#39;</span><span class="p">]</span> <span class="o">==</span> <span class="n">key</span><span class="p">:</span>
</span><span id="HDMIConfigurator-164"><a href="#HDMIConfigurator-164"><span class="linenos">164</span></a>                <span class="k">return</span> <span class="n">i</span>
</span><span id="HDMIConfigurator-165"><a href="#HDMIConfigurator-165"><span class="linenos">165</span></a>        <span class="k">return</span> <span class="kc">None</span>
</span><span id="HDMIConfigurator-166"><a href="#HDMIConfigurator-166"><span class="linenos">166</span></a>    
</span><span id="HDMIConfigurator-167"><a href="#HDMIConfigurator-167"><span class="linenos">167</span></a>    <span class="k">def</span> <span class="nf">update_config</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lines</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="n">key</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="HDMIConfigurator-168"><a href="#HDMIConfigurator-168"><span class="linenos">168</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;更新配置项&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-169"><a href="#HDMIConfigurator-169"><span class="linenos">169</span></a>        <span class="n">line_index</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_config_line</span><span class="p">(</span><span class="n">lines</span><span class="p">,</span> <span class="n">key</span><span class="p">)</span>
</span><span id="HDMIConfigurator-170"><a href="#HDMIConfigurator-170"><span class="linenos">170</span></a>        
</span><span id="HDMIConfigurator-171"><a href="#HDMIConfigurator-171"><span class="linenos">171</span></a>        <span class="k">if</span> <span class="n">line_index</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="HDMIConfigurator-172"><a href="#HDMIConfigurator-172"><span class="linenos">172</span></a>            <span class="c1"># 更新现有配置</span>
</span><span id="HDMIConfigurator-173"><a href="#HDMIConfigurator-173"><span class="linenos">173</span></a>            <span class="n">lines</span><span class="p">[</span><span class="n">line_index</span><span class="p">]</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="se">\n</span><span class="s2">&quot;</span>
</span><span id="HDMIConfigurator-174"><a href="#HDMIConfigurator-174"><span class="linenos">174</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;更新配置: </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-175"><a href="#HDMIConfigurator-175"><span class="linenos">175</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="HDMIConfigurator-176"><a href="#HDMIConfigurator-176"><span class="linenos">176</span></a>            <span class="c1"># 添加新配置</span>
</span><span id="HDMIConfigurator-177"><a href="#HDMIConfigurator-177"><span class="linenos">177</span></a>            <span class="n">lines</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-178"><a href="#HDMIConfigurator-178"><span class="linenos">178</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;添加配置: </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-179"><a href="#HDMIConfigurator-179"><span class="linenos">179</span></a>        
</span><span id="HDMIConfigurator-180"><a href="#HDMIConfigurator-180"><span class="linenos">180</span></a>        <span class="k">return</span> <span class="n">lines</span>
</span><span id="HDMIConfigurator-181"><a href="#HDMIConfigurator-181"><span class="linenos">181</span></a>    
</span><span id="HDMIConfigurator-182"><a href="#HDMIConfigurator-182"><span class="linenos">182</span></a>    <span class="k">def</span> <span class="nf">apply_hdmi_configs</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator-183"><a href="#HDMIConfigurator-183"><span class="linenos">183</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;应用HDMI配置&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-184"><a href="#HDMIConfigurator-184"><span class="linenos">184</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;开始应用HDMI配置...&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-185"><a href="#HDMIConfigurator-185"><span class="linenos">185</span></a>        
</span><span id="HDMIConfigurator-186"><a href="#HDMIConfigurator-186"><span class="linenos">186</span></a>        <span class="c1"># 读取现有配置</span>
</span><span id="HDMIConfigurator-187"><a href="#HDMIConfigurator-187"><span class="linenos">187</span></a>        <span class="n">lines</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_config</span><span class="p">()</span>
</span><span id="HDMIConfigurator-188"><a href="#HDMIConfigurator-188"><span class="linenos">188</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="HDMIConfigurator-189"><a href="#HDMIConfigurator-189"><span class="linenos">189</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator-190"><a href="#HDMIConfigurator-190"><span class="linenos">190</span></a>        
</span><span id="HDMIConfigurator-191"><a href="#HDMIConfigurator-191"><span class="linenos">191</span></a>        <span class="c1"># 应用每个配置项</span>
</span><span id="HDMIConfigurator-192"><a href="#HDMIConfigurator-192"><span class="linenos">192</span></a>        <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">hdmi_configs</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><span id="HDMIConfigurator-193"><a href="#HDMIConfigurator-193"><span class="linenos">193</span></a>            <span class="n">lines</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">update_config</span><span class="p">(</span><span class="n">lines</span><span class="p">,</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
</span><span id="HDMIConfigurator-194"><a href="#HDMIConfigurator-194"><span class="linenos">194</span></a>        
</span><span id="HDMIConfigurator-195"><a href="#HDMIConfigurator-195"><span class="linenos">195</span></a>        <span class="c1"># 写入配置文件</span>
</span><span id="HDMIConfigurator-196"><a href="#HDMIConfigurator-196"><span class="linenos">196</span></a>        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">write_config</span><span class="p">(</span><span class="n">lines</span><span class="p">)</span>
</span><span id="HDMIConfigurator-197"><a href="#HDMIConfigurator-197"><span class="linenos">197</span></a>    
</span><span id="HDMIConfigurator-198"><a href="#HDMIConfigurator-198"><span class="linenos">198</span></a>    <span class="k">def</span> <span class="nf">restore_backup</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator-199"><a href="#HDMIConfigurator-199"><span class="linenos">199</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;恢复备份配置&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-200"><a href="#HDMIConfigurator-200"><span class="linenos">200</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="HDMIConfigurator-201"><a href="#HDMIConfigurator-201"><span class="linenos">201</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;备份文件不存在&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-202"><a href="#HDMIConfigurator-202"><span class="linenos">202</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator-203"><a href="#HDMIConfigurator-203"><span class="linenos">203</span></a>        
</span><span id="HDMIConfigurator-204"><a href="#HDMIConfigurator-204"><span class="linenos">204</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="HDMIConfigurator-205"><a href="#HDMIConfigurator-205"><span class="linenos">205</span></a>            <span class="n">shutil</span><span class="o">.</span><span class="n">copy2</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">)</span>
</span><span id="HDMIConfigurator-206"><a href="#HDMIConfigurator-206"><span class="linenos">206</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;已恢复原始配置&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-207"><a href="#HDMIConfigurator-207"><span class="linenos">207</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="HDMIConfigurator-208"><a href="#HDMIConfigurator-208"><span class="linenos">208</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="HDMIConfigurator-209"><a href="#HDMIConfigurator-209"><span class="linenos">209</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;恢复配置失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-210"><a href="#HDMIConfigurator-210"><span class="linenos">210</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator-211"><a href="#HDMIConfigurator-211"><span class="linenos">211</span></a>    
</span><span id="HDMIConfigurator-212"><a href="#HDMIConfigurator-212"><span class="linenos">212</span></a>    <span class="k">def</span> <span class="nf">show_current_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="HDMIConfigurator-213"><a href="#HDMIConfigurator-213"><span class="linenos">213</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;显示当前配置&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-214"><a href="#HDMIConfigurator-214"><span class="linenos">214</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;当前HDMI相关配置:&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-215"><a href="#HDMIConfigurator-215"><span class="linenos">215</span></a>        
</span><span id="HDMIConfigurator-216"><a href="#HDMIConfigurator-216"><span class="linenos">216</span></a>        <span class="n">lines</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_config</span><span class="p">()</span>
</span><span id="HDMIConfigurator-217"><a href="#HDMIConfigurator-217"><span class="linenos">217</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="HDMIConfigurator-218"><a href="#HDMIConfigurator-218"><span class="linenos">218</span></a>            <span class="k">return</span>
</span><span id="HDMIConfigurator-219"><a href="#HDMIConfigurator-219"><span class="linenos">219</span></a>        
</span><span id="HDMIConfigurator-220"><a href="#HDMIConfigurator-220"><span class="linenos">220</span></a>        <span class="n">hdmi_keys</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">hdmi_configs</span><span class="o">.</span><span class="n">keys</span><span class="p">())</span>
</span><span id="HDMIConfigurator-221"><a href="#HDMIConfigurator-221"><span class="linenos">221</span></a>        
</span><span id="HDMIConfigurator-222"><a href="#HDMIConfigurator-222"><span class="linenos">222</span></a>        <span class="k">for</span> <span class="n">line</span> <span class="ow">in</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="HDMIConfigurator-223"><a href="#HDMIConfigurator-223"><span class="linenos">223</span></a>            <span class="n">parsed</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parse_config_line</span><span class="p">(</span><span class="n">line</span><span class="p">)</span>
</span><span id="HDMIConfigurator-224"><a href="#HDMIConfigurator-224"><span class="linenos">224</span></a>            <span class="k">if</span> <span class="n">parsed</span> <span class="ow">and</span> <span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;key&#39;</span><span class="p">]</span> <span class="ow">in</span> <span class="n">hdmi_keys</span><span class="p">:</span>
</span><span id="HDMIConfigurator-225"><a href="#HDMIConfigurator-225"><span class="linenos">225</span></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  </span><span class="si">{</span><span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;key&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;value&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-226"><a href="#HDMIConfigurator-226"><span class="linenos">226</span></a>    
</span><span id="HDMIConfigurator-227"><a href="#HDMIConfigurator-227"><span class="linenos">227</span></a>    <span class="k">def</span> <span class="nf">show_changes</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="HDMIConfigurator-228"><a href="#HDMIConfigurator-228"><span class="linenos">228</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;显示将要应用的更改&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-229"><a href="#HDMIConfigurator-229"><span class="linenos">229</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;将要应用的HDMI配置:&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-230"><a href="#HDMIConfigurator-230"><span class="linenos">230</span></a>        <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">hdmi_configs</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><span id="HDMIConfigurator-231"><a href="#HDMIConfigurator-231"><span class="linenos">231</span></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-232"><a href="#HDMIConfigurator-232"><span class="linenos">232</span></a>        
</span><span id="HDMIConfigurator-233"><a href="#HDMIConfigurator-233"><span class="linenos">233</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">配置说明:&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-234"><a href="#HDMIConfigurator-234"><span class="linenos">234</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_group=1: HDMI组1（CEA标准）&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-235"><a href="#HDMIConfigurator-235"><span class="linenos">235</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_mode=16: 1080p@60Hz&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-236"><a href="#HDMIConfigurator-236"><span class="linenos">236</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_force_hotplug=1: 强制HDMI热插拔检测&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-237"><a href="#HDMIConfigurator-237"><span class="linenos">237</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_drive=2: HDMI驱动强度&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-238"><a href="#HDMIConfigurator-238"><span class="linenos">238</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  disable_overscan=1: 禁用过扫描&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-239"><a href="#HDMIConfigurator-239"><span class="linenos">239</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  gpu_mem=256: GPU显存256MB&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-240"><a href="#HDMIConfigurator-240"><span class="linenos">240</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_ignore_cec_init=1: 忽略CEC初始化&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-241"><a href="#HDMIConfigurator-241"><span class="linenos">241</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_ignore_cec=1: 忽略CEC&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-242"><a href="#HDMIConfigurator-242"><span class="linenos">242</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  config_hdmi_boost=4: HDMI信号增强&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-243"><a href="#HDMIConfigurator-243"><span class="linenos">243</span></a>    
</span><span id="HDMIConfigurator-244"><a href="#HDMIConfigurator-244"><span class="linenos">244</span></a>    <span class="k">def</span> <span class="nf">validate_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator-245"><a href="#HDMIConfigurator-245"><span class="linenos">245</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;验证配置&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-246"><a href="#HDMIConfigurator-246"><span class="linenos">246</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;验证配置...&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-247"><a href="#HDMIConfigurator-247"><span class="linenos">247</span></a>        
</span><span id="HDMIConfigurator-248"><a href="#HDMIConfigurator-248"><span class="linenos">248</span></a>        <span class="c1"># 检查关键配置项</span>
</span><span id="HDMIConfigurator-249"><a href="#HDMIConfigurator-249"><span class="linenos">249</span></a>        <span class="n">required_configs</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="HDMIConfigurator-250"><a href="#HDMIConfigurator-250"><span class="linenos">250</span></a>            <span class="s1">&#39;hdmi_group&#39;</span><span class="p">:</span> <span class="s1">&#39;1&#39;</span><span class="p">,</span>
</span><span id="HDMIConfigurator-251"><a href="#HDMIConfigurator-251"><span class="linenos">251</span></a>            <span class="s1">&#39;hdmi_mode&#39;</span><span class="p">:</span> <span class="s1">&#39;16&#39;</span><span class="p">,</span>
</span><span id="HDMIConfigurator-252"><a href="#HDMIConfigurator-252"><span class="linenos">252</span></a>            <span class="s1">&#39;gpu_mem&#39;</span><span class="p">:</span> <span class="s1">&#39;256&#39;</span>
</span><span id="HDMIConfigurator-253"><a href="#HDMIConfigurator-253"><span class="linenos">253</span></a>        <span class="p">}</span>
</span><span id="HDMIConfigurator-254"><a href="#HDMIConfigurator-254"><span class="linenos">254</span></a>        
</span><span id="HDMIConfigurator-255"><a href="#HDMIConfigurator-255"><span class="linenos">255</span></a>        <span class="n">lines</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_config</span><span class="p">()</span>
</span><span id="HDMIConfigurator-256"><a href="#HDMIConfigurator-256"><span class="linenos">256</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="HDMIConfigurator-257"><a href="#HDMIConfigurator-257"><span class="linenos">257</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator-258"><a href="#HDMIConfigurator-258"><span class="linenos">258</span></a>        
</span><span id="HDMIConfigurator-259"><a href="#HDMIConfigurator-259"><span class="linenos">259</span></a>        <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">expected_value</span> <span class="ow">in</span> <span class="n">required_configs</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><span id="HDMIConfigurator-260"><a href="#HDMIConfigurator-260"><span class="linenos">260</span></a>            <span class="n">line_index</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_config_line</span><span class="p">(</span><span class="n">lines</span><span class="p">,</span> <span class="n">key</span><span class="p">)</span>
</span><span id="HDMIConfigurator-261"><a href="#HDMIConfigurator-261"><span class="linenos">261</span></a>            <span class="k">if</span> <span class="n">line_index</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="HDMIConfigurator-262"><a href="#HDMIConfigurator-262"><span class="linenos">262</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;缺少配置项: </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-263"><a href="#HDMIConfigurator-263"><span class="linenos">263</span></a>                <span class="k">continue</span>
</span><span id="HDMIConfigurator-264"><a href="#HDMIConfigurator-264"><span class="linenos">264</span></a>            
</span><span id="HDMIConfigurator-265"><a href="#HDMIConfigurator-265"><span class="linenos">265</span></a>            <span class="n">parsed</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parse_config_line</span><span class="p">(</span><span class="n">lines</span><span class="p">[</span><span class="n">line_index</span><span class="p">])</span>
</span><span id="HDMIConfigurator-266"><a href="#HDMIConfigurator-266"><span class="linenos">266</span></a>            <span class="k">if</span> <span class="n">parsed</span> <span class="ow">and</span> <span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;value&#39;</span><span class="p">]</span> <span class="o">!=</span> <span class="n">expected_value</span><span class="p">:</span>
</span><span id="HDMIConfigurator-267"><a href="#HDMIConfigurator-267"><span class="linenos">267</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置项 </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2"> 值不匹配: 期望 </span><span class="si">{</span><span class="n">expected_value</span><span class="si">}</span><span class="s2">, 实际 </span><span class="si">{</span><span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;value&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-268"><a href="#HDMIConfigurator-268"><span class="linenos">268</span></a>        
</span><span id="HDMIConfigurator-269"><a href="#HDMIConfigurator-269"><span class="linenos">269</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;配置验证完成&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-270"><a href="#HDMIConfigurator-270"><span class="linenos">270</span></a>        <span class="k">return</span> <span class="kc">True</span>
</span><span id="HDMIConfigurator-271"><a href="#HDMIConfigurator-271"><span class="linenos">271</span></a>    
</span><span id="HDMIConfigurator-272"><a href="#HDMIConfigurator-272"><span class="linenos">272</span></a>    <span class="k">def</span> <span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">dry_run</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span> <span class="n">restore</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator-273"><a href="#HDMIConfigurator-273"><span class="linenos">273</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;运行配置程序&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator-274"><a href="#HDMIConfigurator-274"><span class="linenos">274</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;=== 树莓派HDMI配置优化工具 ===&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-275"><a href="#HDMIConfigurator-275"><span class="linenos">275</span></a>        
</span><span id="HDMIConfigurator-276"><a href="#HDMIConfigurator-276"><span class="linenos">276</span></a>        <span class="c1"># 检查权限</span>
</span><span id="HDMIConfigurator-277"><a href="#HDMIConfigurator-277"><span class="linenos">277</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">check_permissions</span><span class="p">():</span>
</span><span id="HDMIConfigurator-278"><a href="#HDMIConfigurator-278"><span class="linenos">278</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator-279"><a href="#HDMIConfigurator-279"><span class="linenos">279</span></a>        
</span><span id="HDMIConfigurator-280"><a href="#HDMIConfigurator-280"><span class="linenos">280</span></a>        <span class="c1"># 恢复备份</span>
</span><span id="HDMIConfigurator-281"><a href="#HDMIConfigurator-281"><span class="linenos">281</span></a>        <span class="k">if</span> <span class="n">restore</span><span class="p">:</span>
</span><span id="HDMIConfigurator-282"><a href="#HDMIConfigurator-282"><span class="linenos">282</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">restore_backup</span><span class="p">()</span>
</span><span id="HDMIConfigurator-283"><a href="#HDMIConfigurator-283"><span class="linenos">283</span></a>        
</span><span id="HDMIConfigurator-284"><a href="#HDMIConfigurator-284"><span class="linenos">284</span></a>        <span class="c1"># 显示当前配置</span>
</span><span id="HDMIConfigurator-285"><a href="#HDMIConfigurator-285"><span class="linenos">285</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">show_current_config</span><span class="p">()</span>
</span><span id="HDMIConfigurator-286"><a href="#HDMIConfigurator-286"><span class="linenos">286</span></a>        
</span><span id="HDMIConfigurator-287"><a href="#HDMIConfigurator-287"><span class="linenos">287</span></a>        <span class="c1"># 显示将要应用的更改</span>
</span><span id="HDMIConfigurator-288"><a href="#HDMIConfigurator-288"><span class="linenos">288</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">show_changes</span><span class="p">()</span>
</span><span id="HDMIConfigurator-289"><a href="#HDMIConfigurator-289"><span class="linenos">289</span></a>        
</span><span id="HDMIConfigurator-290"><a href="#HDMIConfigurator-290"><span class="linenos">290</span></a>        <span class="k">if</span> <span class="n">dry_run</span><span class="p">:</span>
</span><span id="HDMIConfigurator-291"><a href="#HDMIConfigurator-291"><span class="linenos">291</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;模拟运行模式，不会实际修改配置文件&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-292"><a href="#HDMIConfigurator-292"><span class="linenos">292</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="HDMIConfigurator-293"><a href="#HDMIConfigurator-293"><span class="linenos">293</span></a>        
</span><span id="HDMIConfigurator-294"><a href="#HDMIConfigurator-294"><span class="linenos">294</span></a>        <span class="c1"># 确认操作</span>
</span><span id="HDMIConfigurator-295"><a href="#HDMIConfigurator-295"><span class="linenos">295</span></a>        <span class="n">confirm</span> <span class="o">=</span> <span class="nb">input</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">确认应用这些配置？(y/N): &quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-296"><a href="#HDMIConfigurator-296"><span class="linenos">296</span></a>        <span class="k">if</span> <span class="n">confirm</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="o">!=</span> <span class="s1">&#39;y&#39;</span><span class="p">:</span>
</span><span id="HDMIConfigurator-297"><a href="#HDMIConfigurator-297"><span class="linenos">297</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;操作已取消&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-298"><a href="#HDMIConfigurator-298"><span class="linenos">298</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator-299"><a href="#HDMIConfigurator-299"><span class="linenos">299</span></a>        
</span><span id="HDMIConfigurator-300"><a href="#HDMIConfigurator-300"><span class="linenos">300</span></a>        <span class="c1"># 备份配置</span>
</span><span id="HDMIConfigurator-301"><a href="#HDMIConfigurator-301"><span class="linenos">301</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">backup_config</span><span class="p">():</span>
</span><span id="HDMIConfigurator-302"><a href="#HDMIConfigurator-302"><span class="linenos">302</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator-303"><a href="#HDMIConfigurator-303"><span class="linenos">303</span></a>        
</span><span id="HDMIConfigurator-304"><a href="#HDMIConfigurator-304"><span class="linenos">304</span></a>        <span class="c1"># 应用配置</span>
</span><span id="HDMIConfigurator-305"><a href="#HDMIConfigurator-305"><span class="linenos">305</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">apply_hdmi_configs</span><span class="p">():</span>
</span><span id="HDMIConfigurator-306"><a href="#HDMIConfigurator-306"><span class="linenos">306</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator-307"><a href="#HDMIConfigurator-307"><span class="linenos">307</span></a>        
</span><span id="HDMIConfigurator-308"><a href="#HDMIConfigurator-308"><span class="linenos">308</span></a>        <span class="c1"># 验证配置</span>
</span><span id="HDMIConfigurator-309"><a href="#HDMIConfigurator-309"><span class="linenos">309</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">validate_config</span><span class="p">()</span>
</span><span id="HDMIConfigurator-310"><a href="#HDMIConfigurator-310"><span class="linenos">310</span></a>        
</span><span id="HDMIConfigurator-311"><a href="#HDMIConfigurator-311"><span class="linenos">311</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;=== 配置完成 ===&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-312"><a href="#HDMIConfigurator-312"><span class="linenos">312</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;请重启树莓派以应用新配置&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-313"><a href="#HDMIConfigurator-313"><span class="linenos">313</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;重启命令: sudo reboot&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator-314"><a href="#HDMIConfigurator-314"><span class="linenos">314</span></a>        
</span><span id="HDMIConfigurator-315"><a href="#HDMIConfigurator-315"><span class="linenos">315</span></a>        <span class="k">return</span> <span class="kc">True</span>
</span></pre></div>


            <div class="docstring"><p>HDMI配置器类</p>

<p>提供完整的树莓派HDMI配置优化功能，包括：</p>

<ul>
<li>配置文件读取和解析</li>
<li>自动备份和恢复</li>
<li>HDMI参数优化</li>
<li>配置验证和预览</li>
<li>错误处理和回滚</li>
</ul>

<p>属性:
    config_path (Path): 配置文件路径
    backup_path (Path): 备份文件路径
    hdmi_configs (Dict): HDMI配置项字典</p>
</div>


                            <div id="HDMIConfigurator.__init__" class="classattr">
                                        <input id="HDMIConfigurator.__init__-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="name">HDMIConfigurator</span><span class="signature pdoc-code condensed">(<span class="param"><span class="n">config_path</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s1">&#39;/boot/config.txt&#39;</span></span>)</span>

                <label class="view-source-button" for="HDMIConfigurator.__init__-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.__init__"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.__init__-64"><a href="#HDMIConfigurator.__init__-64"><span class="linenos">64</span></a>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">config_path</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;/boot/config.txt&quot;</span><span class="p">):</span>
</span><span id="HDMIConfigurator.__init__-65"><a href="#HDMIConfigurator.__init__-65"><span class="linenos">65</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.__init__-66"><a href="#HDMIConfigurator.__init__-66"><span class="linenos">66</span></a><span class="sd">        初始化HDMI配置器</span>
</span><span id="HDMIConfigurator.__init__-67"><a href="#HDMIConfigurator.__init__-67"><span class="linenos">67</span></a><span class="sd">        </span>
</span><span id="HDMIConfigurator.__init__-68"><a href="#HDMIConfigurator.__init__-68"><span class="linenos">68</span></a><span class="sd">        Args:</span>
</span><span id="HDMIConfigurator.__init__-69"><a href="#HDMIConfigurator.__init__-69"><span class="linenos">69</span></a><span class="sd">            config_path (str): 配置文件路径</span>
</span><span id="HDMIConfigurator.__init__-70"><a href="#HDMIConfigurator.__init__-70"><span class="linenos">70</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.__init__-71"><a href="#HDMIConfigurator.__init__-71"><span class="linenos">71</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">config_path</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">config_path</span><span class="p">)</span>
</span><span id="HDMIConfigurator.__init__-72"><a href="#HDMIConfigurator.__init__-72"><span class="linenos">72</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="o">.</span><span class="n">with_suffix</span><span class="p">(</span><span class="s1">&#39;.txt.backup&#39;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.__init__-73"><a href="#HDMIConfigurator.__init__-73"><span class="linenos">73</span></a>        
</span><span id="HDMIConfigurator.__init__-74"><a href="#HDMIConfigurator.__init__-74"><span class="linenos">74</span></a>        <span class="c1"># HDMI配置项</span>
</span><span id="HDMIConfigurator.__init__-75"><a href="#HDMIConfigurator.__init__-75"><span class="linenos">75</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">hdmi_configs</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="HDMIConfigurator.__init__-76"><a href="#HDMIConfigurator.__init__-76"><span class="linenos">76</span></a>            <span class="c1"># 强制HDMI输出为1080p@60Hz</span>
</span><span id="HDMIConfigurator.__init__-77"><a href="#HDMIConfigurator.__init__-77"><span class="linenos">77</span></a>            <span class="s2">&quot;hdmi_group&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator.__init__-78"><a href="#HDMIConfigurator.__init__-78"><span class="linenos">78</span></a>            <span class="s2">&quot;hdmi_mode&quot;</span><span class="p">:</span> <span class="s2">&quot;16&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator.__init__-79"><a href="#HDMIConfigurator.__init__-79"><span class="linenos">79</span></a>            <span class="s2">&quot;hdmi_force_hotplug&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator.__init__-80"><a href="#HDMIConfigurator.__init__-80"><span class="linenos">80</span></a>            <span class="s2">&quot;hdmi_drive&quot;</span><span class="p">:</span> <span class="s2">&quot;2&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator.__init__-81"><a href="#HDMIConfigurator.__init__-81"><span class="linenos">81</span></a>            
</span><span id="HDMIConfigurator.__init__-82"><a href="#HDMIConfigurator.__init__-82"><span class="linenos">82</span></a>            <span class="c1"># 禁用过扫描</span>
</span><span id="HDMIConfigurator.__init__-83"><a href="#HDMIConfigurator.__init__-83"><span class="linenos">83</span></a>            <span class="s2">&quot;disable_overscan&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator.__init__-84"><a href="#HDMIConfigurator.__init__-84"><span class="linenos">84</span></a>            
</span><span id="HDMIConfigurator.__init__-85"><a href="#HDMIConfigurator.__init__-85"><span class="linenos">85</span></a>            <span class="c1"># GPU显存配置</span>
</span><span id="HDMIConfigurator.__init__-86"><a href="#HDMIConfigurator.__init__-86"><span class="linenos">86</span></a>            <span class="s2">&quot;gpu_mem&quot;</span><span class="p">:</span> <span class="s2">&quot;256&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator.__init__-87"><a href="#HDMIConfigurator.__init__-87"><span class="linenos">87</span></a>            
</span><span id="HDMIConfigurator.__init__-88"><a href="#HDMIConfigurator.__init__-88"><span class="linenos">88</span></a>            <span class="c1"># 其他优化配置</span>
</span><span id="HDMIConfigurator.__init__-89"><a href="#HDMIConfigurator.__init__-89"><span class="linenos">89</span></a>            <span class="s2">&quot;hdmi_ignore_cec_init&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator.__init__-90"><a href="#HDMIConfigurator.__init__-90"><span class="linenos">90</span></a>            <span class="s2">&quot;hdmi_ignore_cec&quot;</span><span class="p">:</span> <span class="s2">&quot;1&quot;</span><span class="p">,</span>
</span><span id="HDMIConfigurator.__init__-91"><a href="#HDMIConfigurator.__init__-91"><span class="linenos">91</span></a>            <span class="s2">&quot;config_hdmi_boost&quot;</span><span class="p">:</span> <span class="s2">&quot;4&quot;</span>
</span><span id="HDMIConfigurator.__init__-92"><a href="#HDMIConfigurator.__init__-92"><span class="linenos">92</span></a>        <span class="p">}</span>
</span></pre></div>


            <div class="docstring"><p>初始化HDMI配置器</p>

<p>Args:
    config_path (str): 配置文件路径</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.config_path" class="classattr">
                                <div class="attr variable">
            <span class="name">config_path</span>

        
    </div>
    <a class="headerlink" href="#HDMIConfigurator.config_path"></a>
    
    

                            </div>
                            <div id="HDMIConfigurator.backup_path" class="classattr">
                                <div class="attr variable">
            <span class="name">backup_path</span>

        
    </div>
    <a class="headerlink" href="#HDMIConfigurator.backup_path"></a>
    
    

                            </div>
                            <div id="HDMIConfigurator.hdmi_configs" class="classattr">
                                <div class="attr variable">
            <span class="name">hdmi_configs</span>

        
    </div>
    <a class="headerlink" href="#HDMIConfigurator.hdmi_configs"></a>
    
    

                            </div>
                            <div id="HDMIConfigurator.check_permissions" class="classattr">
                                        <input id="HDMIConfigurator.check_permissions-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">check_permissions</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">) -> <span class="nb">bool</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.check_permissions-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.check_permissions"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.check_permissions-94"><a href="#HDMIConfigurator.check_permissions-94"><span class="linenos"> 94</span></a>    <span class="k">def</span> <span class="nf">check_permissions</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator.check_permissions-95"><a href="#HDMIConfigurator.check_permissions-95"><span class="linenos"> 95</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;检查文件权限&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.check_permissions-96"><a href="#HDMIConfigurator.check_permissions-96"><span class="linenos"> 96</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="HDMIConfigurator.check_permissions-97"><a href="#HDMIConfigurator.check_permissions-97"><span class="linenos"> 97</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置文件不存在: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.check_permissions-98"><a href="#HDMIConfigurator.check_permissions-98"><span class="linenos"> 98</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator.check_permissions-99"><a href="#HDMIConfigurator.check_permissions-99"><span class="linenos"> 99</span></a>        
</span><span id="HDMIConfigurator.check_permissions-100"><a href="#HDMIConfigurator.check_permissions-100"><span class="linenos">100</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">os</span><span class="o">.</span><span class="n">access</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">R_OK</span> <span class="o">|</span> <span class="n">os</span><span class="o">.</span><span class="n">W_OK</span><span class="p">):</span>
</span><span id="HDMIConfigurator.check_permissions-101"><a href="#HDMIConfigurator.check_permissions-101"><span class="linenos">101</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;没有配置文件读写权限: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.check_permissions-102"><a href="#HDMIConfigurator.check_permissions-102"><span class="linenos">102</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;请使用 sudo 运行此脚本&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.check_permissions-103"><a href="#HDMIConfigurator.check_permissions-103"><span class="linenos">103</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator.check_permissions-104"><a href="#HDMIConfigurator.check_permissions-104"><span class="linenos">104</span></a>        
</span><span id="HDMIConfigurator.check_permissions-105"><a href="#HDMIConfigurator.check_permissions-105"><span class="linenos">105</span></a>        <span class="k">return</span> <span class="kc">True</span>
</span></pre></div>


            <div class="docstring"><p>检查文件权限</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.backup_config" class="classattr">
                                        <input id="HDMIConfigurator.backup_config-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">backup_config</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">) -> <span class="nb">bool</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.backup_config-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.backup_config"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.backup_config-107"><a href="#HDMIConfigurator.backup_config-107"><span class="linenos">107</span></a>    <span class="k">def</span> <span class="nf">backup_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator.backup_config-108"><a href="#HDMIConfigurator.backup_config-108"><span class="linenos">108</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;备份原始配置文件&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.backup_config-109"><a href="#HDMIConfigurator.backup_config-109"><span class="linenos">109</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="HDMIConfigurator.backup_config-110"><a href="#HDMIConfigurator.backup_config-110"><span class="linenos">110</span></a>            <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="HDMIConfigurator.backup_config-111"><a href="#HDMIConfigurator.backup_config-111"><span class="linenos">111</span></a>                <span class="n">shutil</span><span class="o">.</span><span class="n">copy2</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="p">)</span>
</span><span id="HDMIConfigurator.backup_config-112"><a href="#HDMIConfigurator.backup_config-112"><span class="linenos">112</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置文件已备份到: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.backup_config-113"><a href="#HDMIConfigurator.backup_config-113"><span class="linenos">113</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="HDMIConfigurator.backup_config-114"><a href="#HDMIConfigurator.backup_config-114"><span class="linenos">114</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;备份文件已存在: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.backup_config-115"><a href="#HDMIConfigurator.backup_config-115"><span class="linenos">115</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="HDMIConfigurator.backup_config-116"><a href="#HDMIConfigurator.backup_config-116"><span class="linenos">116</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="HDMIConfigurator.backup_config-117"><a href="#HDMIConfigurator.backup_config-117"><span class="linenos">117</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;备份配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.backup_config-118"><a href="#HDMIConfigurator.backup_config-118"><span class="linenos">118</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span></pre></div>


            <div class="docstring"><p>备份原始配置文件</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.read_config" class="classattr">
                                        <input id="HDMIConfigurator.read_config-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">read_config</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">) -> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.read_config-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.read_config"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.read_config-120"><a href="#HDMIConfigurator.read_config-120"><span class="linenos">120</span></a>    <span class="k">def</span> <span class="nf">read_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="HDMIConfigurator.read_config-121"><a href="#HDMIConfigurator.read_config-121"><span class="linenos">121</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;读取配置文件&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.read_config-122"><a href="#HDMIConfigurator.read_config-122"><span class="linenos">122</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="HDMIConfigurator.read_config-123"><a href="#HDMIConfigurator.read_config-123"><span class="linenos">123</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">,</span> <span class="s1">&#39;r&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="HDMIConfigurator.read_config-124"><a href="#HDMIConfigurator.read_config-124"><span class="linenos">124</span></a>                <span class="k">return</span> <span class="n">f</span><span class="o">.</span><span class="n">readlines</span><span class="p">()</span>
</span><span id="HDMIConfigurator.read_config-125"><a href="#HDMIConfigurator.read_config-125"><span class="linenos">125</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="HDMIConfigurator.read_config-126"><a href="#HDMIConfigurator.read_config-126"><span class="linenos">126</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;读取配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.read_config-127"><a href="#HDMIConfigurator.read_config-127"><span class="linenos">127</span></a>            <span class="k">return</span> <span class="p">[]</span>
</span></pre></div>


            <div class="docstring"><p>读取配置文件</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.write_config" class="classattr">
                                        <input id="HDMIConfigurator.write_config-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">write_config</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">lines</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span></span><span class="return-annotation">) -> <span class="nb">bool</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.write_config-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.write_config"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.write_config-129"><a href="#HDMIConfigurator.write_config-129"><span class="linenos">129</span></a>    <span class="k">def</span> <span class="nf">write_config</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lines</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator.write_config-130"><a href="#HDMIConfigurator.write_config-130"><span class="linenos">130</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;写入配置文件&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.write_config-131"><a href="#HDMIConfigurator.write_config-131"><span class="linenos">131</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="HDMIConfigurator.write_config-132"><a href="#HDMIConfigurator.write_config-132"><span class="linenos">132</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">,</span> <span class="s1">&#39;w&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="HDMIConfigurator.write_config-133"><a href="#HDMIConfigurator.write_config-133"><span class="linenos">133</span></a>                <span class="n">f</span><span class="o">.</span><span class="n">writelines</span><span class="p">(</span><span class="n">lines</span><span class="p">)</span>
</span><span id="HDMIConfigurator.write_config-134"><a href="#HDMIConfigurator.write_config-134"><span class="linenos">134</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;配置文件写入成功&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.write_config-135"><a href="#HDMIConfigurator.write_config-135"><span class="linenos">135</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="HDMIConfigurator.write_config-136"><a href="#HDMIConfigurator.write_config-136"><span class="linenos">136</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="HDMIConfigurator.write_config-137"><a href="#HDMIConfigurator.write_config-137"><span class="linenos">137</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;写入配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.write_config-138"><a href="#HDMIConfigurator.write_config-138"><span class="linenos">138</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span></pre></div>


            <div class="docstring"><p>写入配置文件</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.parse_config_line" class="classattr">
                                        <input id="HDMIConfigurator.parse_config_line-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">parse_config_line</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">line</span><span class="p">:</span> <span class="nb">str</span></span><span class="return-annotation">) -> <span class="n">Optional</span><span class="p">[</span><span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.parse_config_line-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.parse_config_line"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.parse_config_line-140"><a href="#HDMIConfigurator.parse_config_line-140"><span class="linenos">140</span></a>    <span class="k">def</span> <span class="nf">parse_config_line</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">line</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]:</span>
</span><span id="HDMIConfigurator.parse_config_line-141"><a href="#HDMIConfigurator.parse_config_line-141"><span class="linenos">141</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;解析配置行&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.parse_config_line-142"><a href="#HDMIConfigurator.parse_config_line-142"><span class="linenos">142</span></a>        <span class="n">line</span> <span class="o">=</span> <span class="n">line</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>
</span><span id="HDMIConfigurator.parse_config_line-143"><a href="#HDMIConfigurator.parse_config_line-143"><span class="linenos">143</span></a>        
</span><span id="HDMIConfigurator.parse_config_line-144"><a href="#HDMIConfigurator.parse_config_line-144"><span class="linenos">144</span></a>        <span class="c1"># 跳过注释和空行</span>
</span><span id="HDMIConfigurator.parse_config_line-145"><a href="#HDMIConfigurator.parse_config_line-145"><span class="linenos">145</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">line</span> <span class="ow">or</span> <span class="n">line</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;#&#39;</span><span class="p">):</span>
</span><span id="HDMIConfigurator.parse_config_line-146"><a href="#HDMIConfigurator.parse_config_line-146"><span class="linenos">146</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="HDMIConfigurator.parse_config_line-147"><a href="#HDMIConfigurator.parse_config_line-147"><span class="linenos">147</span></a>        
</span><span id="HDMIConfigurator.parse_config_line-148"><a href="#HDMIConfigurator.parse_config_line-148"><span class="linenos">148</span></a>        <span class="c1"># 解析 key=value 格式</span>
</span><span id="HDMIConfigurator.parse_config_line-149"><a href="#HDMIConfigurator.parse_config_line-149"><span class="linenos">149</span></a>        <span class="k">if</span> <span class="s1">&#39;=&#39;</span> <span class="ow">in</span> <span class="n">line</span><span class="p">:</span>
</span><span id="HDMIConfigurator.parse_config_line-150"><a href="#HDMIConfigurator.parse_config_line-150"><span class="linenos">150</span></a>            <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="o">=</span> <span class="n">line</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;=&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
</span><span id="HDMIConfigurator.parse_config_line-151"><a href="#HDMIConfigurator.parse_config_line-151"><span class="linenos">151</span></a>            <span class="k">return</span> <span class="p">{</span>
</span><span id="HDMIConfigurator.parse_config_line-152"><a href="#HDMIConfigurator.parse_config_line-152"><span class="linenos">152</span></a>                <span class="s1">&#39;key&#39;</span><span class="p">:</span> <span class="n">key</span><span class="o">.</span><span class="n">strip</span><span class="p">(),</span>
</span><span id="HDMIConfigurator.parse_config_line-153"><a href="#HDMIConfigurator.parse_config_line-153"><span class="linenos">153</span></a>                <span class="s1">&#39;value&#39;</span><span class="p">:</span> <span class="n">value</span><span class="o">.</span><span class="n">strip</span><span class="p">(),</span>
</span><span id="HDMIConfigurator.parse_config_line-154"><a href="#HDMIConfigurator.parse_config_line-154"><span class="linenos">154</span></a>                <span class="s1">&#39;line&#39;</span><span class="p">:</span> <span class="n">line</span>
</span><span id="HDMIConfigurator.parse_config_line-155"><a href="#HDMIConfigurator.parse_config_line-155"><span class="linenos">155</span></a>            <span class="p">}</span>
</span><span id="HDMIConfigurator.parse_config_line-156"><a href="#HDMIConfigurator.parse_config_line-156"><span class="linenos">156</span></a>        
</span><span id="HDMIConfigurator.parse_config_line-157"><a href="#HDMIConfigurator.parse_config_line-157"><span class="linenos">157</span></a>        <span class="k">return</span> <span class="kc">None</span>
</span></pre></div>


            <div class="docstring"><p>解析配置行</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.find_config_line" class="classattr">
                                        <input id="HDMIConfigurator.find_config_line-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">find_config_line</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">lines</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>, </span><span class="param"><span class="n">key</span><span class="p">:</span> <span class="nb">str</span></span><span class="return-annotation">) -> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.find_config_line-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.find_config_line"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.find_config_line-159"><a href="#HDMIConfigurator.find_config_line-159"><span class="linenos">159</span></a>    <span class="k">def</span> <span class="nf">find_config_line</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lines</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="n">key</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]:</span>
</span><span id="HDMIConfigurator.find_config_line-160"><a href="#HDMIConfigurator.find_config_line-160"><span class="linenos">160</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;查找配置项的行号&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.find_config_line-161"><a href="#HDMIConfigurator.find_config_line-161"><span class="linenos">161</span></a>        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">line</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">lines</span><span class="p">):</span>
</span><span id="HDMIConfigurator.find_config_line-162"><a href="#HDMIConfigurator.find_config_line-162"><span class="linenos">162</span></a>            <span class="n">parsed</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parse_config_line</span><span class="p">(</span><span class="n">line</span><span class="p">)</span>
</span><span id="HDMIConfigurator.find_config_line-163"><a href="#HDMIConfigurator.find_config_line-163"><span class="linenos">163</span></a>            <span class="k">if</span> <span class="n">parsed</span> <span class="ow">and</span> <span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;key&#39;</span><span class="p">]</span> <span class="o">==</span> <span class="n">key</span><span class="p">:</span>
</span><span id="HDMIConfigurator.find_config_line-164"><a href="#HDMIConfigurator.find_config_line-164"><span class="linenos">164</span></a>                <span class="k">return</span> <span class="n">i</span>
</span><span id="HDMIConfigurator.find_config_line-165"><a href="#HDMIConfigurator.find_config_line-165"><span class="linenos">165</span></a>        <span class="k">return</span> <span class="kc">None</span>
</span></pre></div>


            <div class="docstring"><p>查找配置项的行号</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.update_config" class="classattr">
                                        <input id="HDMIConfigurator.update_config-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">update_config</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">lines</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>, </span><span class="param"><span class="n">key</span><span class="p">:</span> <span class="nb">str</span>, </span><span class="param"><span class="n">value</span><span class="p">:</span> <span class="nb">str</span></span><span class="return-annotation">) -> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.update_config-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.update_config"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.update_config-167"><a href="#HDMIConfigurator.update_config-167"><span class="linenos">167</span></a>    <span class="k">def</span> <span class="nf">update_config</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lines</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="n">key</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="HDMIConfigurator.update_config-168"><a href="#HDMIConfigurator.update_config-168"><span class="linenos">168</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;更新配置项&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.update_config-169"><a href="#HDMIConfigurator.update_config-169"><span class="linenos">169</span></a>        <span class="n">line_index</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_config_line</span><span class="p">(</span><span class="n">lines</span><span class="p">,</span> <span class="n">key</span><span class="p">)</span>
</span><span id="HDMIConfigurator.update_config-170"><a href="#HDMIConfigurator.update_config-170"><span class="linenos">170</span></a>        
</span><span id="HDMIConfigurator.update_config-171"><a href="#HDMIConfigurator.update_config-171"><span class="linenos">171</span></a>        <span class="k">if</span> <span class="n">line_index</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="HDMIConfigurator.update_config-172"><a href="#HDMIConfigurator.update_config-172"><span class="linenos">172</span></a>            <span class="c1"># 更新现有配置</span>
</span><span id="HDMIConfigurator.update_config-173"><a href="#HDMIConfigurator.update_config-173"><span class="linenos">173</span></a>            <span class="n">lines</span><span class="p">[</span><span class="n">line_index</span><span class="p">]</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="se">\n</span><span class="s2">&quot;</span>
</span><span id="HDMIConfigurator.update_config-174"><a href="#HDMIConfigurator.update_config-174"><span class="linenos">174</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;更新配置: </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.update_config-175"><a href="#HDMIConfigurator.update_config-175"><span class="linenos">175</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="HDMIConfigurator.update_config-176"><a href="#HDMIConfigurator.update_config-176"><span class="linenos">176</span></a>            <span class="c1"># 添加新配置</span>
</span><span id="HDMIConfigurator.update_config-177"><a href="#HDMIConfigurator.update_config-177"><span class="linenos">177</span></a>            <span class="n">lines</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.update_config-178"><a href="#HDMIConfigurator.update_config-178"><span class="linenos">178</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;添加配置: </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.update_config-179"><a href="#HDMIConfigurator.update_config-179"><span class="linenos">179</span></a>        
</span><span id="HDMIConfigurator.update_config-180"><a href="#HDMIConfigurator.update_config-180"><span class="linenos">180</span></a>        <span class="k">return</span> <span class="n">lines</span>
</span></pre></div>


            <div class="docstring"><p>更新配置项</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.apply_hdmi_configs" class="classattr">
                                        <input id="HDMIConfigurator.apply_hdmi_configs-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">apply_hdmi_configs</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">) -> <span class="nb">bool</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.apply_hdmi_configs-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.apply_hdmi_configs"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.apply_hdmi_configs-182"><a href="#HDMIConfigurator.apply_hdmi_configs-182"><span class="linenos">182</span></a>    <span class="k">def</span> <span class="nf">apply_hdmi_configs</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator.apply_hdmi_configs-183"><a href="#HDMIConfigurator.apply_hdmi_configs-183"><span class="linenos">183</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;应用HDMI配置&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.apply_hdmi_configs-184"><a href="#HDMIConfigurator.apply_hdmi_configs-184"><span class="linenos">184</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;开始应用HDMI配置...&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.apply_hdmi_configs-185"><a href="#HDMIConfigurator.apply_hdmi_configs-185"><span class="linenos">185</span></a>        
</span><span id="HDMIConfigurator.apply_hdmi_configs-186"><a href="#HDMIConfigurator.apply_hdmi_configs-186"><span class="linenos">186</span></a>        <span class="c1"># 读取现有配置</span>
</span><span id="HDMIConfigurator.apply_hdmi_configs-187"><a href="#HDMIConfigurator.apply_hdmi_configs-187"><span class="linenos">187</span></a>        <span class="n">lines</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_config</span><span class="p">()</span>
</span><span id="HDMIConfigurator.apply_hdmi_configs-188"><a href="#HDMIConfigurator.apply_hdmi_configs-188"><span class="linenos">188</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="HDMIConfigurator.apply_hdmi_configs-189"><a href="#HDMIConfigurator.apply_hdmi_configs-189"><span class="linenos">189</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator.apply_hdmi_configs-190"><a href="#HDMIConfigurator.apply_hdmi_configs-190"><span class="linenos">190</span></a>        
</span><span id="HDMIConfigurator.apply_hdmi_configs-191"><a href="#HDMIConfigurator.apply_hdmi_configs-191"><span class="linenos">191</span></a>        <span class="c1"># 应用每个配置项</span>
</span><span id="HDMIConfigurator.apply_hdmi_configs-192"><a href="#HDMIConfigurator.apply_hdmi_configs-192"><span class="linenos">192</span></a>        <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">hdmi_configs</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><span id="HDMIConfigurator.apply_hdmi_configs-193"><a href="#HDMIConfigurator.apply_hdmi_configs-193"><span class="linenos">193</span></a>            <span class="n">lines</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">update_config</span><span class="p">(</span><span class="n">lines</span><span class="p">,</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
</span><span id="HDMIConfigurator.apply_hdmi_configs-194"><a href="#HDMIConfigurator.apply_hdmi_configs-194"><span class="linenos">194</span></a>        
</span><span id="HDMIConfigurator.apply_hdmi_configs-195"><a href="#HDMIConfigurator.apply_hdmi_configs-195"><span class="linenos">195</span></a>        <span class="c1"># 写入配置文件</span>
</span><span id="HDMIConfigurator.apply_hdmi_configs-196"><a href="#HDMIConfigurator.apply_hdmi_configs-196"><span class="linenos">196</span></a>        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">write_config</span><span class="p">(</span><span class="n">lines</span><span class="p">)</span>
</span></pre></div>


            <div class="docstring"><p>应用HDMI配置</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.restore_backup" class="classattr">
                                        <input id="HDMIConfigurator.restore_backup-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">restore_backup</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">) -> <span class="nb">bool</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.restore_backup-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.restore_backup"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.restore_backup-198"><a href="#HDMIConfigurator.restore_backup-198"><span class="linenos">198</span></a>    <span class="k">def</span> <span class="nf">restore_backup</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator.restore_backup-199"><a href="#HDMIConfigurator.restore_backup-199"><span class="linenos">199</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;恢复备份配置&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.restore_backup-200"><a href="#HDMIConfigurator.restore_backup-200"><span class="linenos">200</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="HDMIConfigurator.restore_backup-201"><a href="#HDMIConfigurator.restore_backup-201"><span class="linenos">201</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;备份文件不存在&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.restore_backup-202"><a href="#HDMIConfigurator.restore_backup-202"><span class="linenos">202</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator.restore_backup-203"><a href="#HDMIConfigurator.restore_backup-203"><span class="linenos">203</span></a>        
</span><span id="HDMIConfigurator.restore_backup-204"><a href="#HDMIConfigurator.restore_backup-204"><span class="linenos">204</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="HDMIConfigurator.restore_backup-205"><a href="#HDMIConfigurator.restore_backup-205"><span class="linenos">205</span></a>            <span class="n">shutil</span><span class="o">.</span><span class="n">copy2</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">backup_path</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">config_path</span><span class="p">)</span>
</span><span id="HDMIConfigurator.restore_backup-206"><a href="#HDMIConfigurator.restore_backup-206"><span class="linenos">206</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;已恢复原始配置&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.restore_backup-207"><a href="#HDMIConfigurator.restore_backup-207"><span class="linenos">207</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="HDMIConfigurator.restore_backup-208"><a href="#HDMIConfigurator.restore_backup-208"><span class="linenos">208</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="HDMIConfigurator.restore_backup-209"><a href="#HDMIConfigurator.restore_backup-209"><span class="linenos">209</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;恢复配置失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.restore_backup-210"><a href="#HDMIConfigurator.restore_backup-210"><span class="linenos">210</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span></pre></div>


            <div class="docstring"><p>恢复备份配置</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.show_current_config" class="classattr">
                                        <input id="HDMIConfigurator.show_current_config-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">show_current_config</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">) -> <span class="kc">None</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.show_current_config-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.show_current_config"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.show_current_config-212"><a href="#HDMIConfigurator.show_current_config-212"><span class="linenos">212</span></a>    <span class="k">def</span> <span class="nf">show_current_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="HDMIConfigurator.show_current_config-213"><a href="#HDMIConfigurator.show_current_config-213"><span class="linenos">213</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;显示当前配置&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.show_current_config-214"><a href="#HDMIConfigurator.show_current_config-214"><span class="linenos">214</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;当前HDMI相关配置:&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_current_config-215"><a href="#HDMIConfigurator.show_current_config-215"><span class="linenos">215</span></a>        
</span><span id="HDMIConfigurator.show_current_config-216"><a href="#HDMIConfigurator.show_current_config-216"><span class="linenos">216</span></a>        <span class="n">lines</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_config</span><span class="p">()</span>
</span><span id="HDMIConfigurator.show_current_config-217"><a href="#HDMIConfigurator.show_current_config-217"><span class="linenos">217</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="HDMIConfigurator.show_current_config-218"><a href="#HDMIConfigurator.show_current_config-218"><span class="linenos">218</span></a>            <span class="k">return</span>
</span><span id="HDMIConfigurator.show_current_config-219"><a href="#HDMIConfigurator.show_current_config-219"><span class="linenos">219</span></a>        
</span><span id="HDMIConfigurator.show_current_config-220"><a href="#HDMIConfigurator.show_current_config-220"><span class="linenos">220</span></a>        <span class="n">hdmi_keys</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">hdmi_configs</span><span class="o">.</span><span class="n">keys</span><span class="p">())</span>
</span><span id="HDMIConfigurator.show_current_config-221"><a href="#HDMIConfigurator.show_current_config-221"><span class="linenos">221</span></a>        
</span><span id="HDMIConfigurator.show_current_config-222"><a href="#HDMIConfigurator.show_current_config-222"><span class="linenos">222</span></a>        <span class="k">for</span> <span class="n">line</span> <span class="ow">in</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="HDMIConfigurator.show_current_config-223"><a href="#HDMIConfigurator.show_current_config-223"><span class="linenos">223</span></a>            <span class="n">parsed</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parse_config_line</span><span class="p">(</span><span class="n">line</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_current_config-224"><a href="#HDMIConfigurator.show_current_config-224"><span class="linenos">224</span></a>            <span class="k">if</span> <span class="n">parsed</span> <span class="ow">and</span> <span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;key&#39;</span><span class="p">]</span> <span class="ow">in</span> <span class="n">hdmi_keys</span><span class="p">:</span>
</span><span id="HDMIConfigurator.show_current_config-225"><a href="#HDMIConfigurator.show_current_config-225"><span class="linenos">225</span></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  </span><span class="si">{</span><span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;key&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;value&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span></pre></div>


            <div class="docstring"><p>显示当前配置</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.show_changes" class="classattr">
                                        <input id="HDMIConfigurator.show_changes-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">show_changes</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">) -> <span class="kc">None</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.show_changes-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.show_changes"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.show_changes-227"><a href="#HDMIConfigurator.show_changes-227"><span class="linenos">227</span></a>    <span class="k">def</span> <span class="nf">show_changes</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="HDMIConfigurator.show_changes-228"><a href="#HDMIConfigurator.show_changes-228"><span class="linenos">228</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;显示将要应用的更改&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.show_changes-229"><a href="#HDMIConfigurator.show_changes-229"><span class="linenos">229</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;将要应用的HDMI配置:&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_changes-230"><a href="#HDMIConfigurator.show_changes-230"><span class="linenos">230</span></a>        <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">hdmi_configs</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><span id="HDMIConfigurator.show_changes-231"><a href="#HDMIConfigurator.show_changes-231"><span class="linenos">231</span></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_changes-232"><a href="#HDMIConfigurator.show_changes-232"><span class="linenos">232</span></a>        
</span><span id="HDMIConfigurator.show_changes-233"><a href="#HDMIConfigurator.show_changes-233"><span class="linenos">233</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">配置说明:&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_changes-234"><a href="#HDMIConfigurator.show_changes-234"><span class="linenos">234</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_group=1: HDMI组1（CEA标准）&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_changes-235"><a href="#HDMIConfigurator.show_changes-235"><span class="linenos">235</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_mode=16: 1080p@60Hz&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_changes-236"><a href="#HDMIConfigurator.show_changes-236"><span class="linenos">236</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_force_hotplug=1: 强制HDMI热插拔检测&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_changes-237"><a href="#HDMIConfigurator.show_changes-237"><span class="linenos">237</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_drive=2: HDMI驱动强度&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_changes-238"><a href="#HDMIConfigurator.show_changes-238"><span class="linenos">238</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  disable_overscan=1: 禁用过扫描&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_changes-239"><a href="#HDMIConfigurator.show_changes-239"><span class="linenos">239</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  gpu_mem=256: GPU显存256MB&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_changes-240"><a href="#HDMIConfigurator.show_changes-240"><span class="linenos">240</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_ignore_cec_init=1: 忽略CEC初始化&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_changes-241"><a href="#HDMIConfigurator.show_changes-241"><span class="linenos">241</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  hdmi_ignore_cec=1: 忽略CEC&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.show_changes-242"><a href="#HDMIConfigurator.show_changes-242"><span class="linenos">242</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;  config_hdmi_boost=4: HDMI信号增强&quot;</span><span class="p">)</span>
</span></pre></div>


            <div class="docstring"><p>显示将要应用的更改</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.validate_config" class="classattr">
                                        <input id="HDMIConfigurator.validate_config-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">validate_config</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">) -> <span class="nb">bool</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.validate_config-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.validate_config"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.validate_config-244"><a href="#HDMIConfigurator.validate_config-244"><span class="linenos">244</span></a>    <span class="k">def</span> <span class="nf">validate_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator.validate_config-245"><a href="#HDMIConfigurator.validate_config-245"><span class="linenos">245</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;验证配置&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.validate_config-246"><a href="#HDMIConfigurator.validate_config-246"><span class="linenos">246</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;验证配置...&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.validate_config-247"><a href="#HDMIConfigurator.validate_config-247"><span class="linenos">247</span></a>        
</span><span id="HDMIConfigurator.validate_config-248"><a href="#HDMIConfigurator.validate_config-248"><span class="linenos">248</span></a>        <span class="c1"># 检查关键配置项</span>
</span><span id="HDMIConfigurator.validate_config-249"><a href="#HDMIConfigurator.validate_config-249"><span class="linenos">249</span></a>        <span class="n">required_configs</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="HDMIConfigurator.validate_config-250"><a href="#HDMIConfigurator.validate_config-250"><span class="linenos">250</span></a>            <span class="s1">&#39;hdmi_group&#39;</span><span class="p">:</span> <span class="s1">&#39;1&#39;</span><span class="p">,</span>
</span><span id="HDMIConfigurator.validate_config-251"><a href="#HDMIConfigurator.validate_config-251"><span class="linenos">251</span></a>            <span class="s1">&#39;hdmi_mode&#39;</span><span class="p">:</span> <span class="s1">&#39;16&#39;</span><span class="p">,</span>
</span><span id="HDMIConfigurator.validate_config-252"><a href="#HDMIConfigurator.validate_config-252"><span class="linenos">252</span></a>            <span class="s1">&#39;gpu_mem&#39;</span><span class="p">:</span> <span class="s1">&#39;256&#39;</span>
</span><span id="HDMIConfigurator.validate_config-253"><a href="#HDMIConfigurator.validate_config-253"><span class="linenos">253</span></a>        <span class="p">}</span>
</span><span id="HDMIConfigurator.validate_config-254"><a href="#HDMIConfigurator.validate_config-254"><span class="linenos">254</span></a>        
</span><span id="HDMIConfigurator.validate_config-255"><a href="#HDMIConfigurator.validate_config-255"><span class="linenos">255</span></a>        <span class="n">lines</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_config</span><span class="p">()</span>
</span><span id="HDMIConfigurator.validate_config-256"><a href="#HDMIConfigurator.validate_config-256"><span class="linenos">256</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="HDMIConfigurator.validate_config-257"><a href="#HDMIConfigurator.validate_config-257"><span class="linenos">257</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator.validate_config-258"><a href="#HDMIConfigurator.validate_config-258"><span class="linenos">258</span></a>        
</span><span id="HDMIConfigurator.validate_config-259"><a href="#HDMIConfigurator.validate_config-259"><span class="linenos">259</span></a>        <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">expected_value</span> <span class="ow">in</span> <span class="n">required_configs</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><span id="HDMIConfigurator.validate_config-260"><a href="#HDMIConfigurator.validate_config-260"><span class="linenos">260</span></a>            <span class="n">line_index</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">find_config_line</span><span class="p">(</span><span class="n">lines</span><span class="p">,</span> <span class="n">key</span><span class="p">)</span>
</span><span id="HDMIConfigurator.validate_config-261"><a href="#HDMIConfigurator.validate_config-261"><span class="linenos">261</span></a>            <span class="k">if</span> <span class="n">line_index</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
</span><span id="HDMIConfigurator.validate_config-262"><a href="#HDMIConfigurator.validate_config-262"><span class="linenos">262</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;缺少配置项: </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.validate_config-263"><a href="#HDMIConfigurator.validate_config-263"><span class="linenos">263</span></a>                <span class="k">continue</span>
</span><span id="HDMIConfigurator.validate_config-264"><a href="#HDMIConfigurator.validate_config-264"><span class="linenos">264</span></a>            
</span><span id="HDMIConfigurator.validate_config-265"><a href="#HDMIConfigurator.validate_config-265"><span class="linenos">265</span></a>            <span class="n">parsed</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parse_config_line</span><span class="p">(</span><span class="n">lines</span><span class="p">[</span><span class="n">line_index</span><span class="p">])</span>
</span><span id="HDMIConfigurator.validate_config-266"><a href="#HDMIConfigurator.validate_config-266"><span class="linenos">266</span></a>            <span class="k">if</span> <span class="n">parsed</span> <span class="ow">and</span> <span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;value&#39;</span><span class="p">]</span> <span class="o">!=</span> <span class="n">expected_value</span><span class="p">:</span>
</span><span id="HDMIConfigurator.validate_config-267"><a href="#HDMIConfigurator.validate_config-267"><span class="linenos">267</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置项 </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2"> 值不匹配: 期望 </span><span class="si">{</span><span class="n">expected_value</span><span class="si">}</span><span class="s2">, 实际 </span><span class="si">{</span><span class="n">parsed</span><span class="p">[</span><span class="s1">&#39;value&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.validate_config-268"><a href="#HDMIConfigurator.validate_config-268"><span class="linenos">268</span></a>        
</span><span id="HDMIConfigurator.validate_config-269"><a href="#HDMIConfigurator.validate_config-269"><span class="linenos">269</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;配置验证完成&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.validate_config-270"><a href="#HDMIConfigurator.validate_config-270"><span class="linenos">270</span></a>        <span class="k">return</span> <span class="kc">True</span>
</span></pre></div>


            <div class="docstring"><p>验证配置</p>
</div>


                            </div>
                            <div id="HDMIConfigurator.run" class="classattr">
                                        <input id="HDMIConfigurator.run-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">run</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">dry_run</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span>, </span><span class="param"><span class="n">restore</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span></span><span class="return-annotation">) -> <span class="nb">bool</span>:</span></span>

                <label class="view-source-button" for="HDMIConfigurator.run-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#HDMIConfigurator.run"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="HDMIConfigurator.run-272"><a href="#HDMIConfigurator.run-272"><span class="linenos">272</span></a>    <span class="k">def</span> <span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">dry_run</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span> <span class="n">restore</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="HDMIConfigurator.run-273"><a href="#HDMIConfigurator.run-273"><span class="linenos">273</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;运行配置程序&quot;&quot;&quot;</span>
</span><span id="HDMIConfigurator.run-274"><a href="#HDMIConfigurator.run-274"><span class="linenos">274</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;=== 树莓派HDMI配置优化工具 ===&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.run-275"><a href="#HDMIConfigurator.run-275"><span class="linenos">275</span></a>        
</span><span id="HDMIConfigurator.run-276"><a href="#HDMIConfigurator.run-276"><span class="linenos">276</span></a>        <span class="c1"># 检查权限</span>
</span><span id="HDMIConfigurator.run-277"><a href="#HDMIConfigurator.run-277"><span class="linenos">277</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">check_permissions</span><span class="p">():</span>
</span><span id="HDMIConfigurator.run-278"><a href="#HDMIConfigurator.run-278"><span class="linenos">278</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator.run-279"><a href="#HDMIConfigurator.run-279"><span class="linenos">279</span></a>        
</span><span id="HDMIConfigurator.run-280"><a href="#HDMIConfigurator.run-280"><span class="linenos">280</span></a>        <span class="c1"># 恢复备份</span>
</span><span id="HDMIConfigurator.run-281"><a href="#HDMIConfigurator.run-281"><span class="linenos">281</span></a>        <span class="k">if</span> <span class="n">restore</span><span class="p">:</span>
</span><span id="HDMIConfigurator.run-282"><a href="#HDMIConfigurator.run-282"><span class="linenos">282</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">restore_backup</span><span class="p">()</span>
</span><span id="HDMIConfigurator.run-283"><a href="#HDMIConfigurator.run-283"><span class="linenos">283</span></a>        
</span><span id="HDMIConfigurator.run-284"><a href="#HDMIConfigurator.run-284"><span class="linenos">284</span></a>        <span class="c1"># 显示当前配置</span>
</span><span id="HDMIConfigurator.run-285"><a href="#HDMIConfigurator.run-285"><span class="linenos">285</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">show_current_config</span><span class="p">()</span>
</span><span id="HDMIConfigurator.run-286"><a href="#HDMIConfigurator.run-286"><span class="linenos">286</span></a>        
</span><span id="HDMIConfigurator.run-287"><a href="#HDMIConfigurator.run-287"><span class="linenos">287</span></a>        <span class="c1"># 显示将要应用的更改</span>
</span><span id="HDMIConfigurator.run-288"><a href="#HDMIConfigurator.run-288"><span class="linenos">288</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">show_changes</span><span class="p">()</span>
</span><span id="HDMIConfigurator.run-289"><a href="#HDMIConfigurator.run-289"><span class="linenos">289</span></a>        
</span><span id="HDMIConfigurator.run-290"><a href="#HDMIConfigurator.run-290"><span class="linenos">290</span></a>        <span class="k">if</span> <span class="n">dry_run</span><span class="p">:</span>
</span><span id="HDMIConfigurator.run-291"><a href="#HDMIConfigurator.run-291"><span class="linenos">291</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;模拟运行模式，不会实际修改配置文件&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.run-292"><a href="#HDMIConfigurator.run-292"><span class="linenos">292</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="HDMIConfigurator.run-293"><a href="#HDMIConfigurator.run-293"><span class="linenos">293</span></a>        
</span><span id="HDMIConfigurator.run-294"><a href="#HDMIConfigurator.run-294"><span class="linenos">294</span></a>        <span class="c1"># 确认操作</span>
</span><span id="HDMIConfigurator.run-295"><a href="#HDMIConfigurator.run-295"><span class="linenos">295</span></a>        <span class="n">confirm</span> <span class="o">=</span> <span class="nb">input</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">确认应用这些配置？(y/N): &quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.run-296"><a href="#HDMIConfigurator.run-296"><span class="linenos">296</span></a>        <span class="k">if</span> <span class="n">confirm</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="o">!=</span> <span class="s1">&#39;y&#39;</span><span class="p">:</span>
</span><span id="HDMIConfigurator.run-297"><a href="#HDMIConfigurator.run-297"><span class="linenos">297</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;操作已取消&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.run-298"><a href="#HDMIConfigurator.run-298"><span class="linenos">298</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator.run-299"><a href="#HDMIConfigurator.run-299"><span class="linenos">299</span></a>        
</span><span id="HDMIConfigurator.run-300"><a href="#HDMIConfigurator.run-300"><span class="linenos">300</span></a>        <span class="c1"># 备份配置</span>
</span><span id="HDMIConfigurator.run-301"><a href="#HDMIConfigurator.run-301"><span class="linenos">301</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">backup_config</span><span class="p">():</span>
</span><span id="HDMIConfigurator.run-302"><a href="#HDMIConfigurator.run-302"><span class="linenos">302</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator.run-303"><a href="#HDMIConfigurator.run-303"><span class="linenos">303</span></a>        
</span><span id="HDMIConfigurator.run-304"><a href="#HDMIConfigurator.run-304"><span class="linenos">304</span></a>        <span class="c1"># 应用配置</span>
</span><span id="HDMIConfigurator.run-305"><a href="#HDMIConfigurator.run-305"><span class="linenos">305</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">apply_hdmi_configs</span><span class="p">():</span>
</span><span id="HDMIConfigurator.run-306"><a href="#HDMIConfigurator.run-306"><span class="linenos">306</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="HDMIConfigurator.run-307"><a href="#HDMIConfigurator.run-307"><span class="linenos">307</span></a>        
</span><span id="HDMIConfigurator.run-308"><a href="#HDMIConfigurator.run-308"><span class="linenos">308</span></a>        <span class="c1"># 验证配置</span>
</span><span id="HDMIConfigurator.run-309"><a href="#HDMIConfigurator.run-309"><span class="linenos">309</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">validate_config</span><span class="p">()</span>
</span><span id="HDMIConfigurator.run-310"><a href="#HDMIConfigurator.run-310"><span class="linenos">310</span></a>        
</span><span id="HDMIConfigurator.run-311"><a href="#HDMIConfigurator.run-311"><span class="linenos">311</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;=== 配置完成 ===&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.run-312"><a href="#HDMIConfigurator.run-312"><span class="linenos">312</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;请重启树莓派以应用新配置&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.run-313"><a href="#HDMIConfigurator.run-313"><span class="linenos">313</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;重启命令: sudo reboot&quot;</span><span class="p">)</span>
</span><span id="HDMIConfigurator.run-314"><a href="#HDMIConfigurator.run-314"><span class="linenos">314</span></a>        
</span><span id="HDMIConfigurator.run-315"><a href="#HDMIConfigurator.run-315"><span class="linenos">315</span></a>        <span class="k">return</span> <span class="kc">True</span>
</span></pre></div>


            <div class="docstring"><p>运行配置程序</p>
</div>


                            </div>
                </section>
                <section id="main">
                            <input id="main-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">main</span><span class="signature pdoc-code condensed">(<span class="return-annotation">):</span></span>

                <label class="view-source-button" for="main-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#main"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="main-317"><a href="#main-317"><span class="linenos">317</span></a><span class="k">def</span> <span class="nf">main</span><span class="p">():</span>
</span><span id="main-318"><a href="#main-318"><span class="linenos">318</span></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;主函数&quot;&quot;&quot;</span>
</span><span id="main-319"><a href="#main-319"><span class="linenos">319</span></a>    <span class="n">parser</span> <span class="o">=</span> <span class="n">argparse</span><span class="o">.</span><span class="n">ArgumentParser</span><span class="p">(</span><span class="n">description</span><span class="o">=</span><span class="s2">&quot;树莓派HDMI配置优化工具&quot;</span><span class="p">)</span>
</span><span id="main-320"><a href="#main-320"><span class="linenos">320</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--config&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="s2">&quot;/boot/config.txt&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;配置文件路径&quot;</span><span class="p">)</span>
</span><span id="main-321"><a href="#main-321"><span class="linenos">321</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--dry-run&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;模拟运行，不实际修改文件&quot;</span><span class="p">)</span>
</span><span id="main-322"><a href="#main-322"><span class="linenos">322</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--restore&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;恢复原始配置&quot;</span><span class="p">)</span>
</span><span id="main-323"><a href="#main-323"><span class="linenos">323</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--show&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;显示当前配置&quot;</span><span class="p">)</span>
</span><span id="main-324"><a href="#main-324"><span class="linenos">324</span></a>    
</span><span id="main-325"><a href="#main-325"><span class="linenos">325</span></a>    <span class="n">args</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>
</span><span id="main-326"><a href="#main-326"><span class="linenos">326</span></a>    
</span><span id="main-327"><a href="#main-327"><span class="linenos">327</span></a>    <span class="n">configurator</span> <span class="o">=</span> <span class="n">HDMIConfigurator</span><span class="p">(</span><span class="n">args</span><span class="o">.</span><span class="n">config</span><span class="p">)</span>
</span><span id="main-328"><a href="#main-328"><span class="linenos">328</span></a>    
</span><span id="main-329"><a href="#main-329"><span class="linenos">329</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">show</span><span class="p">:</span>
</span><span id="main-330"><a href="#main-330"><span class="linenos">330</span></a>        <span class="n">configurator</span><span class="o">.</span><span class="n">show_current_config</span><span class="p">()</span>
</span><span id="main-331"><a href="#main-331"><span class="linenos">331</span></a>        <span class="k">return</span>
</span><span id="main-332"><a href="#main-332"><span class="linenos">332</span></a>    
</span><span id="main-333"><a href="#main-333"><span class="linenos">333</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">restore</span><span class="p">:</span>
</span><span id="main-334"><a href="#main-334"><span class="linenos">334</span></a>        <span class="n">configurator</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">restore</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="main-335"><a href="#main-335"><span class="linenos">335</span></a>        <span class="k">return</span>
</span><span id="main-336"><a href="#main-336"><span class="linenos">336</span></a>    
</span><span id="main-337"><a href="#main-337"><span class="linenos">337</span></a>    <span class="n">success</span> <span class="o">=</span> <span class="n">configurator</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">dry_run</span><span class="o">=</span><span class="n">args</span><span class="o">.</span><span class="n">dry_run</span><span class="p">)</span>
</span><span id="main-338"><a href="#main-338"><span class="linenos">338</span></a>    
</span><span id="main-339"><a href="#main-339"><span class="linenos">339</span></a>    <span class="k">if</span> <span class="n">success</span><span class="p">:</span>
</span><span id="main-340"><a href="#main-340"><span class="linenos">340</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">🎉 HDMI配置优化完成！&quot;</span><span class="p">)</span>
</span><span id="main-341"><a href="#main-341"><span class="linenos">341</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">args</span><span class="o">.</span><span class="n">dry_run</span><span class="p">:</span>
</span><span id="main-342"><a href="#main-342"><span class="linenos">342</span></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;请重启树莓派以应用新配置&quot;</span><span class="p">)</span>
</span><span id="main-343"><a href="#main-343"><span class="linenos">343</span></a>    <span class="k">else</span><span class="p">:</span>
</span><span id="main-344"><a href="#main-344"><span class="linenos">344</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">❌ 配置失败，请查看日志文件&quot;</span><span class="p">)</span>
</span></pre></div>


            <div class="docstring"><p>主函数</p>
</div>


                </section>
    </main>
<script>
    function escapeHTML(html) {
        return document.createElement('div').appendChild(document.createTextNode(html)).parentNode.innerHTML;
    }

    const originalContent = document.querySelector("main.pdoc");
    let currentContent = originalContent;

    function setContent(innerHTML) {
        let elem;
        if (innerHTML) {
            elem = document.createElement("main");
            elem.classList.add("pdoc");
            elem.innerHTML = innerHTML;
        } else {
            elem = originalContent;
        }
        if (currentContent !== elem) {
            currentContent.replaceWith(elem);
            currentContent = elem;
        }
    }

    function getSearchTerm() {
        return (new URL(window.location)).searchParams.get("search");
    }

    const searchBox = document.querySelector(".pdoc input[type=search]");
    searchBox.addEventListener("input", function () {
        let url = new URL(window.location);
        if (searchBox.value.trim()) {
            url.hash = "";
            url.searchParams.set("search", searchBox.value);
        } else {
            url.searchParams.delete("search");
        }
        history.replaceState("", "", url.toString());
        onInput();
    });
    window.addEventListener("popstate", onInput);


    let search, searchErr;

    async function initialize() {
        try {
            search = await new Promise((resolve, reject) => {
                const script = document.createElement("script");
                script.type = "text/javascript";
                script.async = true;
                script.onload = () => resolve(window.pdocSearch);
                script.onerror = (e) => reject(e);
                script.src = "../search.js";
                document.getElementsByTagName("head")[0].appendChild(script);
            });
        } catch (e) {
            console.error("Cannot fetch pdoc search index");
            searchErr = "Cannot fetch search index.";
        }
        onInput();

        document.querySelector("nav.pdoc").addEventListener("click", e => {
            if (e.target.hash) {
                searchBox.value = "";
                searchBox.dispatchEvent(new Event("input"));
            }
        });
    }

    function onInput() {
        setContent((() => {
            const term = getSearchTerm();
            if (!term) {
                return null
            }
            if (searchErr) {
                return `<h3>Error: ${searchErr}</h3>`
            }
            if (!search) {
                return "<h3>Searching...</h3>"
            }

            window.scrollTo({top: 0, left: 0, behavior: 'auto'});

            const results = search(term);

            let html;
            if (results.length === 0) {
                html = `No search results for '${escapeHTML(term)}'.`
            } else {
                html = `<h4>${results.length} search result${results.length > 1 ? "s" : ""} for '${escapeHTML(term)}'.</h4>`;
            }
            for (let result of results.slice(0, 10)) {
                let doc = result.doc;
                let url = `../${doc.modulename.replaceAll(".", "/")}.html`;
                if (doc.qualname) {
                    url += `#${doc.qualname}`;
                }

                let heading;
                switch (result.doc.kind) {
                    case "function":
                        if (doc.fullname.endsWith(".__init__")) {
                            heading = `<span class="name">${doc.fullname.replace(/\.__init__$/, "")}</span>${doc.signature}`;
                        } else {
                            heading = `<span class="def">${doc.funcdef}</span> <span class="name">${doc.fullname}</span>${doc.signature}`;
                        }
                        break;
                    case "class":
                        heading = `<span class="def">class</span> <span class="name">${doc.fullname}</span>`;
                        if (doc.bases)
                            heading += `<wbr>(<span class="base">${doc.bases}</span>)`;
                        heading += `:`;
                        break;
                    case "variable":
                        heading = `<span class="name">${doc.fullname}</span>`;
                        if (doc.annotation)
                            heading += `<span class="annotation">${doc.annotation}</span>`;
                        if (doc.default_value)
                            heading += `<span class="default_value"> = ${doc.default_value}</span>`;
                        break;
                    default:
                        heading = `<span class="name">${doc.fullname}</span>`;
                        break;
                }
                html += `
                        <section class="search-result">
                        <a href="${url}" class="attr ${doc.kind}">${heading}</a>
                        <div class="docstring">${doc.doc}</div>
                        </section>
                    `;

            }
            return html;
        })());
    }

    if (getSearchTerm()) {
        initialize();
        searchBox.value = getSearchTerm();
        onInput();
    } else {
        searchBox.addEventListener("focus", initialize, {once: true});
    }

    searchBox.addEventListener("keydown", e => {
        if (["ArrowDown", "ArrowUp", "Enter"].includes(e.key)) {
            let focused = currentContent.querySelector(".search-result.focused");
            if (!focused) {
                currentContent.querySelector(".search-result").classList.add("focused");
            } else if (
                e.key === "ArrowDown"
                && focused.nextElementSibling
                && focused.nextElementSibling.classList.contains("search-result")
            ) {
                focused.classList.remove("focused");
                focused.nextElementSibling.classList.add("focused");
                focused.nextElementSibling.scrollIntoView({
                    behavior: "smooth",
                    block: "nearest",
                    inline: "nearest"
                });
            } else if (
                e.key === "ArrowUp"
                && focused.previousElementSibling
                && focused.previousElementSibling.classList.contains("search-result")
            ) {
                focused.classList.remove("focused");
                focused.previousElementSibling.classList.add("focused");
                focused.previousElementSibling.scrollIntoView({
                    behavior: "smooth",
                    block: "nearest",
                    inline: "nearest"
                });
            } else if (
                e.key === "Enter"
            ) {
                focused.querySelector("a").click();
            }
        }
    });
</script></body>
</html>