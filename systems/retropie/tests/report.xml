<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="0" tests="9" time="0.334" timestamp="2025-06-24T11:47:04.003755" hostname="MacBook-Air.local"><testcase classname="tests.test_bash_scripts" name="test_bash_syntax[retropie_ecosystem_auto.sh]" time="0.034" /><testcase classname="tests.test_bash_scripts" name="test_bash_syntax[immersive_hardware_auto.sh]" time="0.025" /><testcase classname="tests.test_bash_scripts" name="test_bash_syntax[install.sh]" time="0.018" /><testcase classname="tests.test_hdmi_config" name="test_apply_hdmi_configs" time="0.004" /><testcase classname="tests.test_installer" name="test_check_dependencies" time="0.001" /><testcase classname="tests.test_installer" name="test_get_download_url" time="0.001" /><testcase classname="tests.test_installer" name="test_list_available_disks" time="0.000" /><testcase classname="tests.test_rom_downloader" name="test_load_config" time="0.002" /><testcase classname="tests.test_rom_downloader" name="test_search_roms" time="0.002" /></testsuite></testsuites>