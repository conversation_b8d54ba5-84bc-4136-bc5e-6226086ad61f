<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="generator" content="pdoc 15.0.4"/>
    <title>roms.rom_downloader API documentation</title>

    <style>/*! * Bootstrap Reboot v5.0.0 (https://getbootstrap.com/) * Copyright 2011-2021 The Bootstrap Authors * Copyright 2011-2021 Twitter, Inc. * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE) * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md) */*,::after,::before{box-sizing:border-box}@media (prefers-reduced-motion:no-preference){:root{scroll-behavior:smooth}}body{margin:0;font-family:system-ui,-apple-system,"Segoe UI",<PERSON><PERSON>,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans","Liberation Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-size:1rem;font-weight:400;line-height:1.5;color:#212529;background-color:#fff;-webkit-text-size-adjust:100%;-webkit-tap-highlight-color:transparent}hr{margin:1rem 0;color:inherit;background-color:currentColor;border:0;opacity:.25}hr:not([size]){height:1px}h1,h2,h3,h4,h5,h6{margin-top:0;margin-bottom:.5rem;font-weight:500;line-height:1.2}h1{font-size:calc(1.375rem + 1.5vw)}@media (min-width:1200px){h1{font-size:2.5rem}}h2{font-size:calc(1.325rem + .9vw)}@media (min-width:1200px){h2{font-size:2rem}}h3{font-size:calc(1.3rem + .6vw)}@media (min-width:1200px){h3{font-size:1.75rem}}h4{font-size:calc(1.275rem + .3vw)}@media (min-width:1200px){h4{font-size:1.5rem}}h5{font-size:1.25rem}h6{font-size:1rem}p{margin-top:0;margin-bottom:1rem}abbr[data-bs-original-title],abbr[title]{-webkit-text-decoration:underline dotted;text-decoration:underline dotted;cursor:help;-webkit-text-decoration-skip-ink:none;text-decoration-skip-ink:none}address{margin-bottom:1rem;font-style:normal;line-height:inherit}ol,ul{padding-left:2rem}dl,ol,ul{margin-top:0;margin-bottom:1rem}ol ol,ol ul,ul ol,ul ul{margin-bottom:0}dt{font-weight:700}dd{margin-bottom:.5rem;margin-left:0}blockquote{margin:0 0 1rem}b,strong{font-weight:bolder}small{font-size:.875em}mark{padding:.2em;background-color:#fcf8e3}sub,sup{position:relative;font-size:.75em;line-height:0;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}a{color:#0d6efd;text-decoration:underline}a:hover{color:#0a58ca}a:not([href]):not([class]),a:not([href]):not([class]):hover{color:inherit;text-decoration:none}code,kbd,pre,samp{font-family:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;font-size:1em;direction:ltr;unicode-bidi:bidi-override}pre{display:block;margin-top:0;margin-bottom:1rem;overflow:auto;font-size:.875em}pre code{font-size:inherit;color:inherit;word-break:normal}code{font-size:.875em;color:#d63384;word-wrap:break-word}a>code{color:inherit}kbd{padding:.2rem .4rem;font-size:.875em;color:#fff;background-color:#212529;border-radius:.2rem}kbd kbd{padding:0;font-size:1em;font-weight:700}figure{margin:0 0 1rem}img,svg{vertical-align:middle}table{caption-side:bottom;border-collapse:collapse}caption{padding-top:.5rem;padding-bottom:.5rem;color:#6c757d;text-align:left}th{text-align:inherit;text-align:-webkit-match-parent}tbody,td,tfoot,th,thead,tr{border-color:inherit;border-style:solid;border-width:0}label{display:inline-block}button{border-radius:0}button:focus:not(:focus-visible){outline:0}button,input,optgroup,select,textarea{margin:0;font-family:inherit;font-size:inherit;line-height:inherit}button,select{text-transform:none}[role=button]{cursor:pointer}select{word-wrap:normal}select:disabled{opacity:1}[list]::-webkit-calendar-picker-indicator{display:none}[type=button],[type=reset],[type=submit],button{-webkit-appearance:button}[type=button]:not(:disabled),[type=reset]:not(:disabled),[type=submit]:not(:disabled),button:not(:disabled){cursor:pointer}::-moz-focus-inner{padding:0;border-style:none}textarea{resize:vertical}fieldset{min-width:0;padding:0;margin:0;border:0}legend{float:left;width:100%;padding:0;margin-bottom:.5rem;font-size:calc(1.275rem + .3vw);line-height:inherit}@media (min-width:1200px){legend{font-size:1.5rem}}legend+*{clear:left}::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-fields-wrapper,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-text,::-webkit-datetime-edit-year-field{padding:0}::-webkit-inner-spin-button{height:auto}[type=search]{outline-offset:-2px;-webkit-appearance:textfield}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-color-swatch-wrapper{padding:0}::file-selector-button{font:inherit}::-webkit-file-upload-button{font:inherit;-webkit-appearance:button}output{display:inline-block}iframe{border:0}summary{display:list-item;cursor:pointer}progress{vertical-align:baseline}[hidden]{display:none!important}</style>
    <style>/*! syntax-highlighting.css */pre{line-height:125%;}span.linenos{color:inherit; background-color:transparent; padding-left:5px; padding-right:20px;}.pdoc-code .hll{background-color:#ffffcc}.pdoc-code{background:#f8f8f8;}.pdoc-code .c{color:#3D7B7B; font-style:italic}.pdoc-code .err{border:1px solid #FF0000}.pdoc-code .k{color:#008000; font-weight:bold}.pdoc-code .o{color:#666666}.pdoc-code .ch{color:#3D7B7B; font-style:italic}.pdoc-code .cm{color:#3D7B7B; font-style:italic}.pdoc-code .cp{color:#9C6500}.pdoc-code .cpf{color:#3D7B7B; font-style:italic}.pdoc-code .c1{color:#3D7B7B; font-style:italic}.pdoc-code .cs{color:#3D7B7B; font-style:italic}.pdoc-code .gd{color:#A00000}.pdoc-code .ge{font-style:italic}.pdoc-code .gr{color:#E40000}.pdoc-code .gh{color:#000080; font-weight:bold}.pdoc-code .gi{color:#008400}.pdoc-code .go{color:#717171}.pdoc-code .gp{color:#000080; font-weight:bold}.pdoc-code .gs{font-weight:bold}.pdoc-code .gu{color:#800080; font-weight:bold}.pdoc-code .gt{color:#0044DD}.pdoc-code .kc{color:#008000; font-weight:bold}.pdoc-code .kd{color:#008000; font-weight:bold}.pdoc-code .kn{color:#008000; font-weight:bold}.pdoc-code .kp{color:#008000}.pdoc-code .kr{color:#008000; font-weight:bold}.pdoc-code .kt{color:#B00040}.pdoc-code .m{color:#666666}.pdoc-code .s{color:#BA2121}.pdoc-code .na{color:#687822}.pdoc-code .nb{color:#008000}.pdoc-code .nc{color:#0000FF; font-weight:bold}.pdoc-code .no{color:#880000}.pdoc-code .nd{color:#AA22FF}.pdoc-code .ni{color:#717171; font-weight:bold}.pdoc-code .ne{color:#CB3F38; font-weight:bold}.pdoc-code .nf{color:#0000FF}.pdoc-code .nl{color:#767600}.pdoc-code .nn{color:#0000FF; font-weight:bold}.pdoc-code .nt{color:#008000; font-weight:bold}.pdoc-code .nv{color:#19177C}.pdoc-code .ow{color:#AA22FF; font-weight:bold}.pdoc-code .w{color:#bbbbbb}.pdoc-code .mb{color:#666666}.pdoc-code .mf{color:#666666}.pdoc-code .mh{color:#666666}.pdoc-code .mi{color:#666666}.pdoc-code .mo{color:#666666}.pdoc-code .sa{color:#BA2121}.pdoc-code .sb{color:#BA2121}.pdoc-code .sc{color:#BA2121}.pdoc-code .dl{color:#BA2121}.pdoc-code .sd{color:#BA2121; font-style:italic}.pdoc-code .s2{color:#BA2121}.pdoc-code .se{color:#AA5D1F; font-weight:bold}.pdoc-code .sh{color:#BA2121}.pdoc-code .si{color:#A45A77; font-weight:bold}.pdoc-code .sx{color:#008000}.pdoc-code .sr{color:#A45A77}.pdoc-code .s1{color:#BA2121}.pdoc-code .ss{color:#19177C}.pdoc-code .bp{color:#008000}.pdoc-code .fm{color:#0000FF}.pdoc-code .vc{color:#19177C}.pdoc-code .vg{color:#19177C}.pdoc-code .vi{color:#19177C}.pdoc-code .vm{color:#19177C}.pdoc-code .il{color:#666666}</style>
    <style>/*! theme.css */:root{--pdoc-background:#fff;}.pdoc{--text:#212529;--muted:#6c757d;--link:#3660a5;--link-hover:#1659c5;--code:#f8f8f8;--active:#fff598;--accent:#eee;--accent2:#c1c1c1;--nav-hover:rgba(255, 255, 255, 0.5);--name:#0066BB;--def:#008800;--annotation:#007020;}</style>
    <style>/*! layout.css */html, body{width:100%;height:100%;}html, main{scroll-behavior:smooth;}body{background-color:var(--pdoc-background);}@media (max-width:769px){#navtoggle{cursor:pointer;position:absolute;width:50px;height:40px;top:1rem;right:1rem;border-color:var(--text);color:var(--text);display:flex;opacity:0.8;z-index:999;}#navtoggle:hover{opacity:1;}#togglestate + div{display:none;}#togglestate:checked + div{display:inherit;}main, header{padding:2rem 3vw;}header + main{margin-top:-3rem;}.git-button{display:none !important;}nav input[type="search"]{max-width:77%;}nav input[type="search"]:first-child{margin-top:-6px;}nav input[type="search"]:valid ~ *{display:none !important;}}@media (min-width:770px){:root{--sidebar-width:clamp(12.5rem, 28vw, 22rem);}nav{position:fixed;overflow:auto;height:100vh;width:var(--sidebar-width);}main, header{padding:3rem 2rem 3rem calc(var(--sidebar-width) + 3rem);width:calc(54rem + var(--sidebar-width));max-width:100%;}header + main{margin-top:-4rem;}#navtoggle{display:none;}}#togglestate{position:absolute;height:0;opacity:0;}nav.pdoc{--pad:clamp(0.5rem, 2vw, 1.75rem);--indent:1.5rem;background-color:var(--accent);border-right:1px solid var(--accent2);box-shadow:0 0 20px rgba(50, 50, 50, .2) inset;padding:0 0 0 var(--pad);overflow-wrap:anywhere;scrollbar-width:thin; scrollbar-color:var(--accent2) transparent; z-index:1}nav.pdoc::-webkit-scrollbar{width:.4rem; }nav.pdoc::-webkit-scrollbar-thumb{background-color:var(--accent2); }nav.pdoc > div{padding:var(--pad) 0;}nav.pdoc .module-list-button{display:inline-flex;align-items:center;color:var(--text);border-color:var(--muted);margin-bottom:1rem;}nav.pdoc .module-list-button:hover{border-color:var(--text);}nav.pdoc input[type=search]{display:block;outline-offset:0;width:calc(100% - var(--pad));}nav.pdoc .logo{max-width:calc(100% - var(--pad));max-height:35vh;display:block;margin:0 auto 1rem;transform:translate(calc(-.5 * var(--pad)), 0);}nav.pdoc ul{list-style:none;padding-left:0;}nav.pdoc > div > ul{margin-left:calc(0px - var(--pad));}nav.pdoc li a{padding:.2rem 0 .2rem calc(var(--pad) + var(--indent));}nav.pdoc > div > ul > li > a{padding-left:var(--pad);}nav.pdoc li{transition:all 100ms;}nav.pdoc li:hover{background-color:var(--nav-hover);}nav.pdoc a, nav.pdoc a:hover{color:var(--text);}nav.pdoc a{display:block;}nav.pdoc > h2:first-of-type{margin-top:1.5rem;}nav.pdoc .class:before{content:"class ";color:var(--muted);}nav.pdoc .function:after{content:"()";color:var(--muted);}nav.pdoc footer:before{content:"";display:block;width:calc(100% - var(--pad));border-top:solid var(--accent2) 1px;margin-top:1.5rem;padding-top:.5rem;}nav.pdoc footer{font-size:small;}</style>
    <style>/*! content.css */.pdoc{color:var(--text);box-sizing:border-box;line-height:1.5;background:none;}.pdoc .pdoc-button{cursor:pointer;display:inline-block;border:solid black 1px;border-radius:2px;font-size:.75rem;padding:calc(0.5em - 1px) 1em;transition:100ms all;}.pdoc .alert{padding:1rem 1rem 1rem calc(1.5rem + 24px);border:1px solid transparent;border-radius:.25rem;background-repeat:no-repeat;background-position:.75rem center;margin-bottom:1rem;}.pdoc .alert > em{display:none;}.pdoc .alert > *:last-child{margin-bottom:0;}.pdoc .alert.note{color:#084298;background-color:#cfe2ff;border-color:#b6d4fe;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23084298%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M8%2016A8%208%200%201%200%208%200a8%208%200%200%200%200%2016zm.93-9.412-1%204.705c-.07.34.029.533.304.533.194%200%20.487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703%200-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381%202.29-.287zM8%205.5a1%201%200%201%201%200-2%201%201%200%200%201%200%202z%22/%3E%3C/svg%3E");}.pdoc .alert.tip{color:#0a3622;background-color:#d1e7dd;border-color:#a3cfbb;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%230a3622%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M2%206a6%206%200%201%201%2010.174%204.31c-.203.196-.359.4-.453.619l-.762%201.769A.5.5%200%200%201%2010.5%2013a.5.5%200%200%201%200%201%20.5.5%200%200%201%200%201l-.224.447a1%201%200%200%201-.894.553H6.618a1%201%200%200%201-.894-.553L5.5%2015a.5.5%200%200%201%200-1%20.5.5%200%200%201%200-1%20.5.5%200%200%201-.46-.302l-.761-1.77a2%202%200%200%200-.453-.618A5.98%205.98%200%200%201%202%206m6-5a5%205%200%200%200-3.479%208.592c.263.254.514.564.676.941L5.83%2012h4.342l.632-1.467c.162-.377.413-.687.676-.941A5%205%200%200%200%208%201%22/%3E%3C/svg%3E");}.pdoc .alert.important{color:#055160;background-color:#cff4fc;border-color:#9eeaf9;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23055160%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M2%200a2%202%200%200%200-2%202v12a2%202%200%200%200%202%202h12a2%202%200%200%200%202-2V2a2%202%200%200%200-2-2zm6%204c.535%200%20.954.462.9.995l-.35%203.507a.552.552%200%200%201-1.1%200L7.1%204.995A.905.905%200%200%201%208%204m.002%206a1%201%200%201%201%200%202%201%201%200%200%201%200-2%22/%3E%3C/svg%3E");}.pdoc .alert.warning{color:#664d03;background-color:#fff3cd;border-color:#ffecb5;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23664d03%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M8.982%201.566a1.13%201.13%200%200%200-1.96%200L.165%2013.233c-.457.778.091%201.767.98%201.767h13.713c.889%200%201.438-.99.98-1.767L8.982%201.566zM8%205c.535%200%20.954.462.9.995l-.35%203.507a.552.552%200%200%201-1.1%200L7.1%205.995A.905.905%200%200%201%208%205zm.002%206a1%201%200%201%201%200%202%201%201%200%200%201%200-2z%22/%3E%3C/svg%3E");}.pdoc .alert.caution{color:#842029;background-color:#f8d7da;border-color:#f5c2c7;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23842029%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M11.46.146A.5.5%200%200%200%2011.107%200H4.893a.5.5%200%200%200-.353.146L.146%204.54A.5.5%200%200%200%200%204.893v6.214a.5.5%200%200%200%20.146.353l4.394%204.394a.5.5%200%200%200%20.353.146h6.214a.5.5%200%200%200%20.353-.146l4.394-4.394a.5.5%200%200%200%20.146-.353V4.893a.5.5%200%200%200-.146-.353zM8%204c.535%200%20.954.462.9.995l-.35%203.507a.552.552%200%200%201-1.1%200L7.1%204.995A.905.905%200%200%201%208%204m.002%206a1%201%200%201%201%200%202%201%201%200%200%201%200-2%22/%3E%3C/svg%3E");}.pdoc .alert.danger{color:#842029;background-color:#f8d7da;border-color:#f5c2c7;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23842029%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M5.52.359A.5.5%200%200%201%206%200h4a.5.5%200%200%201%20.474.658L8.694%206H12.5a.5.5%200%200%201%20.395.807l-7%209a.5.5%200%200%201-.873-.454L6.823%209.5H3.5a.5.5%200%200%201-.48-.641l2.5-8.5z%22/%3E%3C/svg%3E");}.pdoc .visually-hidden{position:absolute !important;width:1px !important;height:1px !important;padding:0 !important;margin:-1px !important;overflow:hidden !important;clip:rect(0, 0, 0, 0) !important;white-space:nowrap !important;border:0 !important;}.pdoc h1, .pdoc h2, .pdoc h3{font-weight:300;margin:.3em 0;padding:.2em 0;}.pdoc > section:not(.module-info) h1{font-size:1.5rem;font-weight:500;}.pdoc > section:not(.module-info) h2{font-size:1.4rem;font-weight:500;}.pdoc > section:not(.module-info) h3{font-size:1.3rem;font-weight:500;}.pdoc > section:not(.module-info) h4{font-size:1.2rem;}.pdoc > section:not(.module-info) h5{font-size:1.1rem;}.pdoc a{text-decoration:none;color:var(--link);}.pdoc a:hover{color:var(--link-hover);}.pdoc blockquote{margin-left:2rem;}.pdoc pre{border-top:1px solid var(--accent2);border-bottom:1px solid var(--accent2);margin-top:0;margin-bottom:1em;padding:.5rem 0 .5rem .5rem;overflow-x:auto;background-color:var(--code);}.pdoc code{color:var(--text);padding:.2em .4em;margin:0;font-size:85%;background-color:var(--accent);border-radius:6px;}.pdoc a > code{color:inherit;}.pdoc pre > code{display:inline-block;font-size:inherit;background:none;border:none;padding:0;}.pdoc > section:not(.module-info){margin-bottom:1.5rem;}.pdoc .modulename{margin-top:0;font-weight:bold;}.pdoc .modulename a{color:var(--link);transition:100ms all;}.pdoc .git-button{float:right;border:solid var(--link) 1px;}.pdoc .git-button:hover{background-color:var(--link);color:var(--pdoc-background);}.view-source-toggle-state,.view-source-toggle-state ~ .pdoc-code{display:none;}.view-source-toggle-state:checked ~ .pdoc-code{display:block;}.view-source-button{display:inline-block;float:right;font-size:.75rem;line-height:1.5rem;color:var(--muted);padding:0 .4rem 0 1.3rem;cursor:pointer;text-indent:-2px;}.view-source-button > span{visibility:hidden;}.module-info .view-source-button{float:none;display:flex;justify-content:flex-end;margin:-1.2rem .4rem -.2rem 0;}.view-source-button::before{position:absolute;content:"View Source";display:list-item;list-style-type:disclosure-closed;}.view-source-toggle-state:checked ~ .attr .view-source-button::before,.view-source-toggle-state:checked ~ .view-source-button::before{list-style-type:disclosure-open;}.pdoc .docstring{margin-bottom:1.5rem;}.pdoc section:not(.module-info) .docstring{margin-left:clamp(0rem, 5vw - 2rem, 1rem);}.pdoc .docstring .pdoc-code{margin-left:1em;margin-right:1em;}.pdoc h1:target,.pdoc h2:target,.pdoc h3:target,.pdoc h4:target,.pdoc h5:target,.pdoc h6:target,.pdoc .pdoc-code > pre > span:target{background-color:var(--active);box-shadow:-1rem 0 0 0 var(--active);}.pdoc .pdoc-code > pre > span:target{display:block;}.pdoc div:target > .attr,.pdoc section:target > .attr,.pdoc dd:target > a{background-color:var(--active);}.pdoc *{scroll-margin:2rem;}.pdoc .pdoc-code .linenos{user-select:none;}.pdoc .attr:hover{filter:contrast(0.95);}.pdoc section, .pdoc .classattr{position:relative;}.pdoc .headerlink{--width:clamp(1rem, 3vw, 2rem);position:absolute;top:0;left:calc(0rem - var(--width));transition:all 100ms ease-in-out;opacity:0;}.pdoc .headerlink::before{content:"#";display:block;text-align:center;width:var(--width);height:2.3rem;line-height:2.3rem;font-size:1.5rem;}.pdoc .attr:hover ~ .headerlink,.pdoc *:target > .headerlink,.pdoc .headerlink:hover{opacity:1;}.pdoc .attr{display:block;margin:.5rem 0 .5rem;padding:.4rem .4rem .4rem 1rem;background-color:var(--accent);overflow-x:auto;}.pdoc .classattr{margin-left:2rem;}.pdoc .decorator-deprecated{color:#842029;}.pdoc .decorator-deprecated ~ span{filter:grayscale(1) opacity(0.8);}.pdoc .name{color:var(--name);font-weight:bold;}.pdoc .def{color:var(--def);font-weight:bold;}.pdoc .signature{background-color:transparent;}.pdoc .param, .pdoc .return-annotation{white-space:pre;}.pdoc .signature.multiline .param{display:block;}.pdoc .signature.condensed .param{display:inline-block;}.pdoc .annotation{color:var(--annotation);}.pdoc .view-value-toggle-state,.pdoc .view-value-toggle-state ~ .default_value{display:none;}.pdoc .view-value-toggle-state:checked ~ .default_value{display:inherit;}.pdoc .view-value-button{font-size:.5rem;vertical-align:middle;border-style:dashed;margin-top:-0.1rem;}.pdoc .view-value-button:hover{background:white;}.pdoc .view-value-button::before{content:"show";text-align:center;width:2.2em;display:inline-block;}.pdoc .view-value-toggle-state:checked ~ .view-value-button::before{content:"hide";}.pdoc .inherited{margin-left:2rem;}.pdoc .inherited dt{font-weight:700;}.pdoc .inherited dt, .pdoc .inherited dd{display:inline;margin-left:0;margin-bottom:.5rem;}.pdoc .inherited dd:not(:last-child):after{content:", ";}.pdoc .inherited .class:before{content:"class ";}.pdoc .inherited .function a:after{content:"()";}.pdoc .search-result .docstring{overflow:auto;max-height:25vh;}.pdoc .search-result.focused > .attr{background-color:var(--active);}.pdoc .attribution{margin-top:2rem;display:block;opacity:0.5;transition:all 200ms;filter:grayscale(100%);}.pdoc .attribution:hover{opacity:1;filter:grayscale(0%);}.pdoc .attribution img{margin-left:5px;height:35px;vertical-align:middle;width:70px;transition:all 200ms;}.pdoc table{display:block;width:max-content;max-width:100%;overflow:auto;margin-bottom:1rem;}.pdoc table th{font-weight:600;}.pdoc table th, .pdoc table td{padding:6px 13px;border:1px solid var(--accent2);}</style>
    <style>/*! custom.css */</style></head>
<body>
    <nav class="pdoc">
        <label id="navtoggle" for="togglestate" class="pdoc-button"><svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke-linecap='round' stroke="currentColor" stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg></label>
        <input id="togglestate" type="checkbox" aria-hidden="true" tabindex="-1">
        <div>            <a class="pdoc-button module-list-button" href="../roms.html">
<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-box-arrow-in-left" viewBox="0 0 16 16">
  <path fill-rule="evenodd" d="M10 3.5a.5.5 0 0 0-.5-.5h-8a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5h8a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 1 1 0v2A1.5 1.5 0 0 1 9.5 14h-8A1.5 1.5 0 0 1 0 12.5v-9A1.5 1.5 0 0 1 1.5 2h8A1.5 1.5 0 0 1 11 3.5v2a.5.5 0 0 1-1 0v-2z"/>
  <path fill-rule="evenodd" d="M4.146 8.354a.5.5 0 0 1 0-.708l3-3a.5.5 0 1 1 .708.708L5.707 7.5H14.5a.5.5 0 0 1 0 1H5.707l2.147 2.146a.5.5 0 0 1-.708.708l-3-3z"/>
</svg>                &nbsp;roms</a>


            <input type="search" placeholder="Search..." role="searchbox" aria-label="search"
                   pattern=".+" required>



            <h2>API Documentation</h2>
                <ul class="memberlist">
            <li>
                    <a class="variable" href="#logger">logger</a>
            </li>
            <li>
                    <a class="class" href="#ROMDownloader">ROMDownloader</a>
                            <ul class="memberlist">
                        <li>
                                <a class="function" href="#ROMDownloader.__init__">ROMDownloader</a>
                        </li>
                        <li>
                                <a class="variable" href="#ROMDownloader.config_file">config_file</a>
                        </li>
                        <li>
                                <a class="variable" href="#ROMDownloader.download_dir">download_dir</a>
                        </li>
                        <li>
                                <a class="variable" href="#ROMDownloader.config">config</a>
                        </li>
                        <li>
                                <a class="variable" href="#ROMDownloader.session">session</a>
                        </li>
                        <li>
                                <a class="function" href="#ROMDownloader.search_roms">search_roms</a>
                        </li>
                        <li>
                                <a class="function" href="#ROMDownloader.get_download_url">get_download_url</a>
                        </li>
                        <li>
                                <a class="function" href="#ROMDownloader.download_file">download_file</a>
                        </li>
                        <li>
                                <a class="function" href="#ROMDownloader.verify_file">verify_file</a>
                        </li>
                        <li>
                                <a class="function" href="#ROMDownloader.extract_roms">extract_roms</a>
                        </li>
                        <li>
                                <a class="function" href="#ROMDownloader.connect_sftp">connect_sftp</a>
                        </li>
                        <li>
                                <a class="function" href="#ROMDownloader.upload_roms">upload_roms</a>
                        </li>
                        <li>
                                <a class="function" href="#ROMDownloader.run">run</a>
                        </li>
                </ul>

            </li>
            <li>
                    <a class="function" href="#main">main</a>
            </li>
    </ul>



        <a class="attribution" title="pdoc: Python API documentation generator" href="https://pdoc.dev" target="_blank">
            built with <span class="visually-hidden">pdoc</span><img
                alt="pdoc logo"
                src="data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20role%3D%22img%22%20aria-label%3D%22pdoc%20logo%22%20width%3D%22300%22%20height%3D%22150%22%20viewBox%3D%22-1%200%2060%2030%22%3E%3Ctitle%3Epdoc%3C/title%3E%3Cpath%20d%3D%22M29.621%2021.293c-.011-.273-.214-.475-.511-.481a.5.5%200%200%200-.489.503l-.044%201.393c-.097.551-.695%201.215-1.566%201.704-.577.428-1.306.486-2.193.182-1.426-.617-2.467-1.654-3.304-2.487l-.173-.172a3.43%203.43%200%200%200-.365-.306.49.49%200%200%200-.286-.196c-1.718-1.06-4.931-1.47-7.353.191l-.219.15c-1.707%201.187-3.413%202.131-4.328%201.03-.02-.027-.49-.685-.141-1.763.233-.721.546-2.408.772-4.076.042-.09.067-.187.046-.288.166-1.347.277-2.625.241-3.351%201.378-1.008%202.271-2.586%202.271-4.362%200-.976-.272-1.935-.788-2.774-.057-.094-.122-.18-.184-.268.033-.167.052-.339.052-.516%200-1.477-1.202-2.679-2.679-2.679-.791%200-1.496.352-1.987.9a6.3%206.3%200%200%200-1.001.029c-.492-.564-1.207-.929-2.012-.929-1.477%200-2.679%201.202-2.679%202.679A2.65%202.65%200%200%200%20.97%206.554c-.383.747-.595%201.572-.595%202.41%200%202.311%201.507%204.29%203.635%205.107-.037.699-.147%202.27-.423%203.294l-.137.461c-.622%202.042-2.515%208.257%201.727%2010.643%201.614.908%203.06%201.248%204.317%201.248%202.665%200%204.492-1.524%205.322-2.401%201.476-1.559%202.886-1.854%206.491.82%201.877%201.393%203.514%201.753%204.861%201.068%202.223-1.713%202.811-3.867%203.399-6.374.077-.846.056-1.469.054-1.537zm-4.835%204.313c-.054.305-.156.586-.242.629-.034-.007-.131-.022-.307-.157-.145-.111-.314-.478-.456-.908.221.121.432.25.675.355.115.039.219.051.33.081zm-2.251-1.238c-.05.33-.158.648-.252.694-.022.001-.125-.018-.307-.157-.217-.166-.488-.906-.639-1.573.358.344.754.693%201.198%201.036zm-3.887-2.337c-.006-.116-.018-.231-.041-.342.635.145%201.189.368%201.599.625.097.231.166.481.174.642-.03.049-.055.101-.067.158-.046.013-.128.026-.298.004-.278-.037-.901-.57-1.367-1.087zm-1.127-.497c.116.306.176.625.12.71-.019.014-.117.045-.345.016-.206-.027-.604-.332-.986-.695.41-.051.816-.056%201.211-.031zm-4.535%201.535c.209.22.379.47.358.598-.006.041-.088.138-.351.234-.144.055-.539-.063-.979-.259a11.66%2011.66%200%200%200%20.972-.573zm.983-.664c.359-.237.738-.418%201.126-.554.25.237.479.548.457.694-.006.042-.087.138-.351.235-.174.064-.694-.105-1.232-.375zm-3.381%201.794c-.022.145-.061.29-.149.401-.133.166-.358.248-.69.251h-.002c-.133%200-.306-.26-.45-.621.417.091.854.07%201.291-.031zm-2.066-8.077a4.78%204.78%200%200%201-.775-.584c.172-.115.505-.254.88-.378l-.105.962zm-.331%202.302a10.32%2010.32%200%200%201-.828-.502c.202-.143.576-.328.984-.49l-.156.992zm-.45%202.157l-.701-.403c.214-.115.536-.249.891-.376a11.57%2011.57%200%200%201-.19.779zm-.181%201.716c.064.398.194.702.298.893-.194-.051-.435-.162-.736-.398.061-.119.224-.3.438-.495zM8.87%204.141c0%20.152-.123.276-.276.276s-.275-.124-.275-.276.123-.276.276-.276.275.124.275.276zm-.735-.389a1.15%201.15%200%200%200-.314.783%201.16%201.16%200%200%200%201.162%201.162c.457%200%20.842-.27%201.032-.653.026.117.042.238.042.362a1.68%201.68%200%200%201-1.679%201.679%201.68%201.68%200%200%201-1.679-1.679c0-.843.626-1.535%201.436-1.654zM5.059%205.406A1.68%201.68%200%200%201%203.38%207.085a1.68%201.68%200%200%201-1.679-1.679c0-.037.009-.072.011-.109.21.3.541.508.935.508a1.16%201.16%200%200%200%201.162-1.162%201.14%201.14%200%200%200-.474-.912c.015%200%20.03-.005.045-.005.926.001%201.679.754%201.679%201.68zM3.198%204.141c0%20.152-.123.276-.276.276s-.275-.124-.275-.276.123-.276.276-.276.275.124.275.276zM1.375%208.964c0-.52.103-1.035.288-1.52.466.394%201.06.64%201.717.64%201.144%200%202.116-.725%202.499-1.738.383%201.012%201.355%201.738%202.499%201.738.867%200%201.631-.421%202.121-1.062.307.605.478%201.267.478%201.942%200%202.486-2.153%204.51-4.801%204.51s-4.801-2.023-4.801-4.51zm24.342%2019.349c-.985.498-2.267.168-3.813-.979-3.073-2.281-5.453-3.199-7.813-.705-1.315%201.391-4.163%203.365-8.423.97-3.174-1.786-2.239-6.266-1.261-9.479l.146-.492c.276-1.02.395-2.457.444-3.268a6.11%206.11%200%200%200%201.18.115%206.01%206.01%200%200%200%202.536-.562l-.006.175c-.802.215-1.848.612-2.021%201.25-.079.295.021.601.274.837.219.203.415.364.598.501-.667.304-1.243.698-1.311%201.179-.02.144-.022.507.393.787.213.144.395.26.564.365-1.285.521-1.361.96-1.381%201.126-.018.142-.011.496.427.746l.854.489c-.473.389-.971.914-.999%201.429-.018.278.095.532.316.713.675.556%201.231.721%201.653.721.059%200%20.104-.014.158-.02.207.707.641%201.64%201.513%201.64h.013c.8-.008%201.236-.345%201.462-.626.173-.216.268-.457.325-.692.424.195.93.374%201.372.374.151%200%20.294-.021.423-.068.732-.27.944-.704.993-1.021.009-.061.003-.119.002-.179.266.086.538.147.789.147.15%200%20.294-.021.423-.069.542-.2.797-.489.914-.754.237.147.478.258.704.288.106.014.205.021.296.021.356%200%20.595-.101.767-.229.438.435%201.094.992%201.656%201.067.106.014.205.021.296.021a1.56%201.56%200%200%200%20.323-.035c.17.575.453%201.289.866%201.605.358.273.665.362.914.362a.99.99%200%200%200%20.421-.093%201.03%201.03%200%200%200%20.245-.164c.168.428.39.846.68%201.068.358.273.665.362.913.362a.99.99%200%200%200%20.421-.093c.317-.148.512-.448.639-.762.251.157.495.257.726.257.127%200%20.25-.024.37-.071.427-.17.706-.617.841-1.314.022-.015.047-.022.068-.038.067-.051.133-.104.196-.159-.443%201.486-1.107%202.761-2.086%203.257zM8.66%209.925a.5.5%200%201%200-1%200c0%20.653-.818%201.205-1.787%201.205s-1.787-.552-1.787-1.205a.5.5%200%201%200-1%200c0%201.216%201.25%202.205%202.787%202.205s2.787-.989%202.787-2.205zm4.4%2015.965l-.208.097c-2.661%201.258-4.708%201.436-6.086.527-1.542-1.017-1.88-3.19-1.844-4.198a.4.4%200%200%200-.385-.414c-.242-.029-.406.164-.414.385-.046%201.249.367%203.686%202.202%204.896.708.467%201.547.7%202.51.7%201.248%200%202.706-.392%204.362-1.174l.185-.086a.4.4%200%200%200%20.205-.527c-.089-.204-.326-.291-.527-.206zM9.547%202.292c.093.077.205.114.317.114a.5.5%200%200%200%20.318-.886L8.817.397a.5.5%200%200%200-.703.068.5.5%200%200%200%20.069.703l1.364%201.124zm-7.661-.065c.086%200%20.173-.022.253-.068l1.523-.893a.5.5%200%200%200-.506-.863l-1.523.892a.5.5%200%200%200-.179.685c.094.158.261.247.432.247z%22%20transform%3D%22matrix%28-1%200%200%201%2058%200%29%22%20fill%3D%22%233bb300%22/%3E%3Cpath%20d%3D%22M.3%2021.86V10.18q0-.46.02-.68.04-.22.18-.5.28-.54%201.34-.54%201.06%200%201.42.28.38.26.44.78.76-1.04%202.38-1.04%201.64%200%203.1%201.54%201.46%201.54%201.46%203.58%200%202.04-1.46%203.58-1.44%201.54-3.08%201.54-1.64%200-2.38-.92v4.04q0%20.46-.04.68-.02.22-.18.5-.14.3-.5.42-.36.12-.98.12-.62%200-1-.12-.36-.12-.52-.4-.14-.28-.18-.5-.02-.22-.02-.68zm3.96-9.42q-.46.54-.46%201.18%200%20.64.46%201.18.48.52%201.2.52.74%200%201.24-.52.52-.52.52-1.18%200-.66-.48-1.18-.48-.54-1.26-.54-.76%200-1.22.54zm14.741-8.36q.16-.3.54-.42.38-.12%201-.12.64%200%201.02.12.38.12.52.42.16.3.18.54.04.22.04.68v11.94q0%20.46-.04.7-.02.22-.18.5-.3.54-1.7.54-1.38%200-1.54-.98-.84.96-2.34.96-1.8%200-3.28-1.56-1.48-1.58-1.48-3.66%200-2.1%201.48-3.68%201.5-1.58%203.28-1.58%201.48%200%202.3%201v-4.2q0-.46.02-.68.04-.24.18-.52zm-3.24%2010.86q.52.54%201.26.54.74%200%201.22-.54.5-.54.5-1.18%200-.66-.48-1.22-.46-.56-1.26-.56-.8%200-1.28.56-.48.54-.48%201.2%200%20.66.52%201.2zm7.833-1.2q0-2.4%201.68-3.96%201.68-1.56%203.84-1.56%202.16%200%203.82%201.56%201.66%201.54%201.66%203.94%200%201.66-.86%202.96-.86%201.28-2.1%201.9-1.22.6-2.54.6-1.32%200-2.56-.64-1.24-.66-2.1-1.92-.84-1.28-.84-2.88zm4.18%201.44q.64.48%201.3.48.66%200%201.32-.5.66-.5.66-1.48%200-.98-.62-1.46-.62-.48-1.34-.48-.72%200-1.34.5-.62.5-.62%201.48%200%20.96.64%201.46zm11.412-1.44q0%20.84.56%201.32.56.46%201.18.46.64%200%201.18-.36.56-.38.9-.38.6%200%201.46%201.06.46.58.46%201.04%200%20.76-1.1%201.42-1.14.8-2.8.8-1.86%200-3.58-1.34-.82-.64-1.34-1.7-.52-1.08-.52-2.36%200-1.3.52-2.34.52-1.06%201.34-1.7%201.66-1.32%203.54-1.32.76%200%201.48.22.72.2%201.06.4l.32.2q.36.24.56.38.52.4.52.92%200%20.5-.42%201.14-.72%201.1-1.38%201.1-.38%200-1.08-.44-.36-.34-1.04-.34-.66%200-1.24.48-.58.48-.58%201.34z%22%20fill%3D%22green%22/%3E%3C/svg%3E"/>
        </a>
</div>
    </nav>
    <main class="pdoc">
            <section class="module-info">
                    <h1 class="modulename">
<a href="./../roms.html">roms</a><wbr>.rom_downloader    </h1>

                        <div class="docstring"><p>NES ROM 下载和传输工具</p>

<p>这是一个自动化ROM下载和传输工具，专门用于RetroPie游戏系统。
支持从合法ROM资源站下载游戏合集并自动传输到树莓派。</p>

<p>主要功能：</p>

<ul>
<li>从Archive.org等合法资源站搜索和下载ROM</li>
<li>支持断点续传和校验和验证</li>
<li>自动解压ZIP格式的ROM文件</li>
<li>通过SFTP自动传输到树莓派</li>
<li>完整的日志记录和错误处理</li>
<li>配置文件驱动的灵活配置</li>
</ul>

<p>系统要求：</p>

<ul>
<li>Python 3.7+</li>
<li>网络连接</li>
<li>树莓派SSH访问权限</li>
</ul>

<p>作者: AI Assistant
版本: 2.0.0
许可证: MIT</p>
</div>

                        <input id="mod-rom_downloader-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">

                        <label class="view-source-button" for="mod-rom_downloader-view-source"><span>View Source</span></label>

                        <div class="pdoc-code codehilite"><pre><span></span><span id="L-1"><a href="#L-1"><span class="linenos">  1</span></a><span class="ch">#!/usr/bin/env python3</span>
</span><span id="L-2"><a href="#L-2"><span class="linenos">  2</span></a><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-3"><a href="#L-3"><span class="linenos">  3</span></a><span class="sd">NES ROM 下载和传输工具</span>
</span><span id="L-4"><a href="#L-4"><span class="linenos">  4</span></a>
</span><span id="L-5"><a href="#L-5"><span class="linenos">  5</span></a><span class="sd">这是一个自动化ROM下载和传输工具，专门用于RetroPie游戏系统。</span>
</span><span id="L-6"><a href="#L-6"><span class="linenos">  6</span></a><span class="sd">支持从合法ROM资源站下载游戏合集并自动传输到树莓派。</span>
</span><span id="L-7"><a href="#L-7"><span class="linenos">  7</span></a>
</span><span id="L-8"><a href="#L-8"><span class="linenos">  8</span></a><span class="sd">主要功能：</span>
</span><span id="L-9"><a href="#L-9"><span class="linenos">  9</span></a><span class="sd">- 从Archive.org等合法资源站搜索和下载ROM</span>
</span><span id="L-10"><a href="#L-10"><span class="linenos"> 10</span></a><span class="sd">- 支持断点续传和校验和验证</span>
</span><span id="L-11"><a href="#L-11"><span class="linenos"> 11</span></a><span class="sd">- 自动解压ZIP格式的ROM文件</span>
</span><span id="L-12"><a href="#L-12"><span class="linenos"> 12</span></a><span class="sd">- 通过SFTP自动传输到树莓派</span>
</span><span id="L-13"><a href="#L-13"><span class="linenos"> 13</span></a><span class="sd">- 完整的日志记录和错误处理</span>
</span><span id="L-14"><a href="#L-14"><span class="linenos"> 14</span></a><span class="sd">- 配置文件驱动的灵活配置</span>
</span><span id="L-15"><a href="#L-15"><span class="linenos"> 15</span></a>
</span><span id="L-16"><a href="#L-16"><span class="linenos"> 16</span></a><span class="sd">系统要求：</span>
</span><span id="L-17"><a href="#L-17"><span class="linenos"> 17</span></a><span class="sd">- Python 3.7+</span>
</span><span id="L-18"><a href="#L-18"><span class="linenos"> 18</span></a><span class="sd">- 网络连接</span>
</span><span id="L-19"><a href="#L-19"><span class="linenos"> 19</span></a><span class="sd">- 树莓派SSH访问权限</span>
</span><span id="L-20"><a href="#L-20"><span class="linenos"> 20</span></a>
</span><span id="L-21"><a href="#L-21"><span class="linenos"> 21</span></a><span class="sd">作者: AI Assistant</span>
</span><span id="L-22"><a href="#L-22"><span class="linenos"> 22</span></a><span class="sd">版本: 2.0.0</span>
</span><span id="L-23"><a href="#L-23"><span class="linenos"> 23</span></a><span class="sd">许可证: MIT</span>
</span><span id="L-24"><a href="#L-24"><span class="linenos"> 24</span></a><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-25"><a href="#L-25"><span class="linenos"> 25</span></a>
</span><span id="L-26"><a href="#L-26"><span class="linenos"> 26</span></a><span class="kn">import</span> <span class="nn">os</span>
</span><span id="L-27"><a href="#L-27"><span class="linenos"> 27</span></a><span class="kn">import</span> <span class="nn">sys</span>
</span><span id="L-28"><a href="#L-28"><span class="linenos"> 28</span></a><span class="kn">import</span> <span class="nn">hashlib</span>
</span><span id="L-29"><a href="#L-29"><span class="linenos"> 29</span></a><span class="kn">import</span> <span class="nn">time</span>
</span><span id="L-30"><a href="#L-30"><span class="linenos"> 30</span></a><span class="kn">import</span> <span class="nn">json</span>
</span><span id="L-31"><a href="#L-31"><span class="linenos"> 31</span></a><span class="kn">import</span> <span class="nn">zipfile</span>
</span><span id="L-32"><a href="#L-32"><span class="linenos"> 32</span></a><span class="kn">import</span> <span class="nn">argparse</span>
</span><span id="L-33"><a href="#L-33"><span class="linenos"> 33</span></a><span class="kn">from</span> <span class="nn">pathlib</span> <span class="kn">import</span> <span class="n">Path</span>
</span><span id="L-34"><a href="#L-34"><span class="linenos"> 34</span></a><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">List</span><span class="p">,</span> <span class="n">Tuple</span>
</span><span id="L-35"><a href="#L-35"><span class="linenos"> 35</span></a><span class="n">sys</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">insert</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">abspath</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">dirname</span><span class="p">(</span><span class="vm">__file__</span><span class="p">),</span> <span class="s1">&#39;../core&#39;</span><span class="p">)))</span>
</span><span id="L-36"><a href="#L-36"><span class="linenos"> 36</span></a><span class="kn">from</span> <span class="nn">logger_config</span> <span class="kn">import</span> <span class="n">get_logger</span>
</span><span id="L-37"><a href="#L-37"><span class="linenos"> 37</span></a><span class="kn">import</span> <span class="nn">requests</span>
</span><span id="L-38"><a href="#L-38"><span class="linenos"> 38</span></a><span class="kn">from</span> <span class="nn">requests.adapters</span> <span class="kn">import</span> <span class="n">HTTPAdapter</span>
</span><span id="L-39"><a href="#L-39"><span class="linenos"> 39</span></a><span class="kn">from</span> <span class="nn">urllib3.util.retry</span> <span class="kn">import</span> <span class="n">Retry</span>
</span><span id="L-40"><a href="#L-40"><span class="linenos"> 40</span></a><span class="kn">import</span> <span class="nn">paramiko</span>
</span><span id="L-41"><a href="#L-41"><span class="linenos"> 41</span></a><span class="kn">from</span> <span class="nn">tqdm</span> <span class="kn">import</span> <span class="n">tqdm</span>
</span><span id="L-42"><a href="#L-42"><span class="linenos"> 42</span></a>
</span><span id="L-43"><a href="#L-43"><span class="linenos"> 43</span></a><span class="c1"># 配置日志</span>
</span><span id="L-44"><a href="#L-44"><span class="linenos"> 44</span></a><span class="n">logger</span> <span class="o">=</span> <span class="n">get_logger</span><span class="p">(</span><span class="s2">&quot;rom_downloader&quot;</span><span class="p">,</span> <span class="s2">&quot;rom_downloader.log&quot;</span><span class="p">)</span>
</span><span id="L-45"><a href="#L-45"><span class="linenos"> 45</span></a>
</span><span id="L-46"><a href="#L-46"><span class="linenos"> 46</span></a><span class="k">class</span> <span class="nc">ROMDownloader</span><span class="p">:</span>
</span><span id="L-47"><a href="#L-47"><span class="linenos"> 47</span></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-48"><a href="#L-48"><span class="linenos"> 48</span></a><span class="sd">    ROM下载和传输工具类</span>
</span><span id="L-49"><a href="#L-49"><span class="linenos"> 49</span></a><span class="sd">    </span>
</span><span id="L-50"><a href="#L-50"><span class="linenos"> 50</span></a><span class="sd">    提供完整的ROM自动化下载和传输流程，包括：</span>
</span><span id="L-51"><a href="#L-51"><span class="linenos"> 51</span></a><span class="sd">    - ROM搜索和下载链接获取</span>
</span><span id="L-52"><a href="#L-52"><span class="linenos"> 52</span></a><span class="sd">    - 断点续传的文件下载</span>
</span><span id="L-53"><a href="#L-53"><span class="linenos"> 53</span></a><span class="sd">    - 文件完整性验证</span>
</span><span id="L-54"><a href="#L-54"><span class="linenos"> 54</span></a><span class="sd">    - 自动解压和文件处理</span>
</span><span id="L-55"><a href="#L-55"><span class="linenos"> 55</span></a><span class="sd">    - SFTP传输到树莓派</span>
</span><span id="L-56"><a href="#L-56"><span class="linenos"> 56</span></a><span class="sd">    </span>
</span><span id="L-57"><a href="#L-57"><span class="linenos"> 57</span></a><span class="sd">    属性:</span>
</span><span id="L-58"><a href="#L-58"><span class="linenos"> 58</span></a><span class="sd">        config_file (Path): 配置文件路径</span>
</span><span id="L-59"><a href="#L-59"><span class="linenos"> 59</span></a><span class="sd">        download_dir (Path): 下载目录路径</span>
</span><span id="L-60"><a href="#L-60"><span class="linenos"> 60</span></a><span class="sd">        config (Dict): 配置字典</span>
</span><span id="L-61"><a href="#L-61"><span class="linenos"> 61</span></a><span class="sd">        session (requests.Session): HTTP会话对象</span>
</span><span id="L-62"><a href="#L-62"><span class="linenos"> 62</span></a><span class="sd">    &quot;&quot;&quot;</span>
</span><span id="L-63"><a href="#L-63"><span class="linenos"> 63</span></a>    
</span><span id="L-64"><a href="#L-64"><span class="linenos"> 64</span></a>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">config_file</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;rom_config.json&quot;</span><span class="p">):</span>
</span><span id="L-65"><a href="#L-65"><span class="linenos"> 65</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-66"><a href="#L-66"><span class="linenos"> 66</span></a><span class="sd">        初始化ROM下载器</span>
</span><span id="L-67"><a href="#L-67"><span class="linenos"> 67</span></a><span class="sd">        </span>
</span><span id="L-68"><a href="#L-68"><span class="linenos"> 68</span></a><span class="sd">        Args:</span>
</span><span id="L-69"><a href="#L-69"><span class="linenos"> 69</span></a><span class="sd">            config_file (str): 配置文件路径</span>
</span><span id="L-70"><a href="#L-70"><span class="linenos"> 70</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="L-71"><a href="#L-71"><span class="linenos"> 71</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">config_file</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">config_file</span><span class="p">)</span>
</span><span id="L-72"><a href="#L-72"><span class="linenos"> 72</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s2">&quot;downloads/roms&quot;</span><span class="p">)</span>
</span><span id="L-73"><a href="#L-73"><span class="linenos"> 73</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">parents</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="L-74"><a href="#L-74"><span class="linenos"> 74</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">config</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_load_config</span><span class="p">()</span>
</span><span id="L-75"><a href="#L-75"><span class="linenos"> 75</span></a>        
</span><span id="L-76"><a href="#L-76"><span class="linenos"> 76</span></a>        <span class="c1"># 设置重试策略</span>
</span><span id="L-77"><a href="#L-77"><span class="linenos"> 77</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">session</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_setup_session</span><span class="p">()</span>
</span><span id="L-78"><a href="#L-78"><span class="linenos"> 78</span></a>        
</span><span id="L-79"><a href="#L-79"><span class="linenos"> 79</span></a>    <span class="k">def</span> <span class="nf">_load_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">:</span>
</span><span id="L-80"><a href="#L-80"><span class="linenos"> 80</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;加载配置文件&quot;&quot;&quot;</span>
</span><span id="L-81"><a href="#L-81"><span class="linenos"> 81</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">config_file</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-82"><a href="#L-82"><span class="linenos"> 82</span></a>            <span class="k">try</span><span class="p">:</span>
</span><span id="L-83"><a href="#L-83"><span class="linenos"> 83</span></a>                <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_file</span><span class="p">,</span> <span class="s1">&#39;r&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="L-84"><a href="#L-84"><span class="linenos"> 84</span></a>                    <span class="k">return</span> <span class="n">json</span><span class="o">.</span><span class="n">load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span>
</span><span id="L-85"><a href="#L-85"><span class="linenos"> 85</span></a>            <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-86"><a href="#L-86"><span class="linenos"> 86</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;加载配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-87"><a href="#L-87"><span class="linenos"> 87</span></a>        
</span><span id="L-88"><a href="#L-88"><span class="linenos"> 88</span></a>        <span class="c1"># 默认配置</span>
</span><span id="L-89"><a href="#L-89"><span class="linenos"> 89</span></a>        <span class="n">default_config</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="L-90"><a href="#L-90"><span class="linenos"> 90</span></a>            <span class="s2">&quot;rom_sources&quot;</span><span class="p">:</span> <span class="p">{</span>
</span><span id="L-91"><a href="#L-91"><span class="linenos"> 91</span></a>                <span class="s2">&quot;archive_org&quot;</span><span class="p">:</span> <span class="p">{</span>
</span><span id="L-92"><a href="#L-92"><span class="linenos"> 92</span></a>                    <span class="s2">&quot;base_url&quot;</span><span class="p">:</span> <span class="s2">&quot;https://archive.org&quot;</span><span class="p">,</span>
</span><span id="L-93"><a href="#L-93"><span class="linenos"> 93</span></a>                    <span class="s2">&quot;search_url&quot;</span><span class="p">:</span> <span class="s2">&quot;https://archive.org/advancedsearch.php&quot;</span><span class="p">,</span>
</span><span id="L-94"><a href="#L-94"><span class="linenos"> 94</span></a>                    <span class="s2">&quot;download_patterns&quot;</span><span class="p">:</span> <span class="p">[</span>
</span><span id="L-95"><a href="#L-95"><span class="linenos"> 95</span></a>                        <span class="s2">&quot;nes-100-in-1&quot;</span><span class="p">,</span>
</span><span id="L-96"><a href="#L-96"><span class="linenos"> 96</span></a>                        <span class="s2">&quot;nes-games-collection&quot;</span><span class="p">,</span>
</span><span id="L-97"><a href="#L-97"><span class="linenos"> 97</span></a>                        <span class="s2">&quot;nintendo-entertainment-system-roms&quot;</span>
</span><span id="L-98"><a href="#L-98"><span class="linenos"> 98</span></a>                    <span class="p">]</span>
</span><span id="L-99"><a href="#L-99"><span class="linenos"> 99</span></a>                <span class="p">}</span>
</span><span id="L-100"><a href="#L-100"><span class="linenos">100</span></a>            <span class="p">},</span>
</span><span id="L-101"><a href="#L-101"><span class="linenos">101</span></a>            <span class="s2">&quot;raspberry_pi&quot;</span><span class="p">:</span> <span class="p">{</span>
</span><span id="L-102"><a href="#L-102"><span class="linenos">102</span></a>                <span class="s2">&quot;host&quot;</span><span class="p">:</span> <span class="s2">&quot;*************&quot;</span><span class="p">,</span>
</span><span id="L-103"><a href="#L-103"><span class="linenos">103</span></a>                <span class="s2">&quot;port&quot;</span><span class="p">:</span> <span class="mi">22</span><span class="p">,</span>
</span><span id="L-104"><a href="#L-104"><span class="linenos">104</span></a>                <span class="s2">&quot;username&quot;</span><span class="p">:</span> <span class="s2">&quot;pi&quot;</span><span class="p">,</span>
</span><span id="L-105"><a href="#L-105"><span class="linenos">105</span></a>                <span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
</span><span id="L-106"><a href="#L-106"><span class="linenos">106</span></a>                <span class="s2">&quot;key_file&quot;</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
</span><span id="L-107"><a href="#L-107"><span class="linenos">107</span></a>                <span class="s2">&quot;roms_path&quot;</span><span class="p">:</span> <span class="s2">&quot;/home/<USER>/RetroPie/roms/nes/&quot;</span>
</span><span id="L-108"><a href="#L-108"><span class="linenos">108</span></a>            <span class="p">},</span>
</span><span id="L-109"><a href="#L-109"><span class="linenos">109</span></a>            <span class="s2">&quot;download&quot;</span><span class="p">:</span> <span class="p">{</span>
</span><span id="L-110"><a href="#L-110"><span class="linenos">110</span></a>                <span class="s2">&quot;timeout&quot;</span><span class="p">:</span> <span class="mi">30</span><span class="p">,</span>
</span><span id="L-111"><a href="#L-111"><span class="linenos">111</span></a>                <span class="s2">&quot;chunk_size&quot;</span><span class="p">:</span> <span class="mi">8192</span><span class="p">,</span>
</span><span id="L-112"><a href="#L-112"><span class="linenos">112</span></a>                <span class="s2">&quot;max_retries&quot;</span><span class="p">:</span> <span class="mi">3</span><span class="p">,</span>
</span><span id="L-113"><a href="#L-113"><span class="linenos">113</span></a>                <span class="s2">&quot;retry_delay&quot;</span><span class="p">:</span> <span class="mi">5</span>
</span><span id="L-114"><a href="#L-114"><span class="linenos">114</span></a>            <span class="p">},</span>
</span><span id="L-115"><a href="#L-115"><span class="linenos">115</span></a>            <span class="s2">&quot;verification&quot;</span><span class="p">:</span> <span class="p">{</span>
</span><span id="L-116"><a href="#L-116"><span class="linenos">116</span></a>                <span class="s2">&quot;verify_checksum&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
</span><span id="L-117"><a href="#L-117"><span class="linenos">117</span></a>                <span class="s2">&quot;checksum_algorithm&quot;</span><span class="p">:</span> <span class="s2">&quot;sha256&quot;</span>
</span><span id="L-118"><a href="#L-118"><span class="linenos">118</span></a>            <span class="p">}</span>
</span><span id="L-119"><a href="#L-119"><span class="linenos">119</span></a>        <span class="p">}</span>
</span><span id="L-120"><a href="#L-120"><span class="linenos">120</span></a>        
</span><span id="L-121"><a href="#L-121"><span class="linenos">121</span></a>        <span class="c1"># 保存默认配置</span>
</span><span id="L-122"><a href="#L-122"><span class="linenos">122</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">_save_config</span><span class="p">(</span><span class="n">default_config</span><span class="p">)</span>
</span><span id="L-123"><a href="#L-123"><span class="linenos">123</span></a>        <span class="k">return</span> <span class="n">default_config</span>
</span><span id="L-124"><a href="#L-124"><span class="linenos">124</span></a>    
</span><span id="L-125"><a href="#L-125"><span class="linenos">125</span></a>    <span class="k">def</span> <span class="nf">_save_config</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">config</span><span class="p">:</span> <span class="n">Dict</span><span class="p">):</span>
</span><span id="L-126"><a href="#L-126"><span class="linenos">126</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;保存配置文件&quot;&quot;&quot;</span>
</span><span id="L-127"><a href="#L-127"><span class="linenos">127</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-128"><a href="#L-128"><span class="linenos">128</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_file</span><span class="p">,</span> <span class="s1">&#39;w&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="L-129"><a href="#L-129"><span class="linenos">129</span></a>                <span class="n">json</span><span class="o">.</span><span class="n">dump</span><span class="p">(</span><span class="n">config</span><span class="p">,</span> <span class="n">f</span><span class="p">,</span> <span class="n">indent</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">ensure_ascii</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</span><span id="L-130"><a href="#L-130"><span class="linenos">130</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置文件已保存: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config_file</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-131"><a href="#L-131"><span class="linenos">131</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-132"><a href="#L-132"><span class="linenos">132</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;保存配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-133"><a href="#L-133"><span class="linenos">133</span></a>    
</span><span id="L-134"><a href="#L-134"><span class="linenos">134</span></a>    <span class="k">def</span> <span class="nf">_setup_session</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">requests</span><span class="o">.</span><span class="n">Session</span><span class="p">:</span>
</span><span id="L-135"><a href="#L-135"><span class="linenos">135</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;设置HTTP会话，包含重试策略&quot;&quot;&quot;</span>
</span><span id="L-136"><a href="#L-136"><span class="linenos">136</span></a>        <span class="n">session</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">Session</span><span class="p">()</span>
</span><span id="L-137"><a href="#L-137"><span class="linenos">137</span></a>        
</span><span id="L-138"><a href="#L-138"><span class="linenos">138</span></a>        <span class="n">retry_strategy</span> <span class="o">=</span> <span class="n">Retry</span><span class="p">(</span>
</span><span id="L-139"><a href="#L-139"><span class="linenos">139</span></a>            <span class="n">total</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;download&quot;</span><span class="p">][</span><span class="s2">&quot;max_retries&quot;</span><span class="p">],</span>
</span><span id="L-140"><a href="#L-140"><span class="linenos">140</span></a>            <span class="n">status_forcelist</span><span class="o">=</span><span class="p">[</span><span class="mi">429</span><span class="p">,</span> <span class="mi">500</span><span class="p">,</span> <span class="mi">502</span><span class="p">,</span> <span class="mi">503</span><span class="p">,</span> <span class="mi">504</span><span class="p">],</span>
</span><span id="L-141"><a href="#L-141"><span class="linenos">141</span></a>            <span class="n">method_whitelist</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;HEAD&quot;</span><span class="p">,</span> <span class="s2">&quot;GET&quot;</span><span class="p">,</span> <span class="s2">&quot;OPTIONS&quot;</span><span class="p">],</span>
</span><span id="L-142"><a href="#L-142"><span class="linenos">142</span></a>            <span class="n">backoff_factor</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;download&quot;</span><span class="p">][</span><span class="s2">&quot;retry_delay&quot;</span><span class="p">]</span>
</span><span id="L-143"><a href="#L-143"><span class="linenos">143</span></a>        <span class="p">)</span>
</span><span id="L-144"><a href="#L-144"><span class="linenos">144</span></a>        
</span><span id="L-145"><a href="#L-145"><span class="linenos">145</span></a>        <span class="n">adapter</span> <span class="o">=</span> <span class="n">HTTPAdapter</span><span class="p">(</span><span class="n">max_retries</span><span class="o">=</span><span class="n">retry_strategy</span><span class="p">)</span>
</span><span id="L-146"><a href="#L-146"><span class="linenos">146</span></a>        <span class="n">session</span><span class="o">.</span><span class="n">mount</span><span class="p">(</span><span class="s2">&quot;http://&quot;</span><span class="p">,</span> <span class="n">adapter</span><span class="p">)</span>
</span><span id="L-147"><a href="#L-147"><span class="linenos">147</span></a>        <span class="n">session</span><span class="o">.</span><span class="n">mount</span><span class="p">(</span><span class="s2">&quot;https://&quot;</span><span class="p">,</span> <span class="n">adapter</span><span class="p">)</span>
</span><span id="L-148"><a href="#L-148"><span class="linenos">148</span></a>        
</span><span id="L-149"><a href="#L-149"><span class="linenos">149</span></a>        <span class="k">return</span> <span class="n">session</span>
</span><span id="L-150"><a href="#L-150"><span class="linenos">150</span></a>    
</span><span id="L-151"><a href="#L-151"><span class="linenos">151</span></a>    <span class="k">def</span> <span class="nf">search_roms</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">query</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;nes 100 in 1&quot;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Dict</span><span class="p">]:</span>
</span><span id="L-152"><a href="#L-152"><span class="linenos">152</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;搜索ROM文件&quot;&quot;&quot;</span>
</span><span id="L-153"><a href="#L-153"><span class="linenos">153</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;搜索ROM: </span><span class="si">{</span><span class="n">query</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-154"><a href="#L-154"><span class="linenos">154</span></a>        
</span><span id="L-155"><a href="#L-155"><span class="linenos">155</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-156"><a href="#L-156"><span class="linenos">156</span></a>            <span class="n">search_url</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;rom_sources&quot;</span><span class="p">][</span><span class="s2">&quot;archive_org&quot;</span><span class="p">][</span><span class="s2">&quot;search_url&quot;</span><span class="p">]</span>
</span><span id="L-157"><a href="#L-157"><span class="linenos">157</span></a>            <span class="n">params</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="L-158"><a href="#L-158"><span class="linenos">158</span></a>                <span class="s2">&quot;q&quot;</span><span class="p">:</span> <span class="n">query</span><span class="p">,</span>
</span><span id="L-159"><a href="#L-159"><span class="linenos">159</span></a>                <span class="s2">&quot;output&quot;</span><span class="p">:</span> <span class="s2">&quot;json&quot;</span><span class="p">,</span>
</span><span id="L-160"><a href="#L-160"><span class="linenos">160</span></a>                <span class="s2">&quot;rows&quot;</span><span class="p">:</span> <span class="mi">50</span><span class="p">,</span>
</span><span id="L-161"><a href="#L-161"><span class="linenos">161</span></a>                <span class="s2">&quot;sort&quot;</span><span class="p">:</span> <span class="s2">&quot;downloads desc&quot;</span>
</span><span id="L-162"><a href="#L-162"><span class="linenos">162</span></a>            <span class="p">}</span>
</span><span id="L-163"><a href="#L-163"><span class="linenos">163</span></a>            
</span><span id="L-164"><a href="#L-164"><span class="linenos">164</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">search_url</span><span class="p">,</span> <span class="n">params</span><span class="o">=</span><span class="n">params</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="L-165"><a href="#L-165"><span class="linenos">165</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="L-166"><a href="#L-166"><span class="linenos">166</span></a>            
</span><span id="L-167"><a href="#L-167"><span class="linenos">167</span></a>            <span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
</span><span id="L-168"><a href="#L-168"><span class="linenos">168</span></a>            <span class="n">results</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="L-169"><a href="#L-169"><span class="linenos">169</span></a>            
</span><span id="L-170"><a href="#L-170"><span class="linenos">170</span></a>            <span class="k">for</span> <span class="n">doc</span> <span class="ow">in</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;response&quot;</span><span class="p">,</span> <span class="p">{})</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;docs&quot;</span><span class="p">,</span> <span class="p">[]):</span>
</span><span id="L-171"><a href="#L-171"><span class="linenos">171</span></a>                <span class="k">if</span> <span class="s2">&quot;downloads&quot;</span> <span class="ow">in</span> <span class="n">doc</span> <span class="ow">and</span> <span class="n">doc</span><span class="p">[</span><span class="s2">&quot;downloads&quot;</span><span class="p">]</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="L-172"><a href="#L-172"><span class="linenos">172</span></a>                    <span class="n">result</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="L-173"><a href="#L-173"><span class="linenos">173</span></a>                        <span class="s2">&quot;identifier&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;identifier&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">),</span>
</span><span id="L-174"><a href="#L-174"><span class="linenos">174</span></a>                        <span class="s2">&quot;title&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;title&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">),</span>
</span><span id="L-175"><a href="#L-175"><span class="linenos">175</span></a>                        <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;description&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">),</span>
</span><span id="L-176"><a href="#L-176"><span class="linenos">176</span></a>                        <span class="s2">&quot;downloads&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;downloads&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>
</span><span id="L-177"><a href="#L-177"><span class="linenos">177</span></a>                        <span class="s2">&quot;files&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;files&quot;</span><span class="p">,</span> <span class="p">[])</span>
</span><span id="L-178"><a href="#L-178"><span class="linenos">178</span></a>                    <span class="p">}</span>
</span><span id="L-179"><a href="#L-179"><span class="linenos">179</span></a>                    <span class="n">results</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">result</span><span class="p">)</span>
</span><span id="L-180"><a href="#L-180"><span class="linenos">180</span></a>            
</span><span id="L-181"><a href="#L-181"><span class="linenos">181</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到 </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">results</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个结果&quot;</span><span class="p">)</span>
</span><span id="L-182"><a href="#L-182"><span class="linenos">182</span></a>            <span class="k">return</span> <span class="n">results</span>
</span><span id="L-183"><a href="#L-183"><span class="linenos">183</span></a>            
</span><span id="L-184"><a href="#L-184"><span class="linenos">184</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-185"><a href="#L-185"><span class="linenos">185</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;搜索ROM失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-186"><a href="#L-186"><span class="linenos">186</span></a>            <span class="k">return</span> <span class="p">[]</span>
</span><span id="L-187"><a href="#L-187"><span class="linenos">187</span></a>    
</span><span id="L-188"><a href="#L-188"><span class="linenos">188</span></a>    <span class="k">def</span> <span class="nf">get_download_url</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">identifier</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="L-189"><a href="#L-189"><span class="linenos">189</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;获取下载链接&quot;&quot;&quot;</span>
</span><span id="L-190"><a href="#L-190"><span class="linenos">190</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-191"><a href="#L-191"><span class="linenos">191</span></a>            <span class="n">metadata_url</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s1">&#39;rom_sources&#39;</span><span class="p">][</span><span class="s1">&#39;archive_org&#39;</span><span class="p">][</span><span class="s1">&#39;base_url&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">/metadata/</span><span class="si">{</span><span class="n">identifier</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="L-192"><a href="#L-192"><span class="linenos">192</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">metadata_url</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="L-193"><a href="#L-193"><span class="linenos">193</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="L-194"><a href="#L-194"><span class="linenos">194</span></a>            
</span><span id="L-195"><a href="#L-195"><span class="linenos">195</span></a>            <span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
</span><span id="L-196"><a href="#L-196"><span class="linenos">196</span></a>            <span class="n">files</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;files&quot;</span><span class="p">,</span> <span class="p">{})</span>
</span><span id="L-197"><a href="#L-197"><span class="linenos">197</span></a>            
</span><span id="L-198"><a href="#L-198"><span class="linenos">198</span></a>            <span class="c1"># 查找ZIP文件</span>
</span><span id="L-199"><a href="#L-199"><span class="linenos">199</span></a>            <span class="k">for</span> <span class="n">filename</span><span class="p">,</span> <span class="n">file_info</span> <span class="ow">in</span> <span class="n">files</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><span id="L-200"><a href="#L-200"><span class="linenos">200</span></a>                <span class="k">if</span> <span class="n">filename</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span><span class="o">.</span><span class="n">endswith</span><span class="p">(</span><span class="s1">&#39;.zip&#39;</span><span class="p">):</span>
</span><span id="L-201"><a href="#L-201"><span class="linenos">201</span></a>                    <span class="n">download_url</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s1">&#39;rom_sources&#39;</span><span class="p">][</span><span class="s1">&#39;archive_org&#39;</span><span class="p">][</span><span class="s1">&#39;base_url&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">/download/</span><span class="si">{</span><span class="n">identifier</span><span class="si">}</span><span class="s2">/</span><span class="si">{</span><span class="n">filename</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="L-202"><a href="#L-202"><span class="linenos">202</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到下载链接: </span><span class="si">{</span><span class="n">download_url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-203"><a href="#L-203"><span class="linenos">203</span></a>                    <span class="k">return</span> <span class="n">download_url</span>
</span><span id="L-204"><a href="#L-204"><span class="linenos">204</span></a>            
</span><span id="L-205"><a href="#L-205"><span class="linenos">205</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;未找到ZIP文件: </span><span class="si">{</span><span class="n">identifier</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-206"><a href="#L-206"><span class="linenos">206</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-207"><a href="#L-207"><span class="linenos">207</span></a>            
</span><span id="L-208"><a href="#L-208"><span class="linenos">208</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-209"><a href="#L-209"><span class="linenos">209</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;获取下载链接失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-210"><a href="#L-210"><span class="linenos">210</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-211"><a href="#L-211"><span class="linenos">211</span></a>    
</span><span id="L-212"><a href="#L-212"><span class="linenos">212</span></a>    <span class="k">def</span> <span class="nf">download_file</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">url</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">filename</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Path</span><span class="p">]:</span>
</span><span id="L-213"><a href="#L-213"><span class="linenos">213</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;下载文件，支持断点续传&quot;&quot;&quot;</span>
</span><span id="L-214"><a href="#L-214"><span class="linenos">214</span></a>        <span class="n">file_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="n">filename</span>
</span><span id="L-215"><a href="#L-215"><span class="linenos">215</span></a>        
</span><span id="L-216"><a href="#L-216"><span class="linenos">216</span></a>        <span class="c1"># 检查是否已存在</span>
</span><span id="L-217"><a href="#L-217"><span class="linenos">217</span></a>        <span class="k">if</span> <span class="n">file_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-218"><a href="#L-218"><span class="linenos">218</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件已存在: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-219"><a href="#L-219"><span class="linenos">219</span></a>            <span class="k">return</span> <span class="n">file_path</span>
</span><span id="L-220"><a href="#L-220"><span class="linenos">220</span></a>        
</span><span id="L-221"><a href="#L-221"><span class="linenos">221</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-222"><a href="#L-222"><span class="linenos">222</span></a>            <span class="c1"># 获取文件大小</span>
</span><span id="L-223"><a href="#L-223"><span class="linenos">223</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">head</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="L-224"><a href="#L-224"><span class="linenos">224</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="L-225"><a href="#L-225"><span class="linenos">225</span></a>            
</span><span id="L-226"><a href="#L-226"><span class="linenos">226</span></a>            <span class="n">total_size</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">headers</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;content-length&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
</span><span id="L-227"><a href="#L-227"><span class="linenos">227</span></a>            
</span><span id="L-228"><a href="#L-228"><span class="linenos">228</span></a>            <span class="c1"># 检查是否有部分下载的文件</span>
</span><span id="L-229"><a href="#L-229"><span class="linenos">229</span></a>            <span class="n">temp_path</span> <span class="o">=</span> <span class="n">file_path</span><span class="o">.</span><span class="n">with_suffix</span><span class="p">(</span><span class="s1">&#39;.tmp&#39;</span><span class="p">)</span>
</span><span id="L-230"><a href="#L-230"><span class="linenos">230</span></a>            <span class="n">resume_pos</span> <span class="o">=</span> <span class="mi">0</span>
</span><span id="L-231"><a href="#L-231"><span class="linenos">231</span></a>            
</span><span id="L-232"><a href="#L-232"><span class="linenos">232</span></a>            <span class="k">if</span> <span class="n">temp_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-233"><a href="#L-233"><span class="linenos">233</span></a>                <span class="n">resume_pos</span> <span class="o">=</span> <span class="n">temp_path</span><span class="o">.</span><span class="n">stat</span><span class="p">()</span><span class="o">.</span><span class="n">st_size</span>
</span><span id="L-234"><a href="#L-234"><span class="linenos">234</span></a>                <span class="k">if</span> <span class="n">resume_pos</span> <span class="o">&gt;=</span> <span class="n">total_size</span><span class="p">:</span>
</span><span id="L-235"><a href="#L-235"><span class="linenos">235</span></a>                    <span class="c1"># 文件已完整下载</span>
</span><span id="L-236"><a href="#L-236"><span class="linenos">236</span></a>                    <span class="n">temp_path</span><span class="o">.</span><span class="n">rename</span><span class="p">(</span><span class="n">file_path</span><span class="p">)</span>
</span><span id="L-237"><a href="#L-237"><span class="linenos">237</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件下载完成: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-238"><a href="#L-238"><span class="linenos">238</span></a>                    <span class="k">return</span> <span class="n">file_path</span>
</span><span id="L-239"><a href="#L-239"><span class="linenos">239</span></a>            
</span><span id="L-240"><a href="#L-240"><span class="linenos">240</span></a>            <span class="c1"># 设置断点续传头</span>
</span><span id="L-241"><a href="#L-241"><span class="linenos">241</span></a>            <span class="n">headers</span> <span class="o">=</span> <span class="p">{}</span>
</span><span id="L-242"><a href="#L-242"><span class="linenos">242</span></a>            <span class="k">if</span> <span class="n">resume_pos</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="L-243"><a href="#L-243"><span class="linenos">243</span></a>                <span class="n">headers</span><span class="p">[</span><span class="s1">&#39;Range&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="sa">f</span><span class="s1">&#39;bytes=</span><span class="si">{</span><span class="n">resume_pos</span><span class="si">}</span><span class="s1">-&#39;</span>
</span><span id="L-244"><a href="#L-244"><span class="linenos">244</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;断点续传: </span><span class="si">{</span><span class="n">resume_pos</span><span class="si">}</span><span class="s2"> bytes&quot;</span><span class="p">)</span>
</span><span id="L-245"><a href="#L-245"><span class="linenos">245</span></a>            
</span><span id="L-246"><a href="#L-246"><span class="linenos">246</span></a>            <span class="c1"># 开始下载</span>
</span><span id="L-247"><a href="#L-247"><span class="linenos">247</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">headers</span><span class="o">=</span><span class="n">headers</span><span class="p">,</span> <span class="n">stream</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="L-248"><a href="#L-248"><span class="linenos">248</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="L-249"><a href="#L-249"><span class="linenos">249</span></a>            
</span><span id="L-250"><a href="#L-250"><span class="linenos">250</span></a>            <span class="n">mode</span> <span class="o">=</span> <span class="s1">&#39;ab&#39;</span> <span class="k">if</span> <span class="n">resume_pos</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="k">else</span> <span class="s1">&#39;wb&#39;</span>
</span><span id="L-251"><a href="#L-251"><span class="linenos">251</span></a>            
</span><span id="L-252"><a href="#L-252"><span class="linenos">252</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">temp_path</span><span class="p">,</span> <span class="n">mode</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="L-253"><a href="#L-253"><span class="linenos">253</span></a>                <span class="k">with</span> <span class="n">tqdm</span><span class="p">(</span>
</span><span id="L-254"><a href="#L-254"><span class="linenos">254</span></a>                    <span class="n">total</span><span class="o">=</span><span class="n">total_size</span><span class="p">,</span>
</span><span id="L-255"><a href="#L-255"><span class="linenos">255</span></a>                    <span class="n">initial</span><span class="o">=</span><span class="n">resume_pos</span><span class="p">,</span>
</span><span id="L-256"><a href="#L-256"><span class="linenos">256</span></a>                    <span class="n">unit</span><span class="o">=</span><span class="s1">&#39;B&#39;</span><span class="p">,</span>
</span><span id="L-257"><a href="#L-257"><span class="linenos">257</span></a>                    <span class="n">unit_scale</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
</span><span id="L-258"><a href="#L-258"><span class="linenos">258</span></a>                    <span class="n">desc</span><span class="o">=</span><span class="n">filename</span>
</span><span id="L-259"><a href="#L-259"><span class="linenos">259</span></a>                <span class="p">)</span> <span class="k">as</span> <span class="n">pbar</span><span class="p">:</span>
</span><span id="L-260"><a href="#L-260"><span class="linenos">260</span></a>                    
</span><span id="L-261"><a href="#L-261"><span class="linenos">261</span></a>                    <span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="n">response</span><span class="o">.</span><span class="n">iter_content</span><span class="p">(</span><span class="n">chunk_size</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;download&quot;</span><span class="p">][</span><span class="s2">&quot;chunk_size&quot;</span><span class="p">]):</span>
</span><span id="L-262"><a href="#L-262"><span class="linenos">262</span></a>                        <span class="k">if</span> <span class="n">chunk</span><span class="p">:</span>
</span><span id="L-263"><a href="#L-263"><span class="linenos">263</span></a>                            <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</span><span id="L-264"><a href="#L-264"><span class="linenos">264</span></a>                            <span class="n">pbar</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">chunk</span><span class="p">))</span>
</span><span id="L-265"><a href="#L-265"><span class="linenos">265</span></a>            
</span><span id="L-266"><a href="#L-266"><span class="linenos">266</span></a>            <span class="c1"># 重命名临时文件</span>
</span><span id="L-267"><a href="#L-267"><span class="linenos">267</span></a>            <span class="n">temp_path</span><span class="o">.</span><span class="n">rename</span><span class="p">(</span><span class="n">file_path</span><span class="p">)</span>
</span><span id="L-268"><a href="#L-268"><span class="linenos">268</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载完成: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-269"><a href="#L-269"><span class="linenos">269</span></a>            <span class="k">return</span> <span class="n">file_path</span>
</span><span id="L-270"><a href="#L-270"><span class="linenos">270</span></a>            
</span><span id="L-271"><a href="#L-271"><span class="linenos">271</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-272"><a href="#L-272"><span class="linenos">272</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-273"><a href="#L-273"><span class="linenos">273</span></a>            <span class="c1"># 清理临时文件</span>
</span><span id="L-274"><a href="#L-274"><span class="linenos">274</span></a>            <span class="k">if</span> <span class="n">temp_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-275"><a href="#L-275"><span class="linenos">275</span></a>                <span class="n">temp_path</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
</span><span id="L-276"><a href="#L-276"><span class="linenos">276</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-277"><a href="#L-277"><span class="linenos">277</span></a>    
</span><span id="L-278"><a href="#L-278"><span class="linenos">278</span></a>    <span class="k">def</span> <span class="nf">verify_file</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">file_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-279"><a href="#L-279"><span class="linenos">279</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;验证文件完整性&quot;&quot;&quot;</span>
</span><span id="L-280"><a href="#L-280"><span class="linenos">280</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;verification&quot;</span><span class="p">][</span><span class="s2">&quot;verify_checksum&quot;</span><span class="p">]:</span>
</span><span id="L-281"><a href="#L-281"><span class="linenos">281</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-282"><a href="#L-282"><span class="linenos">282</span></a>        
</span><span id="L-283"><a href="#L-283"><span class="linenos">283</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;验证文件完整性: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-284"><a href="#L-284"><span class="linenos">284</span></a>        
</span><span id="L-285"><a href="#L-285"><span class="linenos">285</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-286"><a href="#L-286"><span class="linenos">286</span></a>            <span class="n">algorithm</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;verification&quot;</span><span class="p">][</span><span class="s2">&quot;checksum_algorithm&quot;</span><span class="p">]</span>
</span><span id="L-287"><a href="#L-287"><span class="linenos">287</span></a>            <span class="n">hash_func</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">hashlib</span><span class="p">,</span> <span class="n">algorithm</span><span class="p">)()</span>
</span><span id="L-288"><a href="#L-288"><span class="linenos">288</span></a>            
</span><span id="L-289"><a href="#L-289"><span class="linenos">289</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">file_path</span><span class="p">,</span> <span class="s1">&#39;rb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="L-290"><a href="#L-290"><span class="linenos">290</span></a>                <span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="nb">iter</span><span class="p">(</span><span class="k">lambda</span><span class="p">:</span> <span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="mi">8192</span><span class="p">),</span> <span class="sa">b</span><span class="s2">&quot;&quot;</span><span class="p">):</span>
</span><span id="L-291"><a href="#L-291"><span class="linenos">291</span></a>                    <span class="n">hash_func</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</span><span id="L-292"><a href="#L-292"><span class="linenos">292</span></a>            
</span><span id="L-293"><a href="#L-293"><span class="linenos">293</span></a>            <span class="n">checksum</span> <span class="o">=</span> <span class="n">hash_func</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
</span><span id="L-294"><a href="#L-294"><span class="linenos">294</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件校验和 (</span><span class="si">{</span><span class="n">algorithm</span><span class="si">}</span><span class="s2">): </span><span class="si">{</span><span class="n">checksum</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-295"><a href="#L-295"><span class="linenos">295</span></a>            
</span><span id="L-296"><a href="#L-296"><span class="linenos">296</span></a>            <span class="c1"># 这里可以添加预定义的校验和验证</span>
</span><span id="L-297"><a href="#L-297"><span class="linenos">297</span></a>            <span class="c1"># 由于ROM文件可能来自不同源，暂时只计算校验和</span>
</span><span id="L-298"><a href="#L-298"><span class="linenos">298</span></a>            
</span><span id="L-299"><a href="#L-299"><span class="linenos">299</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-300"><a href="#L-300"><span class="linenos">300</span></a>            
</span><span id="L-301"><a href="#L-301"><span class="linenos">301</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-302"><a href="#L-302"><span class="linenos">302</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件验证失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-303"><a href="#L-303"><span class="linenos">303</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-304"><a href="#L-304"><span class="linenos">304</span></a>    
</span><span id="L-305"><a href="#L-305"><span class="linenos">305</span></a>    <span class="k">def</span> <span class="nf">extract_roms</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">zip_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Path</span><span class="p">]:</span>
</span><span id="L-306"><a href="#L-306"><span class="linenos">306</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;解压ROM文件&quot;&quot;&quot;</span>
</span><span id="L-307"><a href="#L-307"><span class="linenos">307</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压ROM文件: </span><span class="si">{</span><span class="n">zip_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-308"><a href="#L-308"><span class="linenos">308</span></a>        
</span><span id="L-309"><a href="#L-309"><span class="linenos">309</span></a>        <span class="n">rom_files</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="L-310"><a href="#L-310"><span class="linenos">310</span></a>        <span class="n">extract_dir</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="s2">&quot;extracted&quot;</span>
</span><span id="L-311"><a href="#L-311"><span class="linenos">311</span></a>        <span class="n">extract_dir</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="L-312"><a href="#L-312"><span class="linenos">312</span></a>        
</span><span id="L-313"><a href="#L-313"><span class="linenos">313</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-314"><a href="#L-314"><span class="linenos">314</span></a>            <span class="k">with</span> <span class="n">zipfile</span><span class="o">.</span><span class="n">ZipFile</span><span class="p">(</span><span class="n">zip_path</span><span class="p">,</span> <span class="s1">&#39;r&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">zip_ref</span><span class="p">:</span>
</span><span id="L-315"><a href="#L-315"><span class="linenos">315</span></a>                <span class="c1"># 列出所有文件</span>
</span><span id="L-316"><a href="#L-316"><span class="linenos">316</span></a>                <span class="n">file_list</span> <span class="o">=</span> <span class="n">zip_ref</span><span class="o">.</span><span class="n">namelist</span><span class="p">()</span>
</span><span id="L-317"><a href="#L-317"><span class="linenos">317</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;ZIP文件包含 </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">file_list</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个文件&quot;</span><span class="p">)</span>
</span><span id="L-318"><a href="#L-318"><span class="linenos">318</span></a>                
</span><span id="L-319"><a href="#L-319"><span class="linenos">319</span></a>                <span class="c1"># 过滤ROM文件</span>
</span><span id="L-320"><a href="#L-320"><span class="linenos">320</span></a>                <span class="n">rom_extensions</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;.nes&#39;</span><span class="p">,</span> <span class="s1">&#39;.NES&#39;</span><span class="p">]</span>
</span><span id="L-321"><a href="#L-321"><span class="linenos">321</span></a>                <span class="n">rom_files_in_zip</span> <span class="o">=</span> <span class="p">[</span><span class="n">f</span> <span class="k">for</span> <span class="n">f</span> <span class="ow">in</span> <span class="n">file_list</span> <span class="k">if</span> <span class="nb">any</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">endswith</span><span class="p">(</span><span class="n">ext</span><span class="p">)</span> <span class="k">for</span> <span class="n">ext</span> <span class="ow">in</span> <span class="n">rom_extensions</span><span class="p">)]</span>
</span><span id="L-322"><a href="#L-322"><span class="linenos">322</span></a>                
</span><span id="L-323"><a href="#L-323"><span class="linenos">323</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到 </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">rom_files_in_zip</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个ROM文件&quot;</span><span class="p">)</span>
</span><span id="L-324"><a href="#L-324"><span class="linenos">324</span></a>                
</span><span id="L-325"><a href="#L-325"><span class="linenos">325</span></a>                <span class="c1"># 解压ROM文件</span>
</span><span id="L-326"><a href="#L-326"><span class="linenos">326</span></a>                <span class="k">for</span> <span class="n">rom_file</span> <span class="ow">in</span> <span class="n">rom_files_in_zip</span><span class="p">:</span>
</span><span id="L-327"><a href="#L-327"><span class="linenos">327</span></a>                    <span class="k">try</span><span class="p">:</span>
</span><span id="L-328"><a href="#L-328"><span class="linenos">328</span></a>                        <span class="n">zip_ref</span><span class="o">.</span><span class="n">extract</span><span class="p">(</span><span class="n">rom_file</span><span class="p">,</span> <span class="n">extract_dir</span><span class="p">)</span>
</span><span id="L-329"><a href="#L-329"><span class="linenos">329</span></a>                        <span class="n">rom_path</span> <span class="o">=</span> <span class="n">extract_dir</span> <span class="o">/</span> <span class="n">rom_file</span>
</span><span id="L-330"><a href="#L-330"><span class="linenos">330</span></a>                        <span class="n">rom_files</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">rom_path</span><span class="p">)</span>
</span><span id="L-331"><a href="#L-331"><span class="linenos">331</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压: </span><span class="si">{</span><span class="n">rom_file</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-332"><a href="#L-332"><span class="linenos">332</span></a>                    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-333"><a href="#L-333"><span class="linenos">333</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压文件失败 </span><span class="si">{</span><span class="n">rom_file</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-334"><a href="#L-334"><span class="linenos">334</span></a>            
</span><span id="L-335"><a href="#L-335"><span class="linenos">335</span></a>            <span class="k">return</span> <span class="n">rom_files</span>
</span><span id="L-336"><a href="#L-336"><span class="linenos">336</span></a>            
</span><span id="L-337"><a href="#L-337"><span class="linenos">337</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-338"><a href="#L-338"><span class="linenos">338</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-339"><a href="#L-339"><span class="linenos">339</span></a>            <span class="k">return</span> <span class="p">[]</span>
</span><span id="L-340"><a href="#L-340"><span class="linenos">340</span></a>    
</span><span id="L-341"><a href="#L-341"><span class="linenos">341</span></a>    <span class="k">def</span> <span class="nf">connect_sftp</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">paramiko</span><span class="o">.</span><span class="n">SSHClient</span><span class="p">]:</span>
</span><span id="L-342"><a href="#L-342"><span class="linenos">342</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;连接SFTP服务器&quot;&quot;&quot;</span>
</span><span id="L-343"><a href="#L-343"><span class="linenos">343</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;连接树莓派SFTP服务器...&quot;</span><span class="p">)</span>
</span><span id="L-344"><a href="#L-344"><span class="linenos">344</span></a>        
</span><span id="L-345"><a href="#L-345"><span class="linenos">345</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-346"><a href="#L-346"><span class="linenos">346</span></a>            <span class="n">ssh</span> <span class="o">=</span> <span class="n">paramiko</span><span class="o">.</span><span class="n">SSHClient</span><span class="p">()</span>
</span><span id="L-347"><a href="#L-347"><span class="linenos">347</span></a>            <span class="n">ssh</span><span class="o">.</span><span class="n">set_missing_host_key_policy</span><span class="p">(</span><span class="n">paramiko</span><span class="o">.</span><span class="n">AutoAddPolicy</span><span class="p">())</span>
</span><span id="L-348"><a href="#L-348"><span class="linenos">348</span></a>            
</span><span id="L-349"><a href="#L-349"><span class="linenos">349</span></a>            <span class="n">pi_config</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;raspberry_pi&quot;</span><span class="p">]</span>
</span><span id="L-350"><a href="#L-350"><span class="linenos">350</span></a>            
</span><span id="L-351"><a href="#L-351"><span class="linenos">351</span></a>            <span class="c1"># 使用密钥文件或密码</span>
</span><span id="L-352"><a href="#L-352"><span class="linenos">352</span></a>            <span class="k">if</span> <span class="n">pi_config</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;key_file&quot;</span><span class="p">)</span> <span class="ow">and</span> <span class="n">Path</span><span class="p">(</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;key_file&quot;</span><span class="p">])</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-353"><a href="#L-353"><span class="linenos">353</span></a>                <span class="n">ssh</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span>
</span><span id="L-354"><a href="#L-354"><span class="linenos">354</span></a>                    <span class="n">hostname</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;host&quot;</span><span class="p">],</span>
</span><span id="L-355"><a href="#L-355"><span class="linenos">355</span></a>                    <span class="n">port</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;port&quot;</span><span class="p">],</span>
</span><span id="L-356"><a href="#L-356"><span class="linenos">356</span></a>                    <span class="n">username</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;username&quot;</span><span class="p">],</span>
</span><span id="L-357"><a href="#L-357"><span class="linenos">357</span></a>                    <span class="n">key_filename</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;key_file&quot;</span><span class="p">],</span>
</span><span id="L-358"><a href="#L-358"><span class="linenos">358</span></a>                    <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span>
</span><span id="L-359"><a href="#L-359"><span class="linenos">359</span></a>                <span class="p">)</span>
</span><span id="L-360"><a href="#L-360"><span class="linenos">360</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="L-361"><a href="#L-361"><span class="linenos">361</span></a>                <span class="n">ssh</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span>
</span><span id="L-362"><a href="#L-362"><span class="linenos">362</span></a>                    <span class="n">hostname</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;host&quot;</span><span class="p">],</span>
</span><span id="L-363"><a href="#L-363"><span class="linenos">363</span></a>                    <span class="n">port</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;port&quot;</span><span class="p">],</span>
</span><span id="L-364"><a href="#L-364"><span class="linenos">364</span></a>                    <span class="n">username</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;username&quot;</span><span class="p">],</span>
</span><span id="L-365"><a href="#L-365"><span class="linenos">365</span></a>                    <span class="n">password</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;password&quot;</span><span class="p">],</span>
</span><span id="L-366"><a href="#L-366"><span class="linenos">366</span></a>                    <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span>
</span><span id="L-367"><a href="#L-367"><span class="linenos">367</span></a>                <span class="p">)</span>
</span><span id="L-368"><a href="#L-368"><span class="linenos">368</span></a>            
</span><span id="L-369"><a href="#L-369"><span class="linenos">369</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;SFTP连接成功&quot;</span><span class="p">)</span>
</span><span id="L-370"><a href="#L-370"><span class="linenos">370</span></a>            <span class="k">return</span> <span class="n">ssh</span>
</span><span id="L-371"><a href="#L-371"><span class="linenos">371</span></a>            
</span><span id="L-372"><a href="#L-372"><span class="linenos">372</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-373"><a href="#L-373"><span class="linenos">373</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;SFTP连接失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-374"><a href="#L-374"><span class="linenos">374</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-375"><a href="#L-375"><span class="linenos">375</span></a>    
</span><span id="L-376"><a href="#L-376"><span class="linenos">376</span></a>    <span class="k">def</span> <span class="nf">upload_roms</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">rom_files</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">Path</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-377"><a href="#L-377"><span class="linenos">377</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;上传ROM文件到树莓派&quot;&quot;&quot;</span>
</span><span id="L-378"><a href="#L-378"><span class="linenos">378</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">rom_files</span><span class="p">:</span>
</span><span id="L-379"><a href="#L-379"><span class="linenos">379</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;没有ROM文件需要上传&quot;</span><span class="p">)</span>
</span><span id="L-380"><a href="#L-380"><span class="linenos">380</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-381"><a href="#L-381"><span class="linenos">381</span></a>        
</span><span id="L-382"><a href="#L-382"><span class="linenos">382</span></a>        <span class="n">ssh</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">connect_sftp</span><span class="p">()</span>
</span><span id="L-383"><a href="#L-383"><span class="linenos">383</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">ssh</span><span class="p">:</span>
</span><span id="L-384"><a href="#L-384"><span class="linenos">384</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-385"><a href="#L-385"><span class="linenos">385</span></a>        
</span><span id="L-386"><a href="#L-386"><span class="linenos">386</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-387"><a href="#L-387"><span class="linenos">387</span></a>            <span class="n">sftp</span> <span class="o">=</span> <span class="n">ssh</span><span class="o">.</span><span class="n">open_sftp</span><span class="p">()</span>
</span><span id="L-388"><a href="#L-388"><span class="linenos">388</span></a>            <span class="n">pi_config</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;raspberry_pi&quot;</span><span class="p">]</span>
</span><span id="L-389"><a href="#L-389"><span class="linenos">389</span></a>            <span class="n">remote_path</span> <span class="o">=</span> <span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;roms_path&quot;</span><span class="p">]</span>
</span><span id="L-390"><a href="#L-390"><span class="linenos">390</span></a>            
</span><span id="L-391"><a href="#L-391"><span class="linenos">391</span></a>            <span class="c1"># 确保远程目录存在</span>
</span><span id="L-392"><a href="#L-392"><span class="linenos">392</span></a>            <span class="k">try</span><span class="p">:</span>
</span><span id="L-393"><a href="#L-393"><span class="linenos">393</span></a>                <span class="n">sftp</span><span class="o">.</span><span class="n">stat</span><span class="p">(</span><span class="n">remote_path</span><span class="p">)</span>
</span><span id="L-394"><a href="#L-394"><span class="linenos">394</span></a>            <span class="k">except</span> <span class="ne">FileNotFoundError</span><span class="p">:</span>
</span><span id="L-395"><a href="#L-395"><span class="linenos">395</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;创建远程目录: </span><span class="si">{</span><span class="n">remote_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-396"><a href="#L-396"><span class="linenos">396</span></a>                <span class="n">sftp</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">remote_path</span><span class="p">)</span>
</span><span id="L-397"><a href="#L-397"><span class="linenos">397</span></a>            
</span><span id="L-398"><a href="#L-398"><span class="linenos">398</span></a>            <span class="n">uploaded_count</span> <span class="o">=</span> <span class="mi">0</span>
</span><span id="L-399"><a href="#L-399"><span class="linenos">399</span></a>            
</span><span id="L-400"><a href="#L-400"><span class="linenos">400</span></a>            <span class="k">for</span> <span class="n">rom_file</span> <span class="ow">in</span> <span class="n">rom_files</span><span class="p">:</span>
</span><span id="L-401"><a href="#L-401"><span class="linenos">401</span></a>                <span class="k">try</span><span class="p">:</span>
</span><span id="L-402"><a href="#L-402"><span class="linenos">402</span></a>                    <span class="n">remote_file</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">remote_path</span><span class="si">}</span><span class="s2">/</span><span class="si">{</span><span class="n">rom_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="L-403"><a href="#L-403"><span class="linenos">403</span></a>                    
</span><span id="L-404"><a href="#L-404"><span class="linenos">404</span></a>                    <span class="c1"># 检查文件是否已存在</span>
</span><span id="L-405"><a href="#L-405"><span class="linenos">405</span></a>                    <span class="k">try</span><span class="p">:</span>
</span><span id="L-406"><a href="#L-406"><span class="linenos">406</span></a>                        <span class="n">remote_stat</span> <span class="o">=</span> <span class="n">sftp</span><span class="o">.</span><span class="n">stat</span><span class="p">(</span><span class="n">remote_file</span><span class="p">)</span>
</span><span id="L-407"><a href="#L-407"><span class="linenos">407</span></a>                        <span class="n">local_stat</span> <span class="o">=</span> <span class="n">rom_file</span><span class="o">.</span><span class="n">stat</span><span class="p">()</span>
</span><span id="L-408"><a href="#L-408"><span class="linenos">408</span></a>                        
</span><span id="L-409"><a href="#L-409"><span class="linenos">409</span></a>                        <span class="k">if</span> <span class="n">remote_stat</span><span class="o">.</span><span class="n">st_size</span> <span class="o">==</span> <span class="n">local_stat</span><span class="o">.</span><span class="n">st_size</span><span class="p">:</span>
</span><span id="L-410"><a href="#L-410"><span class="linenos">410</span></a>                            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件已存在，跳过: </span><span class="si">{</span><span class="n">rom_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-411"><a href="#L-411"><span class="linenos">411</span></a>                            <span class="n">uploaded_count</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="L-412"><a href="#L-412"><span class="linenos">412</span></a>                            <span class="k">continue</span>
</span><span id="L-413"><a href="#L-413"><span class="linenos">413</span></a>                    <span class="k">except</span> <span class="ne">FileNotFoundError</span><span class="p">:</span>
</span><span id="L-414"><a href="#L-414"><span class="linenos">414</span></a>                        <span class="k">pass</span>
</span><span id="L-415"><a href="#L-415"><span class="linenos">415</span></a>                    
</span><span id="L-416"><a href="#L-416"><span class="linenos">416</span></a>                    <span class="c1"># 上传文件</span>
</span><span id="L-417"><a href="#L-417"><span class="linenos">417</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;上传: </span><span class="si">{</span><span class="n">rom_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-418"><a href="#L-418"><span class="linenos">418</span></a>                    <span class="n">sftp</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">rom_file</span><span class="p">),</span> <span class="n">remote_file</span><span class="p">)</span>
</span><span id="L-419"><a href="#L-419"><span class="linenos">419</span></a>                    <span class="n">uploaded_count</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="L-420"><a href="#L-420"><span class="linenos">420</span></a>                    
</span><span id="L-421"><a href="#L-421"><span class="linenos">421</span></a>                <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-422"><a href="#L-422"><span class="linenos">422</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;上传文件失败 </span><span class="si">{</span><span class="n">rom_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-423"><a href="#L-423"><span class="linenos">423</span></a>            
</span><span id="L-424"><a href="#L-424"><span class="linenos">424</span></a>            <span class="n">sftp</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</span><span id="L-425"><a href="#L-425"><span class="linenos">425</span></a>            <span class="n">ssh</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</span><span id="L-426"><a href="#L-426"><span class="linenos">426</span></a>            
</span><span id="L-427"><a href="#L-427"><span class="linenos">427</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;上传完成: </span><span class="si">{</span><span class="n">uploaded_count</span><span class="si">}</span><span class="s2">/</span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">rom_files</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个文件&quot;</span><span class="p">)</span>
</span><span id="L-428"><a href="#L-428"><span class="linenos">428</span></a>            <span class="k">return</span> <span class="n">uploaded_count</span> <span class="o">&gt;</span> <span class="mi">0</span>
</span><span id="L-429"><a href="#L-429"><span class="linenos">429</span></a>            
</span><span id="L-430"><a href="#L-430"><span class="linenos">430</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-431"><a href="#L-431"><span class="linenos">431</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;上传过程出错: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-432"><a href="#L-432"><span class="linenos">432</span></a>            <span class="n">ssh</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</span><span id="L-433"><a href="#L-433"><span class="linenos">433</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-434"><a href="#L-434"><span class="linenos">434</span></a>    
</span><span id="L-435"><a href="#L-435"><span class="linenos">435</span></a>    <span class="k">def</span> <span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">search_query</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;nes 100 in 1&quot;</span><span class="p">):</span>
</span><span id="L-436"><a href="#L-436"><span class="linenos">436</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;运行完整的下载和传输流程&quot;&quot;&quot;</span>
</span><span id="L-437"><a href="#L-437"><span class="linenos">437</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;=== NES ROM 下载和传输工具 ===&quot;</span><span class="p">)</span>
</span><span id="L-438"><a href="#L-438"><span class="linenos">438</span></a>
</span><span id="L-439"><a href="#L-439"><span class="linenos">439</span></a>        <span class="c1"># 1. 优先尝试配置文件中的下载地址</span>
</span><span id="L-440"><a href="#L-440"><span class="linenos">440</span></a>        <span class="n">nes_zip_urls</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;nes_zip_urls&quot;</span><span class="p">,</span> <span class="p">[])</span>
</span><span id="L-441"><a href="#L-441"><span class="linenos">441</span></a>        <span class="n">zip_path</span> <span class="o">=</span> <span class="kc">None</span>
</span><span id="L-442"><a href="#L-442"><span class="linenos">442</span></a>        <span class="n">download_url</span> <span class="o">=</span> <span class="kc">None</span>
</span><span id="L-443"><a href="#L-443"><span class="linenos">443</span></a>        <span class="k">if</span> <span class="n">nes_zip_urls</span><span class="p">:</span>
</span><span id="L-444"><a href="#L-444"><span class="linenos">444</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置文件中找到 </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">nes_zip_urls</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个NES ZIP下载地址，优先尝试...&quot;</span><span class="p">)</span>
</span><span id="L-445"><a href="#L-445"><span class="linenos">445</span></a>            <span class="k">for</span> <span class="n">url</span> <span class="ow">in</span> <span class="n">nes_zip_urls</span><span class="p">:</span>
</span><span id="L-446"><a href="#L-446"><span class="linenos">446</span></a>                <span class="n">filename</span> <span class="o">=</span> <span class="n">url</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;/&#39;</span><span class="p">)[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
</span><span id="L-447"><a href="#L-447"><span class="linenos">447</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;尝试下载: </span><span class="si">{</span><span class="n">url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-448"><a href="#L-448"><span class="linenos">448</span></a>                <span class="n">zip_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_file</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">filename</span><span class="p">)</span>
</span><span id="L-449"><a href="#L-449"><span class="linenos">449</span></a>                <span class="k">if</span> <span class="n">zip_path</span><span class="p">:</span>
</span><span id="L-450"><a href="#L-450"><span class="linenos">450</span></a>                    <span class="n">download_url</span> <span class="o">=</span> <span class="n">url</span>
</span><span id="L-451"><a href="#L-451"><span class="linenos">451</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;成功下载: </span><span class="si">{</span><span class="n">url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-452"><a href="#L-452"><span class="linenos">452</span></a>                    <span class="k">break</span>
</span><span id="L-453"><a href="#L-453"><span class="linenos">453</span></a>                <span class="k">else</span><span class="p">:</span>
</span><span id="L-454"><a href="#L-454"><span class="linenos">454</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载失败: </span><span class="si">{</span><span class="n">url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-455"><a href="#L-455"><span class="linenos">455</span></a>        
</span><span id="L-456"><a href="#L-456"><span class="linenos">456</span></a>        <span class="c1"># 2. 如果配置地址全部失败，则自动搜索</span>
</span><span id="L-457"><a href="#L-457"><span class="linenos">457</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">zip_path</span><span class="p">:</span>
</span><span id="L-458"><a href="#L-458"><span class="linenos">458</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;配置地址全部失败，自动搜索可用ROM...&quot;</span><span class="p">)</span>
</span><span id="L-459"><a href="#L-459"><span class="linenos">459</span></a>            <span class="n">results</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">search_roms</span><span class="p">(</span><span class="n">search_query</span><span class="p">)</span>
</span><span id="L-460"><a href="#L-460"><span class="linenos">460</span></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">results</span><span class="p">:</span>
</span><span id="L-461"><a href="#L-461"><span class="linenos">461</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;未找到ROM文件&quot;</span><span class="p">)</span>
</span><span id="L-462"><a href="#L-462"><span class="linenos">462</span></a>                <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-463"><a href="#L-463"><span class="linenos">463</span></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">搜索结果:&quot;</span><span class="p">)</span>
</span><span id="L-464"><a href="#L-464"><span class="linenos">464</span></a>            <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">result</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">results</span><span class="p">[:</span><span class="mi">5</span><span class="p">]):</span>
</span><span id="L-465"><a href="#L-465"><span class="linenos">465</span></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">. </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;title&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2"> (下载次数: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;downloads&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">)&quot;</span><span class="p">)</span>
</span><span id="L-466"><a href="#L-466"><span class="linenos">466</span></a>            <span class="n">selected_result</span> <span class="o">=</span> <span class="n">results</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
</span><span id="L-467"><a href="#L-467"><span class="linenos">467</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;选择: </span><span class="si">{</span><span class="n">selected_result</span><span class="p">[</span><span class="s1">&#39;title&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-468"><a href="#L-468"><span class="linenos">468</span></a>            <span class="n">download_url</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_download_url</span><span class="p">(</span><span class="n">selected_result</span><span class="p">[</span><span class="s1">&#39;identifier&#39;</span><span class="p">])</span>
</span><span id="L-469"><a href="#L-469"><span class="linenos">469</span></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">download_url</span><span class="p">:</span>
</span><span id="L-470"><a href="#L-470"><span class="linenos">470</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;无法获取下载链接&quot;</span><span class="p">)</span>
</span><span id="L-471"><a href="#L-471"><span class="linenos">471</span></a>                <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-472"><a href="#L-472"><span class="linenos">472</span></a>            <span class="n">filename</span> <span class="o">=</span> <span class="n">download_url</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;/&#39;</span><span class="p">)[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
</span><span id="L-473"><a href="#L-473"><span class="linenos">473</span></a>            <span class="n">zip_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_file</span><span class="p">(</span><span class="n">download_url</span><span class="p">,</span> <span class="n">filename</span><span class="p">)</span>
</span><span id="L-474"><a href="#L-474"><span class="linenos">474</span></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">zip_path</span><span class="p">:</span>
</span><span id="L-475"><a href="#L-475"><span class="linenos">475</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;下载失败&quot;</span><span class="p">)</span>
</span><span id="L-476"><a href="#L-476"><span class="linenos">476</span></a>                <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-477"><a href="#L-477"><span class="linenos">477</span></a>
</span><span id="L-478"><a href="#L-478"><span class="linenos">478</span></a>        <span class="c1"># 3. 验证文件</span>
</span><span id="L-479"><a href="#L-479"><span class="linenos">479</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">verify_file</span><span class="p">(</span><span class="n">zip_path</span><span class="p">):</span>
</span><span id="L-480"><a href="#L-480"><span class="linenos">480</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;文件验证失败&quot;</span><span class="p">)</span>
</span><span id="L-481"><a href="#L-481"><span class="linenos">481</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-482"><a href="#L-482"><span class="linenos">482</span></a>
</span><span id="L-483"><a href="#L-483"><span class="linenos">483</span></a>        <span class="c1"># 4. 解压ROM文件</span>
</span><span id="L-484"><a href="#L-484"><span class="linenos">484</span></a>        <span class="n">rom_files</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">extract_roms</span><span class="p">(</span><span class="n">zip_path</span><span class="p">)</span>
</span><span id="L-485"><a href="#L-485"><span class="linenos">485</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">rom_files</span><span class="p">:</span>
</span><span id="L-486"><a href="#L-486"><span class="linenos">486</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;解压失败或未找到ROM文件&quot;</span><span class="p">)</span>
</span><span id="L-487"><a href="#L-487"><span class="linenos">487</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-488"><a href="#L-488"><span class="linenos">488</span></a>
</span><span id="L-489"><a href="#L-489"><span class="linenos">489</span></a>        <span class="c1"># 5. 上传到树莓派</span>
</span><span id="L-490"><a href="#L-490"><span class="linenos">490</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">upload_roms</span><span class="p">(</span><span class="n">rom_files</span><span class="p">):</span>
</span><span id="L-491"><a href="#L-491"><span class="linenos">491</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;ROM传输完成！&quot;</span><span class="p">)</span>
</span><span id="L-492"><a href="#L-492"><span class="linenos">492</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-493"><a href="#L-493"><span class="linenos">493</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="L-494"><a href="#L-494"><span class="linenos">494</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;ROM传输失败&quot;</span><span class="p">)</span>
</span><span id="L-495"><a href="#L-495"><span class="linenos">495</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-496"><a href="#L-496"><span class="linenos">496</span></a>
</span><span id="L-497"><a href="#L-497"><span class="linenos">497</span></a><span class="k">def</span> <span class="nf">main</span><span class="p">():</span>
</span><span id="L-498"><a href="#L-498"><span class="linenos">498</span></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;主函数&quot;&quot;&quot;</span>
</span><span id="L-499"><a href="#L-499"><span class="linenos">499</span></a>    <span class="n">parser</span> <span class="o">=</span> <span class="n">argparse</span><span class="o">.</span><span class="n">ArgumentParser</span><span class="p">(</span><span class="n">description</span><span class="o">=</span><span class="s2">&quot;NES ROM 下载和传输工具&quot;</span><span class="p">)</span>
</span><span id="L-500"><a href="#L-500"><span class="linenos">500</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--config&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="s2">&quot;rom_config.json&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;配置文件路径&quot;</span><span class="p">)</span>
</span><span id="L-501"><a href="#L-501"><span class="linenos">501</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--search&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="s2">&quot;nes 100 in 1&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;搜索关键词&quot;</span><span class="p">)</span>
</span><span id="L-502"><a href="#L-502"><span class="linenos">502</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--download-only&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;仅下载，不上传&quot;</span><span class="p">)</span>
</span><span id="L-503"><a href="#L-503"><span class="linenos">503</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--upload-only&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;仅上传已下载的文件&quot;</span><span class="p">)</span>
</span><span id="L-504"><a href="#L-504"><span class="linenos">504</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--setup-config&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;设置配置文件&quot;</span><span class="p">)</span>
</span><span id="L-505"><a href="#L-505"><span class="linenos">505</span></a>    
</span><span id="L-506"><a href="#L-506"><span class="linenos">506</span></a>    <span class="n">args</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>
</span><span id="L-507"><a href="#L-507"><span class="linenos">507</span></a>    
</span><span id="L-508"><a href="#L-508"><span class="linenos">508</span></a>    <span class="n">downloader</span> <span class="o">=</span> <span class="n">ROMDownloader</span><span class="p">(</span><span class="n">args</span><span class="o">.</span><span class="n">config</span><span class="p">)</span>
</span><span id="L-509"><a href="#L-509"><span class="linenos">509</span></a>    
</span><span id="L-510"><a href="#L-510"><span class="linenos">510</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">setup_config</span><span class="p">:</span>
</span><span id="L-511"><a href="#L-511"><span class="linenos">511</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;请编辑配置文件:&quot;</span><span class="p">,</span> <span class="n">args</span><span class="o">.</span><span class="n">config</span><span class="p">)</span>
</span><span id="L-512"><a href="#L-512"><span class="linenos">512</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;配置项包括:&quot;</span><span class="p">)</span>
</span><span id="L-513"><a href="#L-513"><span class="linenos">513</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;- 树莓派连接信息&quot;</span><span class="p">)</span>
</span><span id="L-514"><a href="#L-514"><span class="linenos">514</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;- 下载参数&quot;</span><span class="p">)</span>
</span><span id="L-515"><a href="#L-515"><span class="linenos">515</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;- 验证设置&quot;</span><span class="p">)</span>
</span><span id="L-516"><a href="#L-516"><span class="linenos">516</span></a>        <span class="k">return</span>
</span><span id="L-517"><a href="#L-517"><span class="linenos">517</span></a>    
</span><span id="L-518"><a href="#L-518"><span class="linenos">518</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">upload_only</span><span class="p">:</span>
</span><span id="L-519"><a href="#L-519"><span class="linenos">519</span></a>        <span class="c1"># 查找已下载的ROM文件</span>
</span><span id="L-520"><a href="#L-520"><span class="linenos">520</span></a>        <span class="n">extract_dir</span> <span class="o">=</span> <span class="n">downloader</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="s2">&quot;extracted&quot;</span>
</span><span id="L-521"><a href="#L-521"><span class="linenos">521</span></a>        <span class="k">if</span> <span class="n">extract_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-522"><a href="#L-522"><span class="linenos">522</span></a>            <span class="n">rom_files</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">extract_dir</span><span class="o">.</span><span class="n">glob</span><span class="p">(</span><span class="s2">&quot;*.nes&quot;</span><span class="p">))</span> <span class="o">+</span> <span class="nb">list</span><span class="p">(</span><span class="n">extract_dir</span><span class="o">.</span><span class="n">glob</span><span class="p">(</span><span class="s2">&quot;*.NES&quot;</span><span class="p">))</span>
</span><span id="L-523"><a href="#L-523"><span class="linenos">523</span></a>            <span class="k">if</span> <span class="n">rom_files</span><span class="p">:</span>
</span><span id="L-524"><a href="#L-524"><span class="linenos">524</span></a>                <span class="n">downloader</span><span class="o">.</span><span class="n">upload_roms</span><span class="p">(</span><span class="n">rom_files</span><span class="p">)</span>
</span><span id="L-525"><a href="#L-525"><span class="linenos">525</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="L-526"><a href="#L-526"><span class="linenos">526</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;未找到ROM文件&quot;</span><span class="p">)</span>
</span><span id="L-527"><a href="#L-527"><span class="linenos">527</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="L-528"><a href="#L-528"><span class="linenos">528</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;未找到解压目录&quot;</span><span class="p">)</span>
</span><span id="L-529"><a href="#L-529"><span class="linenos">529</span></a>        <span class="k">return</span>
</span><span id="L-530"><a href="#L-530"><span class="linenos">530</span></a>    
</span><span id="L-531"><a href="#L-531"><span class="linenos">531</span></a>    <span class="c1"># 运行完整流程</span>
</span><span id="L-532"><a href="#L-532"><span class="linenos">532</span></a>    <span class="n">success</span> <span class="o">=</span> <span class="n">downloader</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">args</span><span class="o">.</span><span class="n">search</span><span class="p">)</span>
</span><span id="L-533"><a href="#L-533"><span class="linenos">533</span></a>    
</span><span id="L-534"><a href="#L-534"><span class="linenos">534</span></a>    <span class="k">if</span> <span class="n">success</span><span class="p">:</span>
</span><span id="L-535"><a href="#L-535"><span class="linenos">535</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">🎉 ROM下载和传输完成！&quot;</span><span class="p">)</span>
</span><span id="L-536"><a href="#L-536"><span class="linenos">536</span></a>    <span class="k">else</span><span class="p">:</span>
</span><span id="L-537"><a href="#L-537"><span class="linenos">537</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">❌ 操作失败，请查看日志文件&quot;</span><span class="p">)</span>
</span><span id="L-538"><a href="#L-538"><span class="linenos">538</span></a>
</span><span id="L-539"><a href="#L-539"><span class="linenos">539</span></a><span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
</span><span id="L-540"><a href="#L-540"><span class="linenos">540</span></a>    <span class="n">main</span><span class="p">()</span> 
</span></pre></div>


            </section>
                <section id="logger">
                    <div class="attr variable">
            <span class="name">logger</span>        =
<span class="default_value">&lt;Logger rom_downloader (INFO)&gt;</span>

        
    </div>
    <a class="headerlink" href="#logger"></a>
    
    

                </section>
                <section id="ROMDownloader">
                            <input id="ROMDownloader-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr class">
            
    <span class="def">class</span>
    <span class="name">ROMDownloader</span>:

                <label class="view-source-button" for="ROMDownloader-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#ROMDownloader"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="ROMDownloader-47"><a href="#ROMDownloader-47"><span class="linenos"> 47</span></a><span class="k">class</span> <span class="nc">ROMDownloader</span><span class="p">:</span>
</span><span id="ROMDownloader-48"><a href="#ROMDownloader-48"><span class="linenos"> 48</span></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-49"><a href="#ROMDownloader-49"><span class="linenos"> 49</span></a><span class="sd">    ROM下载和传输工具类</span>
</span><span id="ROMDownloader-50"><a href="#ROMDownloader-50"><span class="linenos"> 50</span></a><span class="sd">    </span>
</span><span id="ROMDownloader-51"><a href="#ROMDownloader-51"><span class="linenos"> 51</span></a><span class="sd">    提供完整的ROM自动化下载和传输流程，包括：</span>
</span><span id="ROMDownloader-52"><a href="#ROMDownloader-52"><span class="linenos"> 52</span></a><span class="sd">    - ROM搜索和下载链接获取</span>
</span><span id="ROMDownloader-53"><a href="#ROMDownloader-53"><span class="linenos"> 53</span></a><span class="sd">    - 断点续传的文件下载</span>
</span><span id="ROMDownloader-54"><a href="#ROMDownloader-54"><span class="linenos"> 54</span></a><span class="sd">    - 文件完整性验证</span>
</span><span id="ROMDownloader-55"><a href="#ROMDownloader-55"><span class="linenos"> 55</span></a><span class="sd">    - 自动解压和文件处理</span>
</span><span id="ROMDownloader-56"><a href="#ROMDownloader-56"><span class="linenos"> 56</span></a><span class="sd">    - SFTP传输到树莓派</span>
</span><span id="ROMDownloader-57"><a href="#ROMDownloader-57"><span class="linenos"> 57</span></a><span class="sd">    </span>
</span><span id="ROMDownloader-58"><a href="#ROMDownloader-58"><span class="linenos"> 58</span></a><span class="sd">    属性:</span>
</span><span id="ROMDownloader-59"><a href="#ROMDownloader-59"><span class="linenos"> 59</span></a><span class="sd">        config_file (Path): 配置文件路径</span>
</span><span id="ROMDownloader-60"><a href="#ROMDownloader-60"><span class="linenos"> 60</span></a><span class="sd">        download_dir (Path): 下载目录路径</span>
</span><span id="ROMDownloader-61"><a href="#ROMDownloader-61"><span class="linenos"> 61</span></a><span class="sd">        config (Dict): 配置字典</span>
</span><span id="ROMDownloader-62"><a href="#ROMDownloader-62"><span class="linenos"> 62</span></a><span class="sd">        session (requests.Session): HTTP会话对象</span>
</span><span id="ROMDownloader-63"><a href="#ROMDownloader-63"><span class="linenos"> 63</span></a><span class="sd">    &quot;&quot;&quot;</span>
</span><span id="ROMDownloader-64"><a href="#ROMDownloader-64"><span class="linenos"> 64</span></a>    
</span><span id="ROMDownloader-65"><a href="#ROMDownloader-65"><span class="linenos"> 65</span></a>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">config_file</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;rom_config.json&quot;</span><span class="p">):</span>
</span><span id="ROMDownloader-66"><a href="#ROMDownloader-66"><span class="linenos"> 66</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-67"><a href="#ROMDownloader-67"><span class="linenos"> 67</span></a><span class="sd">        初始化ROM下载器</span>
</span><span id="ROMDownloader-68"><a href="#ROMDownloader-68"><span class="linenos"> 68</span></a><span class="sd">        </span>
</span><span id="ROMDownloader-69"><a href="#ROMDownloader-69"><span class="linenos"> 69</span></a><span class="sd">        Args:</span>
</span><span id="ROMDownloader-70"><a href="#ROMDownloader-70"><span class="linenos"> 70</span></a><span class="sd">            config_file (str): 配置文件路径</span>
</span><span id="ROMDownloader-71"><a href="#ROMDownloader-71"><span class="linenos"> 71</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="ROMDownloader-72"><a href="#ROMDownloader-72"><span class="linenos"> 72</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">config_file</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">config_file</span><span class="p">)</span>
</span><span id="ROMDownloader-73"><a href="#ROMDownloader-73"><span class="linenos"> 73</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s2">&quot;downloads/roms&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-74"><a href="#ROMDownloader-74"><span class="linenos"> 74</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">parents</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="ROMDownloader-75"><a href="#ROMDownloader-75"><span class="linenos"> 75</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">config</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_load_config</span><span class="p">()</span>
</span><span id="ROMDownloader-76"><a href="#ROMDownloader-76"><span class="linenos"> 76</span></a>        
</span><span id="ROMDownloader-77"><a href="#ROMDownloader-77"><span class="linenos"> 77</span></a>        <span class="c1"># 设置重试策略</span>
</span><span id="ROMDownloader-78"><a href="#ROMDownloader-78"><span class="linenos"> 78</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">session</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_setup_session</span><span class="p">()</span>
</span><span id="ROMDownloader-79"><a href="#ROMDownloader-79"><span class="linenos"> 79</span></a>        
</span><span id="ROMDownloader-80"><a href="#ROMDownloader-80"><span class="linenos"> 80</span></a>    <span class="k">def</span> <span class="nf">_load_config</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">:</span>
</span><span id="ROMDownloader-81"><a href="#ROMDownloader-81"><span class="linenos"> 81</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;加载配置文件&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-82"><a href="#ROMDownloader-82"><span class="linenos"> 82</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">config_file</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="ROMDownloader-83"><a href="#ROMDownloader-83"><span class="linenos"> 83</span></a>            <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-84"><a href="#ROMDownloader-84"><span class="linenos"> 84</span></a>                <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_file</span><span class="p">,</span> <span class="s1">&#39;r&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="ROMDownloader-85"><a href="#ROMDownloader-85"><span class="linenos"> 85</span></a>                    <span class="k">return</span> <span class="n">json</span><span class="o">.</span><span class="n">load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span>
</span><span id="ROMDownloader-86"><a href="#ROMDownloader-86"><span class="linenos"> 86</span></a>            <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader-87"><a href="#ROMDownloader-87"><span class="linenos"> 87</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;加载配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-88"><a href="#ROMDownloader-88"><span class="linenos"> 88</span></a>        
</span><span id="ROMDownloader-89"><a href="#ROMDownloader-89"><span class="linenos"> 89</span></a>        <span class="c1"># 默认配置</span>
</span><span id="ROMDownloader-90"><a href="#ROMDownloader-90"><span class="linenos"> 90</span></a>        <span class="n">default_config</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="ROMDownloader-91"><a href="#ROMDownloader-91"><span class="linenos"> 91</span></a>            <span class="s2">&quot;rom_sources&quot;</span><span class="p">:</span> <span class="p">{</span>
</span><span id="ROMDownloader-92"><a href="#ROMDownloader-92"><span class="linenos"> 92</span></a>                <span class="s2">&quot;archive_org&quot;</span><span class="p">:</span> <span class="p">{</span>
</span><span id="ROMDownloader-93"><a href="#ROMDownloader-93"><span class="linenos"> 93</span></a>                    <span class="s2">&quot;base_url&quot;</span><span class="p">:</span> <span class="s2">&quot;https://archive.org&quot;</span><span class="p">,</span>
</span><span id="ROMDownloader-94"><a href="#ROMDownloader-94"><span class="linenos"> 94</span></a>                    <span class="s2">&quot;search_url&quot;</span><span class="p">:</span> <span class="s2">&quot;https://archive.org/advancedsearch.php&quot;</span><span class="p">,</span>
</span><span id="ROMDownloader-95"><a href="#ROMDownloader-95"><span class="linenos"> 95</span></a>                    <span class="s2">&quot;download_patterns&quot;</span><span class="p">:</span> <span class="p">[</span>
</span><span id="ROMDownloader-96"><a href="#ROMDownloader-96"><span class="linenos"> 96</span></a>                        <span class="s2">&quot;nes-100-in-1&quot;</span><span class="p">,</span>
</span><span id="ROMDownloader-97"><a href="#ROMDownloader-97"><span class="linenos"> 97</span></a>                        <span class="s2">&quot;nes-games-collection&quot;</span><span class="p">,</span>
</span><span id="ROMDownloader-98"><a href="#ROMDownloader-98"><span class="linenos"> 98</span></a>                        <span class="s2">&quot;nintendo-entertainment-system-roms&quot;</span>
</span><span id="ROMDownloader-99"><a href="#ROMDownloader-99"><span class="linenos"> 99</span></a>                    <span class="p">]</span>
</span><span id="ROMDownloader-100"><a href="#ROMDownloader-100"><span class="linenos">100</span></a>                <span class="p">}</span>
</span><span id="ROMDownloader-101"><a href="#ROMDownloader-101"><span class="linenos">101</span></a>            <span class="p">},</span>
</span><span id="ROMDownloader-102"><a href="#ROMDownloader-102"><span class="linenos">102</span></a>            <span class="s2">&quot;raspberry_pi&quot;</span><span class="p">:</span> <span class="p">{</span>
</span><span id="ROMDownloader-103"><a href="#ROMDownloader-103"><span class="linenos">103</span></a>                <span class="s2">&quot;host&quot;</span><span class="p">:</span> <span class="s2">&quot;*************&quot;</span><span class="p">,</span>
</span><span id="ROMDownloader-104"><a href="#ROMDownloader-104"><span class="linenos">104</span></a>                <span class="s2">&quot;port&quot;</span><span class="p">:</span> <span class="mi">22</span><span class="p">,</span>
</span><span id="ROMDownloader-105"><a href="#ROMDownloader-105"><span class="linenos">105</span></a>                <span class="s2">&quot;username&quot;</span><span class="p">:</span> <span class="s2">&quot;pi&quot;</span><span class="p">,</span>
</span><span id="ROMDownloader-106"><a href="#ROMDownloader-106"><span class="linenos">106</span></a>                <span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
</span><span id="ROMDownloader-107"><a href="#ROMDownloader-107"><span class="linenos">107</span></a>                <span class="s2">&quot;key_file&quot;</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
</span><span id="ROMDownloader-108"><a href="#ROMDownloader-108"><span class="linenos">108</span></a>                <span class="s2">&quot;roms_path&quot;</span><span class="p">:</span> <span class="s2">&quot;/home/<USER>/RetroPie/roms/nes/&quot;</span>
</span><span id="ROMDownloader-109"><a href="#ROMDownloader-109"><span class="linenos">109</span></a>            <span class="p">},</span>
</span><span id="ROMDownloader-110"><a href="#ROMDownloader-110"><span class="linenos">110</span></a>            <span class="s2">&quot;download&quot;</span><span class="p">:</span> <span class="p">{</span>
</span><span id="ROMDownloader-111"><a href="#ROMDownloader-111"><span class="linenos">111</span></a>                <span class="s2">&quot;timeout&quot;</span><span class="p">:</span> <span class="mi">30</span><span class="p">,</span>
</span><span id="ROMDownloader-112"><a href="#ROMDownloader-112"><span class="linenos">112</span></a>                <span class="s2">&quot;chunk_size&quot;</span><span class="p">:</span> <span class="mi">8192</span><span class="p">,</span>
</span><span id="ROMDownloader-113"><a href="#ROMDownloader-113"><span class="linenos">113</span></a>                <span class="s2">&quot;max_retries&quot;</span><span class="p">:</span> <span class="mi">3</span><span class="p">,</span>
</span><span id="ROMDownloader-114"><a href="#ROMDownloader-114"><span class="linenos">114</span></a>                <span class="s2">&quot;retry_delay&quot;</span><span class="p">:</span> <span class="mi">5</span>
</span><span id="ROMDownloader-115"><a href="#ROMDownloader-115"><span class="linenos">115</span></a>            <span class="p">},</span>
</span><span id="ROMDownloader-116"><a href="#ROMDownloader-116"><span class="linenos">116</span></a>            <span class="s2">&quot;verification&quot;</span><span class="p">:</span> <span class="p">{</span>
</span><span id="ROMDownloader-117"><a href="#ROMDownloader-117"><span class="linenos">117</span></a>                <span class="s2">&quot;verify_checksum&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
</span><span id="ROMDownloader-118"><a href="#ROMDownloader-118"><span class="linenos">118</span></a>                <span class="s2">&quot;checksum_algorithm&quot;</span><span class="p">:</span> <span class="s2">&quot;sha256&quot;</span>
</span><span id="ROMDownloader-119"><a href="#ROMDownloader-119"><span class="linenos">119</span></a>            <span class="p">}</span>
</span><span id="ROMDownloader-120"><a href="#ROMDownloader-120"><span class="linenos">120</span></a>        <span class="p">}</span>
</span><span id="ROMDownloader-121"><a href="#ROMDownloader-121"><span class="linenos">121</span></a>        
</span><span id="ROMDownloader-122"><a href="#ROMDownloader-122"><span class="linenos">122</span></a>        <span class="c1"># 保存默认配置</span>
</span><span id="ROMDownloader-123"><a href="#ROMDownloader-123"><span class="linenos">123</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">_save_config</span><span class="p">(</span><span class="n">default_config</span><span class="p">)</span>
</span><span id="ROMDownloader-124"><a href="#ROMDownloader-124"><span class="linenos">124</span></a>        <span class="k">return</span> <span class="n">default_config</span>
</span><span id="ROMDownloader-125"><a href="#ROMDownloader-125"><span class="linenos">125</span></a>    
</span><span id="ROMDownloader-126"><a href="#ROMDownloader-126"><span class="linenos">126</span></a>    <span class="k">def</span> <span class="nf">_save_config</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">config</span><span class="p">:</span> <span class="n">Dict</span><span class="p">):</span>
</span><span id="ROMDownloader-127"><a href="#ROMDownloader-127"><span class="linenos">127</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;保存配置文件&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-128"><a href="#ROMDownloader-128"><span class="linenos">128</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-129"><a href="#ROMDownloader-129"><span class="linenos">129</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">config_file</span><span class="p">,</span> <span class="s1">&#39;w&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="ROMDownloader-130"><a href="#ROMDownloader-130"><span class="linenos">130</span></a>                <span class="n">json</span><span class="o">.</span><span class="n">dump</span><span class="p">(</span><span class="n">config</span><span class="p">,</span> <span class="n">f</span><span class="p">,</span> <span class="n">indent</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">ensure_ascii</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</span><span id="ROMDownloader-131"><a href="#ROMDownloader-131"><span class="linenos">131</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置文件已保存: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config_file</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-132"><a href="#ROMDownloader-132"><span class="linenos">132</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader-133"><a href="#ROMDownloader-133"><span class="linenos">133</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;保存配置文件失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-134"><a href="#ROMDownloader-134"><span class="linenos">134</span></a>    
</span><span id="ROMDownloader-135"><a href="#ROMDownloader-135"><span class="linenos">135</span></a>    <span class="k">def</span> <span class="nf">_setup_session</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">requests</span><span class="o">.</span><span class="n">Session</span><span class="p">:</span>
</span><span id="ROMDownloader-136"><a href="#ROMDownloader-136"><span class="linenos">136</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;设置HTTP会话，包含重试策略&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-137"><a href="#ROMDownloader-137"><span class="linenos">137</span></a>        <span class="n">session</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">Session</span><span class="p">()</span>
</span><span id="ROMDownloader-138"><a href="#ROMDownloader-138"><span class="linenos">138</span></a>        
</span><span id="ROMDownloader-139"><a href="#ROMDownloader-139"><span class="linenos">139</span></a>        <span class="n">retry_strategy</span> <span class="o">=</span> <span class="n">Retry</span><span class="p">(</span>
</span><span id="ROMDownloader-140"><a href="#ROMDownloader-140"><span class="linenos">140</span></a>            <span class="n">total</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;download&quot;</span><span class="p">][</span><span class="s2">&quot;max_retries&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader-141"><a href="#ROMDownloader-141"><span class="linenos">141</span></a>            <span class="n">status_forcelist</span><span class="o">=</span><span class="p">[</span><span class="mi">429</span><span class="p">,</span> <span class="mi">500</span><span class="p">,</span> <span class="mi">502</span><span class="p">,</span> <span class="mi">503</span><span class="p">,</span> <span class="mi">504</span><span class="p">],</span>
</span><span id="ROMDownloader-142"><a href="#ROMDownloader-142"><span class="linenos">142</span></a>            <span class="n">method_whitelist</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;HEAD&quot;</span><span class="p">,</span> <span class="s2">&quot;GET&quot;</span><span class="p">,</span> <span class="s2">&quot;OPTIONS&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader-143"><a href="#ROMDownloader-143"><span class="linenos">143</span></a>            <span class="n">backoff_factor</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;download&quot;</span><span class="p">][</span><span class="s2">&quot;retry_delay&quot;</span><span class="p">]</span>
</span><span id="ROMDownloader-144"><a href="#ROMDownloader-144"><span class="linenos">144</span></a>        <span class="p">)</span>
</span><span id="ROMDownloader-145"><a href="#ROMDownloader-145"><span class="linenos">145</span></a>        
</span><span id="ROMDownloader-146"><a href="#ROMDownloader-146"><span class="linenos">146</span></a>        <span class="n">adapter</span> <span class="o">=</span> <span class="n">HTTPAdapter</span><span class="p">(</span><span class="n">max_retries</span><span class="o">=</span><span class="n">retry_strategy</span><span class="p">)</span>
</span><span id="ROMDownloader-147"><a href="#ROMDownloader-147"><span class="linenos">147</span></a>        <span class="n">session</span><span class="o">.</span><span class="n">mount</span><span class="p">(</span><span class="s2">&quot;http://&quot;</span><span class="p">,</span> <span class="n">adapter</span><span class="p">)</span>
</span><span id="ROMDownloader-148"><a href="#ROMDownloader-148"><span class="linenos">148</span></a>        <span class="n">session</span><span class="o">.</span><span class="n">mount</span><span class="p">(</span><span class="s2">&quot;https://&quot;</span><span class="p">,</span> <span class="n">adapter</span><span class="p">)</span>
</span><span id="ROMDownloader-149"><a href="#ROMDownloader-149"><span class="linenos">149</span></a>        
</span><span id="ROMDownloader-150"><a href="#ROMDownloader-150"><span class="linenos">150</span></a>        <span class="k">return</span> <span class="n">session</span>
</span><span id="ROMDownloader-151"><a href="#ROMDownloader-151"><span class="linenos">151</span></a>    
</span><span id="ROMDownloader-152"><a href="#ROMDownloader-152"><span class="linenos">152</span></a>    <span class="k">def</span> <span class="nf">search_roms</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">query</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;nes 100 in 1&quot;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Dict</span><span class="p">]:</span>
</span><span id="ROMDownloader-153"><a href="#ROMDownloader-153"><span class="linenos">153</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;搜索ROM文件&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-154"><a href="#ROMDownloader-154"><span class="linenos">154</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;搜索ROM: </span><span class="si">{</span><span class="n">query</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-155"><a href="#ROMDownloader-155"><span class="linenos">155</span></a>        
</span><span id="ROMDownloader-156"><a href="#ROMDownloader-156"><span class="linenos">156</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-157"><a href="#ROMDownloader-157"><span class="linenos">157</span></a>            <span class="n">search_url</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;rom_sources&quot;</span><span class="p">][</span><span class="s2">&quot;archive_org&quot;</span><span class="p">][</span><span class="s2">&quot;search_url&quot;</span><span class="p">]</span>
</span><span id="ROMDownloader-158"><a href="#ROMDownloader-158"><span class="linenos">158</span></a>            <span class="n">params</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="ROMDownloader-159"><a href="#ROMDownloader-159"><span class="linenos">159</span></a>                <span class="s2">&quot;q&quot;</span><span class="p">:</span> <span class="n">query</span><span class="p">,</span>
</span><span id="ROMDownloader-160"><a href="#ROMDownloader-160"><span class="linenos">160</span></a>                <span class="s2">&quot;output&quot;</span><span class="p">:</span> <span class="s2">&quot;json&quot;</span><span class="p">,</span>
</span><span id="ROMDownloader-161"><a href="#ROMDownloader-161"><span class="linenos">161</span></a>                <span class="s2">&quot;rows&quot;</span><span class="p">:</span> <span class="mi">50</span><span class="p">,</span>
</span><span id="ROMDownloader-162"><a href="#ROMDownloader-162"><span class="linenos">162</span></a>                <span class="s2">&quot;sort&quot;</span><span class="p">:</span> <span class="s2">&quot;downloads desc&quot;</span>
</span><span id="ROMDownloader-163"><a href="#ROMDownloader-163"><span class="linenos">163</span></a>            <span class="p">}</span>
</span><span id="ROMDownloader-164"><a href="#ROMDownloader-164"><span class="linenos">164</span></a>            
</span><span id="ROMDownloader-165"><a href="#ROMDownloader-165"><span class="linenos">165</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">search_url</span><span class="p">,</span> <span class="n">params</span><span class="o">=</span><span class="n">params</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="ROMDownloader-166"><a href="#ROMDownloader-166"><span class="linenos">166</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="ROMDownloader-167"><a href="#ROMDownloader-167"><span class="linenos">167</span></a>            
</span><span id="ROMDownloader-168"><a href="#ROMDownloader-168"><span class="linenos">168</span></a>            <span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
</span><span id="ROMDownloader-169"><a href="#ROMDownloader-169"><span class="linenos">169</span></a>            <span class="n">results</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="ROMDownloader-170"><a href="#ROMDownloader-170"><span class="linenos">170</span></a>            
</span><span id="ROMDownloader-171"><a href="#ROMDownloader-171"><span class="linenos">171</span></a>            <span class="k">for</span> <span class="n">doc</span> <span class="ow">in</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;response&quot;</span><span class="p">,</span> <span class="p">{})</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;docs&quot;</span><span class="p">,</span> <span class="p">[]):</span>
</span><span id="ROMDownloader-172"><a href="#ROMDownloader-172"><span class="linenos">172</span></a>                <span class="k">if</span> <span class="s2">&quot;downloads&quot;</span> <span class="ow">in</span> <span class="n">doc</span> <span class="ow">and</span> <span class="n">doc</span><span class="p">[</span><span class="s2">&quot;downloads&quot;</span><span class="p">]</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="ROMDownloader-173"><a href="#ROMDownloader-173"><span class="linenos">173</span></a>                    <span class="n">result</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="ROMDownloader-174"><a href="#ROMDownloader-174"><span class="linenos">174</span></a>                        <span class="s2">&quot;identifier&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;identifier&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">),</span>
</span><span id="ROMDownloader-175"><a href="#ROMDownloader-175"><span class="linenos">175</span></a>                        <span class="s2">&quot;title&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;title&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">),</span>
</span><span id="ROMDownloader-176"><a href="#ROMDownloader-176"><span class="linenos">176</span></a>                        <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;description&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">),</span>
</span><span id="ROMDownloader-177"><a href="#ROMDownloader-177"><span class="linenos">177</span></a>                        <span class="s2">&quot;downloads&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;downloads&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>
</span><span id="ROMDownloader-178"><a href="#ROMDownloader-178"><span class="linenos">178</span></a>                        <span class="s2">&quot;files&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;files&quot;</span><span class="p">,</span> <span class="p">[])</span>
</span><span id="ROMDownloader-179"><a href="#ROMDownloader-179"><span class="linenos">179</span></a>                    <span class="p">}</span>
</span><span id="ROMDownloader-180"><a href="#ROMDownloader-180"><span class="linenos">180</span></a>                    <span class="n">results</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">result</span><span class="p">)</span>
</span><span id="ROMDownloader-181"><a href="#ROMDownloader-181"><span class="linenos">181</span></a>            
</span><span id="ROMDownloader-182"><a href="#ROMDownloader-182"><span class="linenos">182</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到 </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">results</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个结果&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-183"><a href="#ROMDownloader-183"><span class="linenos">183</span></a>            <span class="k">return</span> <span class="n">results</span>
</span><span id="ROMDownloader-184"><a href="#ROMDownloader-184"><span class="linenos">184</span></a>            
</span><span id="ROMDownloader-185"><a href="#ROMDownloader-185"><span class="linenos">185</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader-186"><a href="#ROMDownloader-186"><span class="linenos">186</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;搜索ROM失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-187"><a href="#ROMDownloader-187"><span class="linenos">187</span></a>            <span class="k">return</span> <span class="p">[]</span>
</span><span id="ROMDownloader-188"><a href="#ROMDownloader-188"><span class="linenos">188</span></a>    
</span><span id="ROMDownloader-189"><a href="#ROMDownloader-189"><span class="linenos">189</span></a>    <span class="k">def</span> <span class="nf">get_download_url</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">identifier</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="ROMDownloader-190"><a href="#ROMDownloader-190"><span class="linenos">190</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;获取下载链接&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-191"><a href="#ROMDownloader-191"><span class="linenos">191</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-192"><a href="#ROMDownloader-192"><span class="linenos">192</span></a>            <span class="n">metadata_url</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s1">&#39;rom_sources&#39;</span><span class="p">][</span><span class="s1">&#39;archive_org&#39;</span><span class="p">][</span><span class="s1">&#39;base_url&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">/metadata/</span><span class="si">{</span><span class="n">identifier</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="ROMDownloader-193"><a href="#ROMDownloader-193"><span class="linenos">193</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">metadata_url</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="ROMDownloader-194"><a href="#ROMDownloader-194"><span class="linenos">194</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="ROMDownloader-195"><a href="#ROMDownloader-195"><span class="linenos">195</span></a>            
</span><span id="ROMDownloader-196"><a href="#ROMDownloader-196"><span class="linenos">196</span></a>            <span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
</span><span id="ROMDownloader-197"><a href="#ROMDownloader-197"><span class="linenos">197</span></a>            <span class="n">files</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;files&quot;</span><span class="p">,</span> <span class="p">{})</span>
</span><span id="ROMDownloader-198"><a href="#ROMDownloader-198"><span class="linenos">198</span></a>            
</span><span id="ROMDownloader-199"><a href="#ROMDownloader-199"><span class="linenos">199</span></a>            <span class="c1"># 查找ZIP文件</span>
</span><span id="ROMDownloader-200"><a href="#ROMDownloader-200"><span class="linenos">200</span></a>            <span class="k">for</span> <span class="n">filename</span><span class="p">,</span> <span class="n">file_info</span> <span class="ow">in</span> <span class="n">files</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><span id="ROMDownloader-201"><a href="#ROMDownloader-201"><span class="linenos">201</span></a>                <span class="k">if</span> <span class="n">filename</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span><span class="o">.</span><span class="n">endswith</span><span class="p">(</span><span class="s1">&#39;.zip&#39;</span><span class="p">):</span>
</span><span id="ROMDownloader-202"><a href="#ROMDownloader-202"><span class="linenos">202</span></a>                    <span class="n">download_url</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s1">&#39;rom_sources&#39;</span><span class="p">][</span><span class="s1">&#39;archive_org&#39;</span><span class="p">][</span><span class="s1">&#39;base_url&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">/download/</span><span class="si">{</span><span class="n">identifier</span><span class="si">}</span><span class="s2">/</span><span class="si">{</span><span class="n">filename</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="ROMDownloader-203"><a href="#ROMDownloader-203"><span class="linenos">203</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到下载链接: </span><span class="si">{</span><span class="n">download_url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-204"><a href="#ROMDownloader-204"><span class="linenos">204</span></a>                    <span class="k">return</span> <span class="n">download_url</span>
</span><span id="ROMDownloader-205"><a href="#ROMDownloader-205"><span class="linenos">205</span></a>            
</span><span id="ROMDownloader-206"><a href="#ROMDownloader-206"><span class="linenos">206</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;未找到ZIP文件: </span><span class="si">{</span><span class="n">identifier</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-207"><a href="#ROMDownloader-207"><span class="linenos">207</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="ROMDownloader-208"><a href="#ROMDownloader-208"><span class="linenos">208</span></a>            
</span><span id="ROMDownloader-209"><a href="#ROMDownloader-209"><span class="linenos">209</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader-210"><a href="#ROMDownloader-210"><span class="linenos">210</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;获取下载链接失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-211"><a href="#ROMDownloader-211"><span class="linenos">211</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="ROMDownloader-212"><a href="#ROMDownloader-212"><span class="linenos">212</span></a>    
</span><span id="ROMDownloader-213"><a href="#ROMDownloader-213"><span class="linenos">213</span></a>    <span class="k">def</span> <span class="nf">download_file</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">url</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">filename</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Path</span><span class="p">]:</span>
</span><span id="ROMDownloader-214"><a href="#ROMDownloader-214"><span class="linenos">214</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;下载文件，支持断点续传&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-215"><a href="#ROMDownloader-215"><span class="linenos">215</span></a>        <span class="n">file_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="n">filename</span>
</span><span id="ROMDownloader-216"><a href="#ROMDownloader-216"><span class="linenos">216</span></a>        
</span><span id="ROMDownloader-217"><a href="#ROMDownloader-217"><span class="linenos">217</span></a>        <span class="c1"># 检查是否已存在</span>
</span><span id="ROMDownloader-218"><a href="#ROMDownloader-218"><span class="linenos">218</span></a>        <span class="k">if</span> <span class="n">file_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="ROMDownloader-219"><a href="#ROMDownloader-219"><span class="linenos">219</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件已存在: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-220"><a href="#ROMDownloader-220"><span class="linenos">220</span></a>            <span class="k">return</span> <span class="n">file_path</span>
</span><span id="ROMDownloader-221"><a href="#ROMDownloader-221"><span class="linenos">221</span></a>        
</span><span id="ROMDownloader-222"><a href="#ROMDownloader-222"><span class="linenos">222</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-223"><a href="#ROMDownloader-223"><span class="linenos">223</span></a>            <span class="c1"># 获取文件大小</span>
</span><span id="ROMDownloader-224"><a href="#ROMDownloader-224"><span class="linenos">224</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">head</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="ROMDownloader-225"><a href="#ROMDownloader-225"><span class="linenos">225</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="ROMDownloader-226"><a href="#ROMDownloader-226"><span class="linenos">226</span></a>            
</span><span id="ROMDownloader-227"><a href="#ROMDownloader-227"><span class="linenos">227</span></a>            <span class="n">total_size</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">headers</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;content-length&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
</span><span id="ROMDownloader-228"><a href="#ROMDownloader-228"><span class="linenos">228</span></a>            
</span><span id="ROMDownloader-229"><a href="#ROMDownloader-229"><span class="linenos">229</span></a>            <span class="c1"># 检查是否有部分下载的文件</span>
</span><span id="ROMDownloader-230"><a href="#ROMDownloader-230"><span class="linenos">230</span></a>            <span class="n">temp_path</span> <span class="o">=</span> <span class="n">file_path</span><span class="o">.</span><span class="n">with_suffix</span><span class="p">(</span><span class="s1">&#39;.tmp&#39;</span><span class="p">)</span>
</span><span id="ROMDownloader-231"><a href="#ROMDownloader-231"><span class="linenos">231</span></a>            <span class="n">resume_pos</span> <span class="o">=</span> <span class="mi">0</span>
</span><span id="ROMDownloader-232"><a href="#ROMDownloader-232"><span class="linenos">232</span></a>            
</span><span id="ROMDownloader-233"><a href="#ROMDownloader-233"><span class="linenos">233</span></a>            <span class="k">if</span> <span class="n">temp_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="ROMDownloader-234"><a href="#ROMDownloader-234"><span class="linenos">234</span></a>                <span class="n">resume_pos</span> <span class="o">=</span> <span class="n">temp_path</span><span class="o">.</span><span class="n">stat</span><span class="p">()</span><span class="o">.</span><span class="n">st_size</span>
</span><span id="ROMDownloader-235"><a href="#ROMDownloader-235"><span class="linenos">235</span></a>                <span class="k">if</span> <span class="n">resume_pos</span> <span class="o">&gt;=</span> <span class="n">total_size</span><span class="p">:</span>
</span><span id="ROMDownloader-236"><a href="#ROMDownloader-236"><span class="linenos">236</span></a>                    <span class="c1"># 文件已完整下载</span>
</span><span id="ROMDownloader-237"><a href="#ROMDownloader-237"><span class="linenos">237</span></a>                    <span class="n">temp_path</span><span class="o">.</span><span class="n">rename</span><span class="p">(</span><span class="n">file_path</span><span class="p">)</span>
</span><span id="ROMDownloader-238"><a href="#ROMDownloader-238"><span class="linenos">238</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件下载完成: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-239"><a href="#ROMDownloader-239"><span class="linenos">239</span></a>                    <span class="k">return</span> <span class="n">file_path</span>
</span><span id="ROMDownloader-240"><a href="#ROMDownloader-240"><span class="linenos">240</span></a>            
</span><span id="ROMDownloader-241"><a href="#ROMDownloader-241"><span class="linenos">241</span></a>            <span class="c1"># 设置断点续传头</span>
</span><span id="ROMDownloader-242"><a href="#ROMDownloader-242"><span class="linenos">242</span></a>            <span class="n">headers</span> <span class="o">=</span> <span class="p">{}</span>
</span><span id="ROMDownloader-243"><a href="#ROMDownloader-243"><span class="linenos">243</span></a>            <span class="k">if</span> <span class="n">resume_pos</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="ROMDownloader-244"><a href="#ROMDownloader-244"><span class="linenos">244</span></a>                <span class="n">headers</span><span class="p">[</span><span class="s1">&#39;Range&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="sa">f</span><span class="s1">&#39;bytes=</span><span class="si">{</span><span class="n">resume_pos</span><span class="si">}</span><span class="s1">-&#39;</span>
</span><span id="ROMDownloader-245"><a href="#ROMDownloader-245"><span class="linenos">245</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;断点续传: </span><span class="si">{</span><span class="n">resume_pos</span><span class="si">}</span><span class="s2"> bytes&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-246"><a href="#ROMDownloader-246"><span class="linenos">246</span></a>            
</span><span id="ROMDownloader-247"><a href="#ROMDownloader-247"><span class="linenos">247</span></a>            <span class="c1"># 开始下载</span>
</span><span id="ROMDownloader-248"><a href="#ROMDownloader-248"><span class="linenos">248</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">headers</span><span class="o">=</span><span class="n">headers</span><span class="p">,</span> <span class="n">stream</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="ROMDownloader-249"><a href="#ROMDownloader-249"><span class="linenos">249</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="ROMDownloader-250"><a href="#ROMDownloader-250"><span class="linenos">250</span></a>            
</span><span id="ROMDownloader-251"><a href="#ROMDownloader-251"><span class="linenos">251</span></a>            <span class="n">mode</span> <span class="o">=</span> <span class="s1">&#39;ab&#39;</span> <span class="k">if</span> <span class="n">resume_pos</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="k">else</span> <span class="s1">&#39;wb&#39;</span>
</span><span id="ROMDownloader-252"><a href="#ROMDownloader-252"><span class="linenos">252</span></a>            
</span><span id="ROMDownloader-253"><a href="#ROMDownloader-253"><span class="linenos">253</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">temp_path</span><span class="p">,</span> <span class="n">mode</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="ROMDownloader-254"><a href="#ROMDownloader-254"><span class="linenos">254</span></a>                <span class="k">with</span> <span class="n">tqdm</span><span class="p">(</span>
</span><span id="ROMDownloader-255"><a href="#ROMDownloader-255"><span class="linenos">255</span></a>                    <span class="n">total</span><span class="o">=</span><span class="n">total_size</span><span class="p">,</span>
</span><span id="ROMDownloader-256"><a href="#ROMDownloader-256"><span class="linenos">256</span></a>                    <span class="n">initial</span><span class="o">=</span><span class="n">resume_pos</span><span class="p">,</span>
</span><span id="ROMDownloader-257"><a href="#ROMDownloader-257"><span class="linenos">257</span></a>                    <span class="n">unit</span><span class="o">=</span><span class="s1">&#39;B&#39;</span><span class="p">,</span>
</span><span id="ROMDownloader-258"><a href="#ROMDownloader-258"><span class="linenos">258</span></a>                    <span class="n">unit_scale</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
</span><span id="ROMDownloader-259"><a href="#ROMDownloader-259"><span class="linenos">259</span></a>                    <span class="n">desc</span><span class="o">=</span><span class="n">filename</span>
</span><span id="ROMDownloader-260"><a href="#ROMDownloader-260"><span class="linenos">260</span></a>                <span class="p">)</span> <span class="k">as</span> <span class="n">pbar</span><span class="p">:</span>
</span><span id="ROMDownloader-261"><a href="#ROMDownloader-261"><span class="linenos">261</span></a>                    
</span><span id="ROMDownloader-262"><a href="#ROMDownloader-262"><span class="linenos">262</span></a>                    <span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="n">response</span><span class="o">.</span><span class="n">iter_content</span><span class="p">(</span><span class="n">chunk_size</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;download&quot;</span><span class="p">][</span><span class="s2">&quot;chunk_size&quot;</span><span class="p">]):</span>
</span><span id="ROMDownloader-263"><a href="#ROMDownloader-263"><span class="linenos">263</span></a>                        <span class="k">if</span> <span class="n">chunk</span><span class="p">:</span>
</span><span id="ROMDownloader-264"><a href="#ROMDownloader-264"><span class="linenos">264</span></a>                            <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</span><span id="ROMDownloader-265"><a href="#ROMDownloader-265"><span class="linenos">265</span></a>                            <span class="n">pbar</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">chunk</span><span class="p">))</span>
</span><span id="ROMDownloader-266"><a href="#ROMDownloader-266"><span class="linenos">266</span></a>            
</span><span id="ROMDownloader-267"><a href="#ROMDownloader-267"><span class="linenos">267</span></a>            <span class="c1"># 重命名临时文件</span>
</span><span id="ROMDownloader-268"><a href="#ROMDownloader-268"><span class="linenos">268</span></a>            <span class="n">temp_path</span><span class="o">.</span><span class="n">rename</span><span class="p">(</span><span class="n">file_path</span><span class="p">)</span>
</span><span id="ROMDownloader-269"><a href="#ROMDownloader-269"><span class="linenos">269</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载完成: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-270"><a href="#ROMDownloader-270"><span class="linenos">270</span></a>            <span class="k">return</span> <span class="n">file_path</span>
</span><span id="ROMDownloader-271"><a href="#ROMDownloader-271"><span class="linenos">271</span></a>            
</span><span id="ROMDownloader-272"><a href="#ROMDownloader-272"><span class="linenos">272</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader-273"><a href="#ROMDownloader-273"><span class="linenos">273</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-274"><a href="#ROMDownloader-274"><span class="linenos">274</span></a>            <span class="c1"># 清理临时文件</span>
</span><span id="ROMDownloader-275"><a href="#ROMDownloader-275"><span class="linenos">275</span></a>            <span class="k">if</span> <span class="n">temp_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="ROMDownloader-276"><a href="#ROMDownloader-276"><span class="linenos">276</span></a>                <span class="n">temp_path</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
</span><span id="ROMDownloader-277"><a href="#ROMDownloader-277"><span class="linenos">277</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="ROMDownloader-278"><a href="#ROMDownloader-278"><span class="linenos">278</span></a>    
</span><span id="ROMDownloader-279"><a href="#ROMDownloader-279"><span class="linenos">279</span></a>    <span class="k">def</span> <span class="nf">verify_file</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">file_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="ROMDownloader-280"><a href="#ROMDownloader-280"><span class="linenos">280</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;验证文件完整性&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-281"><a href="#ROMDownloader-281"><span class="linenos">281</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;verification&quot;</span><span class="p">][</span><span class="s2">&quot;verify_checksum&quot;</span><span class="p">]:</span>
</span><span id="ROMDownloader-282"><a href="#ROMDownloader-282"><span class="linenos">282</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="ROMDownloader-283"><a href="#ROMDownloader-283"><span class="linenos">283</span></a>        
</span><span id="ROMDownloader-284"><a href="#ROMDownloader-284"><span class="linenos">284</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;验证文件完整性: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-285"><a href="#ROMDownloader-285"><span class="linenos">285</span></a>        
</span><span id="ROMDownloader-286"><a href="#ROMDownloader-286"><span class="linenos">286</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-287"><a href="#ROMDownloader-287"><span class="linenos">287</span></a>            <span class="n">algorithm</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;verification&quot;</span><span class="p">][</span><span class="s2">&quot;checksum_algorithm&quot;</span><span class="p">]</span>
</span><span id="ROMDownloader-288"><a href="#ROMDownloader-288"><span class="linenos">288</span></a>            <span class="n">hash_func</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">hashlib</span><span class="p">,</span> <span class="n">algorithm</span><span class="p">)()</span>
</span><span id="ROMDownloader-289"><a href="#ROMDownloader-289"><span class="linenos">289</span></a>            
</span><span id="ROMDownloader-290"><a href="#ROMDownloader-290"><span class="linenos">290</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">file_path</span><span class="p">,</span> <span class="s1">&#39;rb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="ROMDownloader-291"><a href="#ROMDownloader-291"><span class="linenos">291</span></a>                <span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="nb">iter</span><span class="p">(</span><span class="k">lambda</span><span class="p">:</span> <span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="mi">8192</span><span class="p">),</span> <span class="sa">b</span><span class="s2">&quot;&quot;</span><span class="p">):</span>
</span><span id="ROMDownloader-292"><a href="#ROMDownloader-292"><span class="linenos">292</span></a>                    <span class="n">hash_func</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</span><span id="ROMDownloader-293"><a href="#ROMDownloader-293"><span class="linenos">293</span></a>            
</span><span id="ROMDownloader-294"><a href="#ROMDownloader-294"><span class="linenos">294</span></a>            <span class="n">checksum</span> <span class="o">=</span> <span class="n">hash_func</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
</span><span id="ROMDownloader-295"><a href="#ROMDownloader-295"><span class="linenos">295</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件校验和 (</span><span class="si">{</span><span class="n">algorithm</span><span class="si">}</span><span class="s2">): </span><span class="si">{</span><span class="n">checksum</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-296"><a href="#ROMDownloader-296"><span class="linenos">296</span></a>            
</span><span id="ROMDownloader-297"><a href="#ROMDownloader-297"><span class="linenos">297</span></a>            <span class="c1"># 这里可以添加预定义的校验和验证</span>
</span><span id="ROMDownloader-298"><a href="#ROMDownloader-298"><span class="linenos">298</span></a>            <span class="c1"># 由于ROM文件可能来自不同源，暂时只计算校验和</span>
</span><span id="ROMDownloader-299"><a href="#ROMDownloader-299"><span class="linenos">299</span></a>            
</span><span id="ROMDownloader-300"><a href="#ROMDownloader-300"><span class="linenos">300</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="ROMDownloader-301"><a href="#ROMDownloader-301"><span class="linenos">301</span></a>            
</span><span id="ROMDownloader-302"><a href="#ROMDownloader-302"><span class="linenos">302</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader-303"><a href="#ROMDownloader-303"><span class="linenos">303</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件验证失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-304"><a href="#ROMDownloader-304"><span class="linenos">304</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader-305"><a href="#ROMDownloader-305"><span class="linenos">305</span></a>    
</span><span id="ROMDownloader-306"><a href="#ROMDownloader-306"><span class="linenos">306</span></a>    <span class="k">def</span> <span class="nf">extract_roms</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">zip_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Path</span><span class="p">]:</span>
</span><span id="ROMDownloader-307"><a href="#ROMDownloader-307"><span class="linenos">307</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;解压ROM文件&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-308"><a href="#ROMDownloader-308"><span class="linenos">308</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压ROM文件: </span><span class="si">{</span><span class="n">zip_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-309"><a href="#ROMDownloader-309"><span class="linenos">309</span></a>        
</span><span id="ROMDownloader-310"><a href="#ROMDownloader-310"><span class="linenos">310</span></a>        <span class="n">rom_files</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="ROMDownloader-311"><a href="#ROMDownloader-311"><span class="linenos">311</span></a>        <span class="n">extract_dir</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="s2">&quot;extracted&quot;</span>
</span><span id="ROMDownloader-312"><a href="#ROMDownloader-312"><span class="linenos">312</span></a>        <span class="n">extract_dir</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="ROMDownloader-313"><a href="#ROMDownloader-313"><span class="linenos">313</span></a>        
</span><span id="ROMDownloader-314"><a href="#ROMDownloader-314"><span class="linenos">314</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-315"><a href="#ROMDownloader-315"><span class="linenos">315</span></a>            <span class="k">with</span> <span class="n">zipfile</span><span class="o">.</span><span class="n">ZipFile</span><span class="p">(</span><span class="n">zip_path</span><span class="p">,</span> <span class="s1">&#39;r&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">zip_ref</span><span class="p">:</span>
</span><span id="ROMDownloader-316"><a href="#ROMDownloader-316"><span class="linenos">316</span></a>                <span class="c1"># 列出所有文件</span>
</span><span id="ROMDownloader-317"><a href="#ROMDownloader-317"><span class="linenos">317</span></a>                <span class="n">file_list</span> <span class="o">=</span> <span class="n">zip_ref</span><span class="o">.</span><span class="n">namelist</span><span class="p">()</span>
</span><span id="ROMDownloader-318"><a href="#ROMDownloader-318"><span class="linenos">318</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;ZIP文件包含 </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">file_list</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个文件&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-319"><a href="#ROMDownloader-319"><span class="linenos">319</span></a>                
</span><span id="ROMDownloader-320"><a href="#ROMDownloader-320"><span class="linenos">320</span></a>                <span class="c1"># 过滤ROM文件</span>
</span><span id="ROMDownloader-321"><a href="#ROMDownloader-321"><span class="linenos">321</span></a>                <span class="n">rom_extensions</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;.nes&#39;</span><span class="p">,</span> <span class="s1">&#39;.NES&#39;</span><span class="p">]</span>
</span><span id="ROMDownloader-322"><a href="#ROMDownloader-322"><span class="linenos">322</span></a>                <span class="n">rom_files_in_zip</span> <span class="o">=</span> <span class="p">[</span><span class="n">f</span> <span class="k">for</span> <span class="n">f</span> <span class="ow">in</span> <span class="n">file_list</span> <span class="k">if</span> <span class="nb">any</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">endswith</span><span class="p">(</span><span class="n">ext</span><span class="p">)</span> <span class="k">for</span> <span class="n">ext</span> <span class="ow">in</span> <span class="n">rom_extensions</span><span class="p">)]</span>
</span><span id="ROMDownloader-323"><a href="#ROMDownloader-323"><span class="linenos">323</span></a>                
</span><span id="ROMDownloader-324"><a href="#ROMDownloader-324"><span class="linenos">324</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到 </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">rom_files_in_zip</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个ROM文件&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-325"><a href="#ROMDownloader-325"><span class="linenos">325</span></a>                
</span><span id="ROMDownloader-326"><a href="#ROMDownloader-326"><span class="linenos">326</span></a>                <span class="c1"># 解压ROM文件</span>
</span><span id="ROMDownloader-327"><a href="#ROMDownloader-327"><span class="linenos">327</span></a>                <span class="k">for</span> <span class="n">rom_file</span> <span class="ow">in</span> <span class="n">rom_files_in_zip</span><span class="p">:</span>
</span><span id="ROMDownloader-328"><a href="#ROMDownloader-328"><span class="linenos">328</span></a>                    <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-329"><a href="#ROMDownloader-329"><span class="linenos">329</span></a>                        <span class="n">zip_ref</span><span class="o">.</span><span class="n">extract</span><span class="p">(</span><span class="n">rom_file</span><span class="p">,</span> <span class="n">extract_dir</span><span class="p">)</span>
</span><span id="ROMDownloader-330"><a href="#ROMDownloader-330"><span class="linenos">330</span></a>                        <span class="n">rom_path</span> <span class="o">=</span> <span class="n">extract_dir</span> <span class="o">/</span> <span class="n">rom_file</span>
</span><span id="ROMDownloader-331"><a href="#ROMDownloader-331"><span class="linenos">331</span></a>                        <span class="n">rom_files</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">rom_path</span><span class="p">)</span>
</span><span id="ROMDownloader-332"><a href="#ROMDownloader-332"><span class="linenos">332</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压: </span><span class="si">{</span><span class="n">rom_file</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-333"><a href="#ROMDownloader-333"><span class="linenos">333</span></a>                    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader-334"><a href="#ROMDownloader-334"><span class="linenos">334</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压文件失败 </span><span class="si">{</span><span class="n">rom_file</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-335"><a href="#ROMDownloader-335"><span class="linenos">335</span></a>            
</span><span id="ROMDownloader-336"><a href="#ROMDownloader-336"><span class="linenos">336</span></a>            <span class="k">return</span> <span class="n">rom_files</span>
</span><span id="ROMDownloader-337"><a href="#ROMDownloader-337"><span class="linenos">337</span></a>            
</span><span id="ROMDownloader-338"><a href="#ROMDownloader-338"><span class="linenos">338</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader-339"><a href="#ROMDownloader-339"><span class="linenos">339</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-340"><a href="#ROMDownloader-340"><span class="linenos">340</span></a>            <span class="k">return</span> <span class="p">[]</span>
</span><span id="ROMDownloader-341"><a href="#ROMDownloader-341"><span class="linenos">341</span></a>    
</span><span id="ROMDownloader-342"><a href="#ROMDownloader-342"><span class="linenos">342</span></a>    <span class="k">def</span> <span class="nf">connect_sftp</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">paramiko</span><span class="o">.</span><span class="n">SSHClient</span><span class="p">]:</span>
</span><span id="ROMDownloader-343"><a href="#ROMDownloader-343"><span class="linenos">343</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;连接SFTP服务器&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-344"><a href="#ROMDownloader-344"><span class="linenos">344</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;连接树莓派SFTP服务器...&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-345"><a href="#ROMDownloader-345"><span class="linenos">345</span></a>        
</span><span id="ROMDownloader-346"><a href="#ROMDownloader-346"><span class="linenos">346</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-347"><a href="#ROMDownloader-347"><span class="linenos">347</span></a>            <span class="n">ssh</span> <span class="o">=</span> <span class="n">paramiko</span><span class="o">.</span><span class="n">SSHClient</span><span class="p">()</span>
</span><span id="ROMDownloader-348"><a href="#ROMDownloader-348"><span class="linenos">348</span></a>            <span class="n">ssh</span><span class="o">.</span><span class="n">set_missing_host_key_policy</span><span class="p">(</span><span class="n">paramiko</span><span class="o">.</span><span class="n">AutoAddPolicy</span><span class="p">())</span>
</span><span id="ROMDownloader-349"><a href="#ROMDownloader-349"><span class="linenos">349</span></a>            
</span><span id="ROMDownloader-350"><a href="#ROMDownloader-350"><span class="linenos">350</span></a>            <span class="n">pi_config</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;raspberry_pi&quot;</span><span class="p">]</span>
</span><span id="ROMDownloader-351"><a href="#ROMDownloader-351"><span class="linenos">351</span></a>            
</span><span id="ROMDownloader-352"><a href="#ROMDownloader-352"><span class="linenos">352</span></a>            <span class="c1"># 使用密钥文件或密码</span>
</span><span id="ROMDownloader-353"><a href="#ROMDownloader-353"><span class="linenos">353</span></a>            <span class="k">if</span> <span class="n">pi_config</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;key_file&quot;</span><span class="p">)</span> <span class="ow">and</span> <span class="n">Path</span><span class="p">(</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;key_file&quot;</span><span class="p">])</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="ROMDownloader-354"><a href="#ROMDownloader-354"><span class="linenos">354</span></a>                <span class="n">ssh</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span>
</span><span id="ROMDownloader-355"><a href="#ROMDownloader-355"><span class="linenos">355</span></a>                    <span class="n">hostname</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;host&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader-356"><a href="#ROMDownloader-356"><span class="linenos">356</span></a>                    <span class="n">port</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;port&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader-357"><a href="#ROMDownloader-357"><span class="linenos">357</span></a>                    <span class="n">username</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;username&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader-358"><a href="#ROMDownloader-358"><span class="linenos">358</span></a>                    <span class="n">key_filename</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;key_file&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader-359"><a href="#ROMDownloader-359"><span class="linenos">359</span></a>                    <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span>
</span><span id="ROMDownloader-360"><a href="#ROMDownloader-360"><span class="linenos">360</span></a>                <span class="p">)</span>
</span><span id="ROMDownloader-361"><a href="#ROMDownloader-361"><span class="linenos">361</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="ROMDownloader-362"><a href="#ROMDownloader-362"><span class="linenos">362</span></a>                <span class="n">ssh</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span>
</span><span id="ROMDownloader-363"><a href="#ROMDownloader-363"><span class="linenos">363</span></a>                    <span class="n">hostname</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;host&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader-364"><a href="#ROMDownloader-364"><span class="linenos">364</span></a>                    <span class="n">port</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;port&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader-365"><a href="#ROMDownloader-365"><span class="linenos">365</span></a>                    <span class="n">username</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;username&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader-366"><a href="#ROMDownloader-366"><span class="linenos">366</span></a>                    <span class="n">password</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;password&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader-367"><a href="#ROMDownloader-367"><span class="linenos">367</span></a>                    <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span>
</span><span id="ROMDownloader-368"><a href="#ROMDownloader-368"><span class="linenos">368</span></a>                <span class="p">)</span>
</span><span id="ROMDownloader-369"><a href="#ROMDownloader-369"><span class="linenos">369</span></a>            
</span><span id="ROMDownloader-370"><a href="#ROMDownloader-370"><span class="linenos">370</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;SFTP连接成功&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-371"><a href="#ROMDownloader-371"><span class="linenos">371</span></a>            <span class="k">return</span> <span class="n">ssh</span>
</span><span id="ROMDownloader-372"><a href="#ROMDownloader-372"><span class="linenos">372</span></a>            
</span><span id="ROMDownloader-373"><a href="#ROMDownloader-373"><span class="linenos">373</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader-374"><a href="#ROMDownloader-374"><span class="linenos">374</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;SFTP连接失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-375"><a href="#ROMDownloader-375"><span class="linenos">375</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="ROMDownloader-376"><a href="#ROMDownloader-376"><span class="linenos">376</span></a>    
</span><span id="ROMDownloader-377"><a href="#ROMDownloader-377"><span class="linenos">377</span></a>    <span class="k">def</span> <span class="nf">upload_roms</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">rom_files</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">Path</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="ROMDownloader-378"><a href="#ROMDownloader-378"><span class="linenos">378</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;上传ROM文件到树莓派&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-379"><a href="#ROMDownloader-379"><span class="linenos">379</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">rom_files</span><span class="p">:</span>
</span><span id="ROMDownloader-380"><a href="#ROMDownloader-380"><span class="linenos">380</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;没有ROM文件需要上传&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-381"><a href="#ROMDownloader-381"><span class="linenos">381</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader-382"><a href="#ROMDownloader-382"><span class="linenos">382</span></a>        
</span><span id="ROMDownloader-383"><a href="#ROMDownloader-383"><span class="linenos">383</span></a>        <span class="n">ssh</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">connect_sftp</span><span class="p">()</span>
</span><span id="ROMDownloader-384"><a href="#ROMDownloader-384"><span class="linenos">384</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">ssh</span><span class="p">:</span>
</span><span id="ROMDownloader-385"><a href="#ROMDownloader-385"><span class="linenos">385</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader-386"><a href="#ROMDownloader-386"><span class="linenos">386</span></a>        
</span><span id="ROMDownloader-387"><a href="#ROMDownloader-387"><span class="linenos">387</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-388"><a href="#ROMDownloader-388"><span class="linenos">388</span></a>            <span class="n">sftp</span> <span class="o">=</span> <span class="n">ssh</span><span class="o">.</span><span class="n">open_sftp</span><span class="p">()</span>
</span><span id="ROMDownloader-389"><a href="#ROMDownloader-389"><span class="linenos">389</span></a>            <span class="n">pi_config</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;raspberry_pi&quot;</span><span class="p">]</span>
</span><span id="ROMDownloader-390"><a href="#ROMDownloader-390"><span class="linenos">390</span></a>            <span class="n">remote_path</span> <span class="o">=</span> <span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;roms_path&quot;</span><span class="p">]</span>
</span><span id="ROMDownloader-391"><a href="#ROMDownloader-391"><span class="linenos">391</span></a>            
</span><span id="ROMDownloader-392"><a href="#ROMDownloader-392"><span class="linenos">392</span></a>            <span class="c1"># 确保远程目录存在</span>
</span><span id="ROMDownloader-393"><a href="#ROMDownloader-393"><span class="linenos">393</span></a>            <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-394"><a href="#ROMDownloader-394"><span class="linenos">394</span></a>                <span class="n">sftp</span><span class="o">.</span><span class="n">stat</span><span class="p">(</span><span class="n">remote_path</span><span class="p">)</span>
</span><span id="ROMDownloader-395"><a href="#ROMDownloader-395"><span class="linenos">395</span></a>            <span class="k">except</span> <span class="ne">FileNotFoundError</span><span class="p">:</span>
</span><span id="ROMDownloader-396"><a href="#ROMDownloader-396"><span class="linenos">396</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;创建远程目录: </span><span class="si">{</span><span class="n">remote_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-397"><a href="#ROMDownloader-397"><span class="linenos">397</span></a>                <span class="n">sftp</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">remote_path</span><span class="p">)</span>
</span><span id="ROMDownloader-398"><a href="#ROMDownloader-398"><span class="linenos">398</span></a>            
</span><span id="ROMDownloader-399"><a href="#ROMDownloader-399"><span class="linenos">399</span></a>            <span class="n">uploaded_count</span> <span class="o">=</span> <span class="mi">0</span>
</span><span id="ROMDownloader-400"><a href="#ROMDownloader-400"><span class="linenos">400</span></a>            
</span><span id="ROMDownloader-401"><a href="#ROMDownloader-401"><span class="linenos">401</span></a>            <span class="k">for</span> <span class="n">rom_file</span> <span class="ow">in</span> <span class="n">rom_files</span><span class="p">:</span>
</span><span id="ROMDownloader-402"><a href="#ROMDownloader-402"><span class="linenos">402</span></a>                <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-403"><a href="#ROMDownloader-403"><span class="linenos">403</span></a>                    <span class="n">remote_file</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">remote_path</span><span class="si">}</span><span class="s2">/</span><span class="si">{</span><span class="n">rom_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="ROMDownloader-404"><a href="#ROMDownloader-404"><span class="linenos">404</span></a>                    
</span><span id="ROMDownloader-405"><a href="#ROMDownloader-405"><span class="linenos">405</span></a>                    <span class="c1"># 检查文件是否已存在</span>
</span><span id="ROMDownloader-406"><a href="#ROMDownloader-406"><span class="linenos">406</span></a>                    <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader-407"><a href="#ROMDownloader-407"><span class="linenos">407</span></a>                        <span class="n">remote_stat</span> <span class="o">=</span> <span class="n">sftp</span><span class="o">.</span><span class="n">stat</span><span class="p">(</span><span class="n">remote_file</span><span class="p">)</span>
</span><span id="ROMDownloader-408"><a href="#ROMDownloader-408"><span class="linenos">408</span></a>                        <span class="n">local_stat</span> <span class="o">=</span> <span class="n">rom_file</span><span class="o">.</span><span class="n">stat</span><span class="p">()</span>
</span><span id="ROMDownloader-409"><a href="#ROMDownloader-409"><span class="linenos">409</span></a>                        
</span><span id="ROMDownloader-410"><a href="#ROMDownloader-410"><span class="linenos">410</span></a>                        <span class="k">if</span> <span class="n">remote_stat</span><span class="o">.</span><span class="n">st_size</span> <span class="o">==</span> <span class="n">local_stat</span><span class="o">.</span><span class="n">st_size</span><span class="p">:</span>
</span><span id="ROMDownloader-411"><a href="#ROMDownloader-411"><span class="linenos">411</span></a>                            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件已存在，跳过: </span><span class="si">{</span><span class="n">rom_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-412"><a href="#ROMDownloader-412"><span class="linenos">412</span></a>                            <span class="n">uploaded_count</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="ROMDownloader-413"><a href="#ROMDownloader-413"><span class="linenos">413</span></a>                            <span class="k">continue</span>
</span><span id="ROMDownloader-414"><a href="#ROMDownloader-414"><span class="linenos">414</span></a>                    <span class="k">except</span> <span class="ne">FileNotFoundError</span><span class="p">:</span>
</span><span id="ROMDownloader-415"><a href="#ROMDownloader-415"><span class="linenos">415</span></a>                        <span class="k">pass</span>
</span><span id="ROMDownloader-416"><a href="#ROMDownloader-416"><span class="linenos">416</span></a>                    
</span><span id="ROMDownloader-417"><a href="#ROMDownloader-417"><span class="linenos">417</span></a>                    <span class="c1"># 上传文件</span>
</span><span id="ROMDownloader-418"><a href="#ROMDownloader-418"><span class="linenos">418</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;上传: </span><span class="si">{</span><span class="n">rom_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-419"><a href="#ROMDownloader-419"><span class="linenos">419</span></a>                    <span class="n">sftp</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">rom_file</span><span class="p">),</span> <span class="n">remote_file</span><span class="p">)</span>
</span><span id="ROMDownloader-420"><a href="#ROMDownloader-420"><span class="linenos">420</span></a>                    <span class="n">uploaded_count</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="ROMDownloader-421"><a href="#ROMDownloader-421"><span class="linenos">421</span></a>                    
</span><span id="ROMDownloader-422"><a href="#ROMDownloader-422"><span class="linenos">422</span></a>                <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader-423"><a href="#ROMDownloader-423"><span class="linenos">423</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;上传文件失败 </span><span class="si">{</span><span class="n">rom_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-424"><a href="#ROMDownloader-424"><span class="linenos">424</span></a>            
</span><span id="ROMDownloader-425"><a href="#ROMDownloader-425"><span class="linenos">425</span></a>            <span class="n">sftp</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</span><span id="ROMDownloader-426"><a href="#ROMDownloader-426"><span class="linenos">426</span></a>            <span class="n">ssh</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</span><span id="ROMDownloader-427"><a href="#ROMDownloader-427"><span class="linenos">427</span></a>            
</span><span id="ROMDownloader-428"><a href="#ROMDownloader-428"><span class="linenos">428</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;上传完成: </span><span class="si">{</span><span class="n">uploaded_count</span><span class="si">}</span><span class="s2">/</span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">rom_files</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个文件&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-429"><a href="#ROMDownloader-429"><span class="linenos">429</span></a>            <span class="k">return</span> <span class="n">uploaded_count</span> <span class="o">&gt;</span> <span class="mi">0</span>
</span><span id="ROMDownloader-430"><a href="#ROMDownloader-430"><span class="linenos">430</span></a>            
</span><span id="ROMDownloader-431"><a href="#ROMDownloader-431"><span class="linenos">431</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader-432"><a href="#ROMDownloader-432"><span class="linenos">432</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;上传过程出错: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-433"><a href="#ROMDownloader-433"><span class="linenos">433</span></a>            <span class="n">ssh</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</span><span id="ROMDownloader-434"><a href="#ROMDownloader-434"><span class="linenos">434</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader-435"><a href="#ROMDownloader-435"><span class="linenos">435</span></a>    
</span><span id="ROMDownloader-436"><a href="#ROMDownloader-436"><span class="linenos">436</span></a>    <span class="k">def</span> <span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">search_query</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;nes 100 in 1&quot;</span><span class="p">):</span>
</span><span id="ROMDownloader-437"><a href="#ROMDownloader-437"><span class="linenos">437</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;运行完整的下载和传输流程&quot;&quot;&quot;</span>
</span><span id="ROMDownloader-438"><a href="#ROMDownloader-438"><span class="linenos">438</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;=== NES ROM 下载和传输工具 ===&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-439"><a href="#ROMDownloader-439"><span class="linenos">439</span></a>
</span><span id="ROMDownloader-440"><a href="#ROMDownloader-440"><span class="linenos">440</span></a>        <span class="c1"># 1. 优先尝试配置文件中的下载地址</span>
</span><span id="ROMDownloader-441"><a href="#ROMDownloader-441"><span class="linenos">441</span></a>        <span class="n">nes_zip_urls</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;nes_zip_urls&quot;</span><span class="p">,</span> <span class="p">[])</span>
</span><span id="ROMDownloader-442"><a href="#ROMDownloader-442"><span class="linenos">442</span></a>        <span class="n">zip_path</span> <span class="o">=</span> <span class="kc">None</span>
</span><span id="ROMDownloader-443"><a href="#ROMDownloader-443"><span class="linenos">443</span></a>        <span class="n">download_url</span> <span class="o">=</span> <span class="kc">None</span>
</span><span id="ROMDownloader-444"><a href="#ROMDownloader-444"><span class="linenos">444</span></a>        <span class="k">if</span> <span class="n">nes_zip_urls</span><span class="p">:</span>
</span><span id="ROMDownloader-445"><a href="#ROMDownloader-445"><span class="linenos">445</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置文件中找到 </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">nes_zip_urls</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个NES ZIP下载地址，优先尝试...&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-446"><a href="#ROMDownloader-446"><span class="linenos">446</span></a>            <span class="k">for</span> <span class="n">url</span> <span class="ow">in</span> <span class="n">nes_zip_urls</span><span class="p">:</span>
</span><span id="ROMDownloader-447"><a href="#ROMDownloader-447"><span class="linenos">447</span></a>                <span class="n">filename</span> <span class="o">=</span> <span class="n">url</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;/&#39;</span><span class="p">)[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
</span><span id="ROMDownloader-448"><a href="#ROMDownloader-448"><span class="linenos">448</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;尝试下载: </span><span class="si">{</span><span class="n">url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-449"><a href="#ROMDownloader-449"><span class="linenos">449</span></a>                <span class="n">zip_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_file</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">filename</span><span class="p">)</span>
</span><span id="ROMDownloader-450"><a href="#ROMDownloader-450"><span class="linenos">450</span></a>                <span class="k">if</span> <span class="n">zip_path</span><span class="p">:</span>
</span><span id="ROMDownloader-451"><a href="#ROMDownloader-451"><span class="linenos">451</span></a>                    <span class="n">download_url</span> <span class="o">=</span> <span class="n">url</span>
</span><span id="ROMDownloader-452"><a href="#ROMDownloader-452"><span class="linenos">452</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;成功下载: </span><span class="si">{</span><span class="n">url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-453"><a href="#ROMDownloader-453"><span class="linenos">453</span></a>                    <span class="k">break</span>
</span><span id="ROMDownloader-454"><a href="#ROMDownloader-454"><span class="linenos">454</span></a>                <span class="k">else</span><span class="p">:</span>
</span><span id="ROMDownloader-455"><a href="#ROMDownloader-455"><span class="linenos">455</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载失败: </span><span class="si">{</span><span class="n">url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-456"><a href="#ROMDownloader-456"><span class="linenos">456</span></a>        
</span><span id="ROMDownloader-457"><a href="#ROMDownloader-457"><span class="linenos">457</span></a>        <span class="c1"># 2. 如果配置地址全部失败，则自动搜索</span>
</span><span id="ROMDownloader-458"><a href="#ROMDownloader-458"><span class="linenos">458</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">zip_path</span><span class="p">:</span>
</span><span id="ROMDownloader-459"><a href="#ROMDownloader-459"><span class="linenos">459</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;配置地址全部失败，自动搜索可用ROM...&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-460"><a href="#ROMDownloader-460"><span class="linenos">460</span></a>            <span class="n">results</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">search_roms</span><span class="p">(</span><span class="n">search_query</span><span class="p">)</span>
</span><span id="ROMDownloader-461"><a href="#ROMDownloader-461"><span class="linenos">461</span></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">results</span><span class="p">:</span>
</span><span id="ROMDownloader-462"><a href="#ROMDownloader-462"><span class="linenos">462</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;未找到ROM文件&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-463"><a href="#ROMDownloader-463"><span class="linenos">463</span></a>                <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader-464"><a href="#ROMDownloader-464"><span class="linenos">464</span></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">搜索结果:&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-465"><a href="#ROMDownloader-465"><span class="linenos">465</span></a>            <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">result</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">results</span><span class="p">[:</span><span class="mi">5</span><span class="p">]):</span>
</span><span id="ROMDownloader-466"><a href="#ROMDownloader-466"><span class="linenos">466</span></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">. </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;title&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2"> (下载次数: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;downloads&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">)&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-467"><a href="#ROMDownloader-467"><span class="linenos">467</span></a>            <span class="n">selected_result</span> <span class="o">=</span> <span class="n">results</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
</span><span id="ROMDownloader-468"><a href="#ROMDownloader-468"><span class="linenos">468</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;选择: </span><span class="si">{</span><span class="n">selected_result</span><span class="p">[</span><span class="s1">&#39;title&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-469"><a href="#ROMDownloader-469"><span class="linenos">469</span></a>            <span class="n">download_url</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_download_url</span><span class="p">(</span><span class="n">selected_result</span><span class="p">[</span><span class="s1">&#39;identifier&#39;</span><span class="p">])</span>
</span><span id="ROMDownloader-470"><a href="#ROMDownloader-470"><span class="linenos">470</span></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">download_url</span><span class="p">:</span>
</span><span id="ROMDownloader-471"><a href="#ROMDownloader-471"><span class="linenos">471</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;无法获取下载链接&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-472"><a href="#ROMDownloader-472"><span class="linenos">472</span></a>                <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader-473"><a href="#ROMDownloader-473"><span class="linenos">473</span></a>            <span class="n">filename</span> <span class="o">=</span> <span class="n">download_url</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;/&#39;</span><span class="p">)[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
</span><span id="ROMDownloader-474"><a href="#ROMDownloader-474"><span class="linenos">474</span></a>            <span class="n">zip_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_file</span><span class="p">(</span><span class="n">download_url</span><span class="p">,</span> <span class="n">filename</span><span class="p">)</span>
</span><span id="ROMDownloader-475"><a href="#ROMDownloader-475"><span class="linenos">475</span></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">zip_path</span><span class="p">:</span>
</span><span id="ROMDownloader-476"><a href="#ROMDownloader-476"><span class="linenos">476</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;下载失败&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-477"><a href="#ROMDownloader-477"><span class="linenos">477</span></a>                <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader-478"><a href="#ROMDownloader-478"><span class="linenos">478</span></a>
</span><span id="ROMDownloader-479"><a href="#ROMDownloader-479"><span class="linenos">479</span></a>        <span class="c1"># 3. 验证文件</span>
</span><span id="ROMDownloader-480"><a href="#ROMDownloader-480"><span class="linenos">480</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">verify_file</span><span class="p">(</span><span class="n">zip_path</span><span class="p">):</span>
</span><span id="ROMDownloader-481"><a href="#ROMDownloader-481"><span class="linenos">481</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;文件验证失败&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-482"><a href="#ROMDownloader-482"><span class="linenos">482</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader-483"><a href="#ROMDownloader-483"><span class="linenos">483</span></a>
</span><span id="ROMDownloader-484"><a href="#ROMDownloader-484"><span class="linenos">484</span></a>        <span class="c1"># 4. 解压ROM文件</span>
</span><span id="ROMDownloader-485"><a href="#ROMDownloader-485"><span class="linenos">485</span></a>        <span class="n">rom_files</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">extract_roms</span><span class="p">(</span><span class="n">zip_path</span><span class="p">)</span>
</span><span id="ROMDownloader-486"><a href="#ROMDownloader-486"><span class="linenos">486</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">rom_files</span><span class="p">:</span>
</span><span id="ROMDownloader-487"><a href="#ROMDownloader-487"><span class="linenos">487</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;解压失败或未找到ROM文件&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-488"><a href="#ROMDownloader-488"><span class="linenos">488</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader-489"><a href="#ROMDownloader-489"><span class="linenos">489</span></a>
</span><span id="ROMDownloader-490"><a href="#ROMDownloader-490"><span class="linenos">490</span></a>        <span class="c1"># 5. 上传到树莓派</span>
</span><span id="ROMDownloader-491"><a href="#ROMDownloader-491"><span class="linenos">491</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">upload_roms</span><span class="p">(</span><span class="n">rom_files</span><span class="p">):</span>
</span><span id="ROMDownloader-492"><a href="#ROMDownloader-492"><span class="linenos">492</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;ROM传输完成！&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-493"><a href="#ROMDownloader-493"><span class="linenos">493</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="ROMDownloader-494"><a href="#ROMDownloader-494"><span class="linenos">494</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="ROMDownloader-495"><a href="#ROMDownloader-495"><span class="linenos">495</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;ROM传输失败&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader-496"><a href="#ROMDownloader-496"><span class="linenos">496</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span></pre></div>


            <div class="docstring"><p>ROM下载和传输工具类</p>

<p>提供完整的ROM自动化下载和传输流程，包括：</p>

<ul>
<li>ROM搜索和下载链接获取</li>
<li>断点续传的文件下载</li>
<li>文件完整性验证</li>
<li>自动解压和文件处理</li>
<li>SFTP传输到树莓派</li>
</ul>

<p>属性:
    config_file (Path): 配置文件路径
    download_dir (Path): 下载目录路径
    config (Dict): 配置字典
    session (requests.Session): HTTP会话对象</p>
</div>


                            <div id="ROMDownloader.__init__" class="classattr">
                                        <input id="ROMDownloader.__init__-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="name">ROMDownloader</span><span class="signature pdoc-code condensed">(<span class="param"><span class="n">config_file</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s1">&#39;rom_config.json&#39;</span></span>)</span>

                <label class="view-source-button" for="ROMDownloader.__init__-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#ROMDownloader.__init__"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="ROMDownloader.__init__-65"><a href="#ROMDownloader.__init__-65"><span class="linenos">65</span></a>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">config_file</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;rom_config.json&quot;</span><span class="p">):</span>
</span><span id="ROMDownloader.__init__-66"><a href="#ROMDownloader.__init__-66"><span class="linenos">66</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="ROMDownloader.__init__-67"><a href="#ROMDownloader.__init__-67"><span class="linenos">67</span></a><span class="sd">        初始化ROM下载器</span>
</span><span id="ROMDownloader.__init__-68"><a href="#ROMDownloader.__init__-68"><span class="linenos">68</span></a><span class="sd">        </span>
</span><span id="ROMDownloader.__init__-69"><a href="#ROMDownloader.__init__-69"><span class="linenos">69</span></a><span class="sd">        Args:</span>
</span><span id="ROMDownloader.__init__-70"><a href="#ROMDownloader.__init__-70"><span class="linenos">70</span></a><span class="sd">            config_file (str): 配置文件路径</span>
</span><span id="ROMDownloader.__init__-71"><a href="#ROMDownloader.__init__-71"><span class="linenos">71</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="ROMDownloader.__init__-72"><a href="#ROMDownloader.__init__-72"><span class="linenos">72</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">config_file</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">config_file</span><span class="p">)</span>
</span><span id="ROMDownloader.__init__-73"><a href="#ROMDownloader.__init__-73"><span class="linenos">73</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s2">&quot;downloads/roms&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.__init__-74"><a href="#ROMDownloader.__init__-74"><span class="linenos">74</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">parents</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="ROMDownloader.__init__-75"><a href="#ROMDownloader.__init__-75"><span class="linenos">75</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">config</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_load_config</span><span class="p">()</span>
</span><span id="ROMDownloader.__init__-76"><a href="#ROMDownloader.__init__-76"><span class="linenos">76</span></a>        
</span><span id="ROMDownloader.__init__-77"><a href="#ROMDownloader.__init__-77"><span class="linenos">77</span></a>        <span class="c1"># 设置重试策略</span>
</span><span id="ROMDownloader.__init__-78"><a href="#ROMDownloader.__init__-78"><span class="linenos">78</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">session</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_setup_session</span><span class="p">()</span>
</span></pre></div>


            <div class="docstring"><p>初始化ROM下载器</p>

<p>Args:
    config_file (str): 配置文件路径</p>
</div>


                            </div>
                            <div id="ROMDownloader.config_file" class="classattr">
                                <div class="attr variable">
            <span class="name">config_file</span>

        
    </div>
    <a class="headerlink" href="#ROMDownloader.config_file"></a>
    
    

                            </div>
                            <div id="ROMDownloader.download_dir" class="classattr">
                                <div class="attr variable">
            <span class="name">download_dir</span>

        
    </div>
    <a class="headerlink" href="#ROMDownloader.download_dir"></a>
    
    

                            </div>
                            <div id="ROMDownloader.config" class="classattr">
                                <div class="attr variable">
            <span class="name">config</span>

        
    </div>
    <a class="headerlink" href="#ROMDownloader.config"></a>
    
    

                            </div>
                            <div id="ROMDownloader.session" class="classattr">
                                <div class="attr variable">
            <span class="name">session</span>

        
    </div>
    <a class="headerlink" href="#ROMDownloader.session"></a>
    
    

                            </div>
                            <div id="ROMDownloader.search_roms" class="classattr">
                                        <input id="ROMDownloader.search_roms-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">search_roms</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">query</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s1">&#39;nes 100 in 1&#39;</span></span><span class="return-annotation">) -> <span class="n">List</span><span class="p">[</span><span class="n">Dict</span><span class="p">]</span>:</span></span>

                <label class="view-source-button" for="ROMDownloader.search_roms-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#ROMDownloader.search_roms"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="ROMDownloader.search_roms-152"><a href="#ROMDownloader.search_roms-152"><span class="linenos">152</span></a>    <span class="k">def</span> <span class="nf">search_roms</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">query</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;nes 100 in 1&quot;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Dict</span><span class="p">]:</span>
</span><span id="ROMDownloader.search_roms-153"><a href="#ROMDownloader.search_roms-153"><span class="linenos">153</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;搜索ROM文件&quot;&quot;&quot;</span>
</span><span id="ROMDownloader.search_roms-154"><a href="#ROMDownloader.search_roms-154"><span class="linenos">154</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;搜索ROM: </span><span class="si">{</span><span class="n">query</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.search_roms-155"><a href="#ROMDownloader.search_roms-155"><span class="linenos">155</span></a>        
</span><span id="ROMDownloader.search_roms-156"><a href="#ROMDownloader.search_roms-156"><span class="linenos">156</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader.search_roms-157"><a href="#ROMDownloader.search_roms-157"><span class="linenos">157</span></a>            <span class="n">search_url</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;rom_sources&quot;</span><span class="p">][</span><span class="s2">&quot;archive_org&quot;</span><span class="p">][</span><span class="s2">&quot;search_url&quot;</span><span class="p">]</span>
</span><span id="ROMDownloader.search_roms-158"><a href="#ROMDownloader.search_roms-158"><span class="linenos">158</span></a>            <span class="n">params</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="ROMDownloader.search_roms-159"><a href="#ROMDownloader.search_roms-159"><span class="linenos">159</span></a>                <span class="s2">&quot;q&quot;</span><span class="p">:</span> <span class="n">query</span><span class="p">,</span>
</span><span id="ROMDownloader.search_roms-160"><a href="#ROMDownloader.search_roms-160"><span class="linenos">160</span></a>                <span class="s2">&quot;output&quot;</span><span class="p">:</span> <span class="s2">&quot;json&quot;</span><span class="p">,</span>
</span><span id="ROMDownloader.search_roms-161"><a href="#ROMDownloader.search_roms-161"><span class="linenos">161</span></a>                <span class="s2">&quot;rows&quot;</span><span class="p">:</span> <span class="mi">50</span><span class="p">,</span>
</span><span id="ROMDownloader.search_roms-162"><a href="#ROMDownloader.search_roms-162"><span class="linenos">162</span></a>                <span class="s2">&quot;sort&quot;</span><span class="p">:</span> <span class="s2">&quot;downloads desc&quot;</span>
</span><span id="ROMDownloader.search_roms-163"><a href="#ROMDownloader.search_roms-163"><span class="linenos">163</span></a>            <span class="p">}</span>
</span><span id="ROMDownloader.search_roms-164"><a href="#ROMDownloader.search_roms-164"><span class="linenos">164</span></a>            
</span><span id="ROMDownloader.search_roms-165"><a href="#ROMDownloader.search_roms-165"><span class="linenos">165</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">search_url</span><span class="p">,</span> <span class="n">params</span><span class="o">=</span><span class="n">params</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="ROMDownloader.search_roms-166"><a href="#ROMDownloader.search_roms-166"><span class="linenos">166</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="ROMDownloader.search_roms-167"><a href="#ROMDownloader.search_roms-167"><span class="linenos">167</span></a>            
</span><span id="ROMDownloader.search_roms-168"><a href="#ROMDownloader.search_roms-168"><span class="linenos">168</span></a>            <span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
</span><span id="ROMDownloader.search_roms-169"><a href="#ROMDownloader.search_roms-169"><span class="linenos">169</span></a>            <span class="n">results</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="ROMDownloader.search_roms-170"><a href="#ROMDownloader.search_roms-170"><span class="linenos">170</span></a>            
</span><span id="ROMDownloader.search_roms-171"><a href="#ROMDownloader.search_roms-171"><span class="linenos">171</span></a>            <span class="k">for</span> <span class="n">doc</span> <span class="ow">in</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;response&quot;</span><span class="p">,</span> <span class="p">{})</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;docs&quot;</span><span class="p">,</span> <span class="p">[]):</span>
</span><span id="ROMDownloader.search_roms-172"><a href="#ROMDownloader.search_roms-172"><span class="linenos">172</span></a>                <span class="k">if</span> <span class="s2">&quot;downloads&quot;</span> <span class="ow">in</span> <span class="n">doc</span> <span class="ow">and</span> <span class="n">doc</span><span class="p">[</span><span class="s2">&quot;downloads&quot;</span><span class="p">]</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="ROMDownloader.search_roms-173"><a href="#ROMDownloader.search_roms-173"><span class="linenos">173</span></a>                    <span class="n">result</span> <span class="o">=</span> <span class="p">{</span>
</span><span id="ROMDownloader.search_roms-174"><a href="#ROMDownloader.search_roms-174"><span class="linenos">174</span></a>                        <span class="s2">&quot;identifier&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;identifier&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">),</span>
</span><span id="ROMDownloader.search_roms-175"><a href="#ROMDownloader.search_roms-175"><span class="linenos">175</span></a>                        <span class="s2">&quot;title&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;title&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">),</span>
</span><span id="ROMDownloader.search_roms-176"><a href="#ROMDownloader.search_roms-176"><span class="linenos">176</span></a>                        <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;description&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">),</span>
</span><span id="ROMDownloader.search_roms-177"><a href="#ROMDownloader.search_roms-177"><span class="linenos">177</span></a>                        <span class="s2">&quot;downloads&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;downloads&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>
</span><span id="ROMDownloader.search_roms-178"><a href="#ROMDownloader.search_roms-178"><span class="linenos">178</span></a>                        <span class="s2">&quot;files&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;files&quot;</span><span class="p">,</span> <span class="p">[])</span>
</span><span id="ROMDownloader.search_roms-179"><a href="#ROMDownloader.search_roms-179"><span class="linenos">179</span></a>                    <span class="p">}</span>
</span><span id="ROMDownloader.search_roms-180"><a href="#ROMDownloader.search_roms-180"><span class="linenos">180</span></a>                    <span class="n">results</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">result</span><span class="p">)</span>
</span><span id="ROMDownloader.search_roms-181"><a href="#ROMDownloader.search_roms-181"><span class="linenos">181</span></a>            
</span><span id="ROMDownloader.search_roms-182"><a href="#ROMDownloader.search_roms-182"><span class="linenos">182</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到 </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">results</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个结果&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.search_roms-183"><a href="#ROMDownloader.search_roms-183"><span class="linenos">183</span></a>            <span class="k">return</span> <span class="n">results</span>
</span><span id="ROMDownloader.search_roms-184"><a href="#ROMDownloader.search_roms-184"><span class="linenos">184</span></a>            
</span><span id="ROMDownloader.search_roms-185"><a href="#ROMDownloader.search_roms-185"><span class="linenos">185</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader.search_roms-186"><a href="#ROMDownloader.search_roms-186"><span class="linenos">186</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;搜索ROM失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.search_roms-187"><a href="#ROMDownloader.search_roms-187"><span class="linenos">187</span></a>            <span class="k">return</span> <span class="p">[]</span>
</span></pre></div>


            <div class="docstring"><p>搜索ROM文件</p>
</div>


                            </div>
                            <div id="ROMDownloader.get_download_url" class="classattr">
                                        <input id="ROMDownloader.get_download_url-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">get_download_url</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">identifier</span><span class="p">:</span> <span class="nb">str</span></span><span class="return-annotation">) -> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>:</span></span>

                <label class="view-source-button" for="ROMDownloader.get_download_url-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#ROMDownloader.get_download_url"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="ROMDownloader.get_download_url-189"><a href="#ROMDownloader.get_download_url-189"><span class="linenos">189</span></a>    <span class="k">def</span> <span class="nf">get_download_url</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">identifier</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="ROMDownloader.get_download_url-190"><a href="#ROMDownloader.get_download_url-190"><span class="linenos">190</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;获取下载链接&quot;&quot;&quot;</span>
</span><span id="ROMDownloader.get_download_url-191"><a href="#ROMDownloader.get_download_url-191"><span class="linenos">191</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader.get_download_url-192"><a href="#ROMDownloader.get_download_url-192"><span class="linenos">192</span></a>            <span class="n">metadata_url</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s1">&#39;rom_sources&#39;</span><span class="p">][</span><span class="s1">&#39;archive_org&#39;</span><span class="p">][</span><span class="s1">&#39;base_url&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">/metadata/</span><span class="si">{</span><span class="n">identifier</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="ROMDownloader.get_download_url-193"><a href="#ROMDownloader.get_download_url-193"><span class="linenos">193</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">metadata_url</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="ROMDownloader.get_download_url-194"><a href="#ROMDownloader.get_download_url-194"><span class="linenos">194</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="ROMDownloader.get_download_url-195"><a href="#ROMDownloader.get_download_url-195"><span class="linenos">195</span></a>            
</span><span id="ROMDownloader.get_download_url-196"><a href="#ROMDownloader.get_download_url-196"><span class="linenos">196</span></a>            <span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
</span><span id="ROMDownloader.get_download_url-197"><a href="#ROMDownloader.get_download_url-197"><span class="linenos">197</span></a>            <span class="n">files</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;files&quot;</span><span class="p">,</span> <span class="p">{})</span>
</span><span id="ROMDownloader.get_download_url-198"><a href="#ROMDownloader.get_download_url-198"><span class="linenos">198</span></a>            
</span><span id="ROMDownloader.get_download_url-199"><a href="#ROMDownloader.get_download_url-199"><span class="linenos">199</span></a>            <span class="c1"># 查找ZIP文件</span>
</span><span id="ROMDownloader.get_download_url-200"><a href="#ROMDownloader.get_download_url-200"><span class="linenos">200</span></a>            <span class="k">for</span> <span class="n">filename</span><span class="p">,</span> <span class="n">file_info</span> <span class="ow">in</span> <span class="n">files</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><span id="ROMDownloader.get_download_url-201"><a href="#ROMDownloader.get_download_url-201"><span class="linenos">201</span></a>                <span class="k">if</span> <span class="n">filename</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span><span class="o">.</span><span class="n">endswith</span><span class="p">(</span><span class="s1">&#39;.zip&#39;</span><span class="p">):</span>
</span><span id="ROMDownloader.get_download_url-202"><a href="#ROMDownloader.get_download_url-202"><span class="linenos">202</span></a>                    <span class="n">download_url</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s1">&#39;rom_sources&#39;</span><span class="p">][</span><span class="s1">&#39;archive_org&#39;</span><span class="p">][</span><span class="s1">&#39;base_url&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">/download/</span><span class="si">{</span><span class="n">identifier</span><span class="si">}</span><span class="s2">/</span><span class="si">{</span><span class="n">filename</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="ROMDownloader.get_download_url-203"><a href="#ROMDownloader.get_download_url-203"><span class="linenos">203</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到下载链接: </span><span class="si">{</span><span class="n">download_url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.get_download_url-204"><a href="#ROMDownloader.get_download_url-204"><span class="linenos">204</span></a>                    <span class="k">return</span> <span class="n">download_url</span>
</span><span id="ROMDownloader.get_download_url-205"><a href="#ROMDownloader.get_download_url-205"><span class="linenos">205</span></a>            
</span><span id="ROMDownloader.get_download_url-206"><a href="#ROMDownloader.get_download_url-206"><span class="linenos">206</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;未找到ZIP文件: </span><span class="si">{</span><span class="n">identifier</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.get_download_url-207"><a href="#ROMDownloader.get_download_url-207"><span class="linenos">207</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="ROMDownloader.get_download_url-208"><a href="#ROMDownloader.get_download_url-208"><span class="linenos">208</span></a>            
</span><span id="ROMDownloader.get_download_url-209"><a href="#ROMDownloader.get_download_url-209"><span class="linenos">209</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader.get_download_url-210"><a href="#ROMDownloader.get_download_url-210"><span class="linenos">210</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;获取下载链接失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.get_download_url-211"><a href="#ROMDownloader.get_download_url-211"><span class="linenos">211</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span></pre></div>


            <div class="docstring"><p>获取下载链接</p>
</div>


                            </div>
                            <div id="ROMDownloader.download_file" class="classattr">
                                        <input id="ROMDownloader.download_file-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">download_file</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">url</span><span class="p">:</span> <span class="nb">str</span>, </span><span class="param"><span class="n">filename</span><span class="p">:</span> <span class="nb">str</span></span><span class="return-annotation">) -> <span class="n">Optional</span><span class="p">[</span><span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">]</span>:</span></span>

                <label class="view-source-button" for="ROMDownloader.download_file-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#ROMDownloader.download_file"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="ROMDownloader.download_file-213"><a href="#ROMDownloader.download_file-213"><span class="linenos">213</span></a>    <span class="k">def</span> <span class="nf">download_file</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">url</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">filename</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Path</span><span class="p">]:</span>
</span><span id="ROMDownloader.download_file-214"><a href="#ROMDownloader.download_file-214"><span class="linenos">214</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;下载文件，支持断点续传&quot;&quot;&quot;</span>
</span><span id="ROMDownloader.download_file-215"><a href="#ROMDownloader.download_file-215"><span class="linenos">215</span></a>        <span class="n">file_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="n">filename</span>
</span><span id="ROMDownloader.download_file-216"><a href="#ROMDownloader.download_file-216"><span class="linenos">216</span></a>        
</span><span id="ROMDownloader.download_file-217"><a href="#ROMDownloader.download_file-217"><span class="linenos">217</span></a>        <span class="c1"># 检查是否已存在</span>
</span><span id="ROMDownloader.download_file-218"><a href="#ROMDownloader.download_file-218"><span class="linenos">218</span></a>        <span class="k">if</span> <span class="n">file_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="ROMDownloader.download_file-219"><a href="#ROMDownloader.download_file-219"><span class="linenos">219</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件已存在: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.download_file-220"><a href="#ROMDownloader.download_file-220"><span class="linenos">220</span></a>            <span class="k">return</span> <span class="n">file_path</span>
</span><span id="ROMDownloader.download_file-221"><a href="#ROMDownloader.download_file-221"><span class="linenos">221</span></a>        
</span><span id="ROMDownloader.download_file-222"><a href="#ROMDownloader.download_file-222"><span class="linenos">222</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader.download_file-223"><a href="#ROMDownloader.download_file-223"><span class="linenos">223</span></a>            <span class="c1"># 获取文件大小</span>
</span><span id="ROMDownloader.download_file-224"><a href="#ROMDownloader.download_file-224"><span class="linenos">224</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">head</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="ROMDownloader.download_file-225"><a href="#ROMDownloader.download_file-225"><span class="linenos">225</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="ROMDownloader.download_file-226"><a href="#ROMDownloader.download_file-226"><span class="linenos">226</span></a>            
</span><span id="ROMDownloader.download_file-227"><a href="#ROMDownloader.download_file-227"><span class="linenos">227</span></a>            <span class="n">total_size</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">headers</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;content-length&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
</span><span id="ROMDownloader.download_file-228"><a href="#ROMDownloader.download_file-228"><span class="linenos">228</span></a>            
</span><span id="ROMDownloader.download_file-229"><a href="#ROMDownloader.download_file-229"><span class="linenos">229</span></a>            <span class="c1"># 检查是否有部分下载的文件</span>
</span><span id="ROMDownloader.download_file-230"><a href="#ROMDownloader.download_file-230"><span class="linenos">230</span></a>            <span class="n">temp_path</span> <span class="o">=</span> <span class="n">file_path</span><span class="o">.</span><span class="n">with_suffix</span><span class="p">(</span><span class="s1">&#39;.tmp&#39;</span><span class="p">)</span>
</span><span id="ROMDownloader.download_file-231"><a href="#ROMDownloader.download_file-231"><span class="linenos">231</span></a>            <span class="n">resume_pos</span> <span class="o">=</span> <span class="mi">0</span>
</span><span id="ROMDownloader.download_file-232"><a href="#ROMDownloader.download_file-232"><span class="linenos">232</span></a>            
</span><span id="ROMDownloader.download_file-233"><a href="#ROMDownloader.download_file-233"><span class="linenos">233</span></a>            <span class="k">if</span> <span class="n">temp_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="ROMDownloader.download_file-234"><a href="#ROMDownloader.download_file-234"><span class="linenos">234</span></a>                <span class="n">resume_pos</span> <span class="o">=</span> <span class="n">temp_path</span><span class="o">.</span><span class="n">stat</span><span class="p">()</span><span class="o">.</span><span class="n">st_size</span>
</span><span id="ROMDownloader.download_file-235"><a href="#ROMDownloader.download_file-235"><span class="linenos">235</span></a>                <span class="k">if</span> <span class="n">resume_pos</span> <span class="o">&gt;=</span> <span class="n">total_size</span><span class="p">:</span>
</span><span id="ROMDownloader.download_file-236"><a href="#ROMDownloader.download_file-236"><span class="linenos">236</span></a>                    <span class="c1"># 文件已完整下载</span>
</span><span id="ROMDownloader.download_file-237"><a href="#ROMDownloader.download_file-237"><span class="linenos">237</span></a>                    <span class="n">temp_path</span><span class="o">.</span><span class="n">rename</span><span class="p">(</span><span class="n">file_path</span><span class="p">)</span>
</span><span id="ROMDownloader.download_file-238"><a href="#ROMDownloader.download_file-238"><span class="linenos">238</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件下载完成: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.download_file-239"><a href="#ROMDownloader.download_file-239"><span class="linenos">239</span></a>                    <span class="k">return</span> <span class="n">file_path</span>
</span><span id="ROMDownloader.download_file-240"><a href="#ROMDownloader.download_file-240"><span class="linenos">240</span></a>            
</span><span id="ROMDownloader.download_file-241"><a href="#ROMDownloader.download_file-241"><span class="linenos">241</span></a>            <span class="c1"># 设置断点续传头</span>
</span><span id="ROMDownloader.download_file-242"><a href="#ROMDownloader.download_file-242"><span class="linenos">242</span></a>            <span class="n">headers</span> <span class="o">=</span> <span class="p">{}</span>
</span><span id="ROMDownloader.download_file-243"><a href="#ROMDownloader.download_file-243"><span class="linenos">243</span></a>            <span class="k">if</span> <span class="n">resume_pos</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="ROMDownloader.download_file-244"><a href="#ROMDownloader.download_file-244"><span class="linenos">244</span></a>                <span class="n">headers</span><span class="p">[</span><span class="s1">&#39;Range&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="sa">f</span><span class="s1">&#39;bytes=</span><span class="si">{</span><span class="n">resume_pos</span><span class="si">}</span><span class="s1">-&#39;</span>
</span><span id="ROMDownloader.download_file-245"><a href="#ROMDownloader.download_file-245"><span class="linenos">245</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;断点续传: </span><span class="si">{</span><span class="n">resume_pos</span><span class="si">}</span><span class="s2"> bytes&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.download_file-246"><a href="#ROMDownloader.download_file-246"><span class="linenos">246</span></a>            
</span><span id="ROMDownloader.download_file-247"><a href="#ROMDownloader.download_file-247"><span class="linenos">247</span></a>            <span class="c1"># 开始下载</span>
</span><span id="ROMDownloader.download_file-248"><a href="#ROMDownloader.download_file-248"><span class="linenos">248</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">headers</span><span class="o">=</span><span class="n">headers</span><span class="p">,</span> <span class="n">stream</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="ROMDownloader.download_file-249"><a href="#ROMDownloader.download_file-249"><span class="linenos">249</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="ROMDownloader.download_file-250"><a href="#ROMDownloader.download_file-250"><span class="linenos">250</span></a>            
</span><span id="ROMDownloader.download_file-251"><a href="#ROMDownloader.download_file-251"><span class="linenos">251</span></a>            <span class="n">mode</span> <span class="o">=</span> <span class="s1">&#39;ab&#39;</span> <span class="k">if</span> <span class="n">resume_pos</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="k">else</span> <span class="s1">&#39;wb&#39;</span>
</span><span id="ROMDownloader.download_file-252"><a href="#ROMDownloader.download_file-252"><span class="linenos">252</span></a>            
</span><span id="ROMDownloader.download_file-253"><a href="#ROMDownloader.download_file-253"><span class="linenos">253</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">temp_path</span><span class="p">,</span> <span class="n">mode</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="ROMDownloader.download_file-254"><a href="#ROMDownloader.download_file-254"><span class="linenos">254</span></a>                <span class="k">with</span> <span class="n">tqdm</span><span class="p">(</span>
</span><span id="ROMDownloader.download_file-255"><a href="#ROMDownloader.download_file-255"><span class="linenos">255</span></a>                    <span class="n">total</span><span class="o">=</span><span class="n">total_size</span><span class="p">,</span>
</span><span id="ROMDownloader.download_file-256"><a href="#ROMDownloader.download_file-256"><span class="linenos">256</span></a>                    <span class="n">initial</span><span class="o">=</span><span class="n">resume_pos</span><span class="p">,</span>
</span><span id="ROMDownloader.download_file-257"><a href="#ROMDownloader.download_file-257"><span class="linenos">257</span></a>                    <span class="n">unit</span><span class="o">=</span><span class="s1">&#39;B&#39;</span><span class="p">,</span>
</span><span id="ROMDownloader.download_file-258"><a href="#ROMDownloader.download_file-258"><span class="linenos">258</span></a>                    <span class="n">unit_scale</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
</span><span id="ROMDownloader.download_file-259"><a href="#ROMDownloader.download_file-259"><span class="linenos">259</span></a>                    <span class="n">desc</span><span class="o">=</span><span class="n">filename</span>
</span><span id="ROMDownloader.download_file-260"><a href="#ROMDownloader.download_file-260"><span class="linenos">260</span></a>                <span class="p">)</span> <span class="k">as</span> <span class="n">pbar</span><span class="p">:</span>
</span><span id="ROMDownloader.download_file-261"><a href="#ROMDownloader.download_file-261"><span class="linenos">261</span></a>                    
</span><span id="ROMDownloader.download_file-262"><a href="#ROMDownloader.download_file-262"><span class="linenos">262</span></a>                    <span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="n">response</span><span class="o">.</span><span class="n">iter_content</span><span class="p">(</span><span class="n">chunk_size</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;download&quot;</span><span class="p">][</span><span class="s2">&quot;chunk_size&quot;</span><span class="p">]):</span>
</span><span id="ROMDownloader.download_file-263"><a href="#ROMDownloader.download_file-263"><span class="linenos">263</span></a>                        <span class="k">if</span> <span class="n">chunk</span><span class="p">:</span>
</span><span id="ROMDownloader.download_file-264"><a href="#ROMDownloader.download_file-264"><span class="linenos">264</span></a>                            <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</span><span id="ROMDownloader.download_file-265"><a href="#ROMDownloader.download_file-265"><span class="linenos">265</span></a>                            <span class="n">pbar</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">chunk</span><span class="p">))</span>
</span><span id="ROMDownloader.download_file-266"><a href="#ROMDownloader.download_file-266"><span class="linenos">266</span></a>            
</span><span id="ROMDownloader.download_file-267"><a href="#ROMDownloader.download_file-267"><span class="linenos">267</span></a>            <span class="c1"># 重命名临时文件</span>
</span><span id="ROMDownloader.download_file-268"><a href="#ROMDownloader.download_file-268"><span class="linenos">268</span></a>            <span class="n">temp_path</span><span class="o">.</span><span class="n">rename</span><span class="p">(</span><span class="n">file_path</span><span class="p">)</span>
</span><span id="ROMDownloader.download_file-269"><a href="#ROMDownloader.download_file-269"><span class="linenos">269</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载完成: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.download_file-270"><a href="#ROMDownloader.download_file-270"><span class="linenos">270</span></a>            <span class="k">return</span> <span class="n">file_path</span>
</span><span id="ROMDownloader.download_file-271"><a href="#ROMDownloader.download_file-271"><span class="linenos">271</span></a>            
</span><span id="ROMDownloader.download_file-272"><a href="#ROMDownloader.download_file-272"><span class="linenos">272</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader.download_file-273"><a href="#ROMDownloader.download_file-273"><span class="linenos">273</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.download_file-274"><a href="#ROMDownloader.download_file-274"><span class="linenos">274</span></a>            <span class="c1"># 清理临时文件</span>
</span><span id="ROMDownloader.download_file-275"><a href="#ROMDownloader.download_file-275"><span class="linenos">275</span></a>            <span class="k">if</span> <span class="n">temp_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="ROMDownloader.download_file-276"><a href="#ROMDownloader.download_file-276"><span class="linenos">276</span></a>                <span class="n">temp_path</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
</span><span id="ROMDownloader.download_file-277"><a href="#ROMDownloader.download_file-277"><span class="linenos">277</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span></pre></div>


            <div class="docstring"><p>下载文件，支持断点续传</p>
</div>


                            </div>
                            <div id="ROMDownloader.verify_file" class="classattr">
                                        <input id="ROMDownloader.verify_file-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">verify_file</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">file_path</span><span class="p">:</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span></span><span class="return-annotation">) -> <span class="nb">bool</span>:</span></span>

                <label class="view-source-button" for="ROMDownloader.verify_file-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#ROMDownloader.verify_file"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="ROMDownloader.verify_file-279"><a href="#ROMDownloader.verify_file-279"><span class="linenos">279</span></a>    <span class="k">def</span> <span class="nf">verify_file</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">file_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="ROMDownloader.verify_file-280"><a href="#ROMDownloader.verify_file-280"><span class="linenos">280</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;验证文件完整性&quot;&quot;&quot;</span>
</span><span id="ROMDownloader.verify_file-281"><a href="#ROMDownloader.verify_file-281"><span class="linenos">281</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;verification&quot;</span><span class="p">][</span><span class="s2">&quot;verify_checksum&quot;</span><span class="p">]:</span>
</span><span id="ROMDownloader.verify_file-282"><a href="#ROMDownloader.verify_file-282"><span class="linenos">282</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="ROMDownloader.verify_file-283"><a href="#ROMDownloader.verify_file-283"><span class="linenos">283</span></a>        
</span><span id="ROMDownloader.verify_file-284"><a href="#ROMDownloader.verify_file-284"><span class="linenos">284</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;验证文件完整性: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.verify_file-285"><a href="#ROMDownloader.verify_file-285"><span class="linenos">285</span></a>        
</span><span id="ROMDownloader.verify_file-286"><a href="#ROMDownloader.verify_file-286"><span class="linenos">286</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader.verify_file-287"><a href="#ROMDownloader.verify_file-287"><span class="linenos">287</span></a>            <span class="n">algorithm</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;verification&quot;</span><span class="p">][</span><span class="s2">&quot;checksum_algorithm&quot;</span><span class="p">]</span>
</span><span id="ROMDownloader.verify_file-288"><a href="#ROMDownloader.verify_file-288"><span class="linenos">288</span></a>            <span class="n">hash_func</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">hashlib</span><span class="p">,</span> <span class="n">algorithm</span><span class="p">)()</span>
</span><span id="ROMDownloader.verify_file-289"><a href="#ROMDownloader.verify_file-289"><span class="linenos">289</span></a>            
</span><span id="ROMDownloader.verify_file-290"><a href="#ROMDownloader.verify_file-290"><span class="linenos">290</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">file_path</span><span class="p">,</span> <span class="s1">&#39;rb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="ROMDownloader.verify_file-291"><a href="#ROMDownloader.verify_file-291"><span class="linenos">291</span></a>                <span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="nb">iter</span><span class="p">(</span><span class="k">lambda</span><span class="p">:</span> <span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="mi">8192</span><span class="p">),</span> <span class="sa">b</span><span class="s2">&quot;&quot;</span><span class="p">):</span>
</span><span id="ROMDownloader.verify_file-292"><a href="#ROMDownloader.verify_file-292"><span class="linenos">292</span></a>                    <span class="n">hash_func</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</span><span id="ROMDownloader.verify_file-293"><a href="#ROMDownloader.verify_file-293"><span class="linenos">293</span></a>            
</span><span id="ROMDownloader.verify_file-294"><a href="#ROMDownloader.verify_file-294"><span class="linenos">294</span></a>            <span class="n">checksum</span> <span class="o">=</span> <span class="n">hash_func</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
</span><span id="ROMDownloader.verify_file-295"><a href="#ROMDownloader.verify_file-295"><span class="linenos">295</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件校验和 (</span><span class="si">{</span><span class="n">algorithm</span><span class="si">}</span><span class="s2">): </span><span class="si">{</span><span class="n">checksum</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.verify_file-296"><a href="#ROMDownloader.verify_file-296"><span class="linenos">296</span></a>            
</span><span id="ROMDownloader.verify_file-297"><a href="#ROMDownloader.verify_file-297"><span class="linenos">297</span></a>            <span class="c1"># 这里可以添加预定义的校验和验证</span>
</span><span id="ROMDownloader.verify_file-298"><a href="#ROMDownloader.verify_file-298"><span class="linenos">298</span></a>            <span class="c1"># 由于ROM文件可能来自不同源，暂时只计算校验和</span>
</span><span id="ROMDownloader.verify_file-299"><a href="#ROMDownloader.verify_file-299"><span class="linenos">299</span></a>            
</span><span id="ROMDownloader.verify_file-300"><a href="#ROMDownloader.verify_file-300"><span class="linenos">300</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="ROMDownloader.verify_file-301"><a href="#ROMDownloader.verify_file-301"><span class="linenos">301</span></a>            
</span><span id="ROMDownloader.verify_file-302"><a href="#ROMDownloader.verify_file-302"><span class="linenos">302</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader.verify_file-303"><a href="#ROMDownloader.verify_file-303"><span class="linenos">303</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件验证失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.verify_file-304"><a href="#ROMDownloader.verify_file-304"><span class="linenos">304</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span></pre></div>


            <div class="docstring"><p>验证文件完整性</p>
</div>


                            </div>
                            <div id="ROMDownloader.extract_roms" class="classattr">
                                        <input id="ROMDownloader.extract_roms-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">extract_roms</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">zip_path</span><span class="p">:</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span></span><span class="return-annotation">) -> <span class="n">List</span><span class="p">[</span><span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">]</span>:</span></span>

                <label class="view-source-button" for="ROMDownloader.extract_roms-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#ROMDownloader.extract_roms"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="ROMDownloader.extract_roms-306"><a href="#ROMDownloader.extract_roms-306"><span class="linenos">306</span></a>    <span class="k">def</span> <span class="nf">extract_roms</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">zip_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Path</span><span class="p">]:</span>
</span><span id="ROMDownloader.extract_roms-307"><a href="#ROMDownloader.extract_roms-307"><span class="linenos">307</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;解压ROM文件&quot;&quot;&quot;</span>
</span><span id="ROMDownloader.extract_roms-308"><a href="#ROMDownloader.extract_roms-308"><span class="linenos">308</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压ROM文件: </span><span class="si">{</span><span class="n">zip_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.extract_roms-309"><a href="#ROMDownloader.extract_roms-309"><span class="linenos">309</span></a>        
</span><span id="ROMDownloader.extract_roms-310"><a href="#ROMDownloader.extract_roms-310"><span class="linenos">310</span></a>        <span class="n">rom_files</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="ROMDownloader.extract_roms-311"><a href="#ROMDownloader.extract_roms-311"><span class="linenos">311</span></a>        <span class="n">extract_dir</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="s2">&quot;extracted&quot;</span>
</span><span id="ROMDownloader.extract_roms-312"><a href="#ROMDownloader.extract_roms-312"><span class="linenos">312</span></a>        <span class="n">extract_dir</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="ROMDownloader.extract_roms-313"><a href="#ROMDownloader.extract_roms-313"><span class="linenos">313</span></a>        
</span><span id="ROMDownloader.extract_roms-314"><a href="#ROMDownloader.extract_roms-314"><span class="linenos">314</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader.extract_roms-315"><a href="#ROMDownloader.extract_roms-315"><span class="linenos">315</span></a>            <span class="k">with</span> <span class="n">zipfile</span><span class="o">.</span><span class="n">ZipFile</span><span class="p">(</span><span class="n">zip_path</span><span class="p">,</span> <span class="s1">&#39;r&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">zip_ref</span><span class="p">:</span>
</span><span id="ROMDownloader.extract_roms-316"><a href="#ROMDownloader.extract_roms-316"><span class="linenos">316</span></a>                <span class="c1"># 列出所有文件</span>
</span><span id="ROMDownloader.extract_roms-317"><a href="#ROMDownloader.extract_roms-317"><span class="linenos">317</span></a>                <span class="n">file_list</span> <span class="o">=</span> <span class="n">zip_ref</span><span class="o">.</span><span class="n">namelist</span><span class="p">()</span>
</span><span id="ROMDownloader.extract_roms-318"><a href="#ROMDownloader.extract_roms-318"><span class="linenos">318</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;ZIP文件包含 </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">file_list</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个文件&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.extract_roms-319"><a href="#ROMDownloader.extract_roms-319"><span class="linenos">319</span></a>                
</span><span id="ROMDownloader.extract_roms-320"><a href="#ROMDownloader.extract_roms-320"><span class="linenos">320</span></a>                <span class="c1"># 过滤ROM文件</span>
</span><span id="ROMDownloader.extract_roms-321"><a href="#ROMDownloader.extract_roms-321"><span class="linenos">321</span></a>                <span class="n">rom_extensions</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;.nes&#39;</span><span class="p">,</span> <span class="s1">&#39;.NES&#39;</span><span class="p">]</span>
</span><span id="ROMDownloader.extract_roms-322"><a href="#ROMDownloader.extract_roms-322"><span class="linenos">322</span></a>                <span class="n">rom_files_in_zip</span> <span class="o">=</span> <span class="p">[</span><span class="n">f</span> <span class="k">for</span> <span class="n">f</span> <span class="ow">in</span> <span class="n">file_list</span> <span class="k">if</span> <span class="nb">any</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">endswith</span><span class="p">(</span><span class="n">ext</span><span class="p">)</span> <span class="k">for</span> <span class="n">ext</span> <span class="ow">in</span> <span class="n">rom_extensions</span><span class="p">)]</span>
</span><span id="ROMDownloader.extract_roms-323"><a href="#ROMDownloader.extract_roms-323"><span class="linenos">323</span></a>                
</span><span id="ROMDownloader.extract_roms-324"><a href="#ROMDownloader.extract_roms-324"><span class="linenos">324</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到 </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">rom_files_in_zip</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个ROM文件&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.extract_roms-325"><a href="#ROMDownloader.extract_roms-325"><span class="linenos">325</span></a>                
</span><span id="ROMDownloader.extract_roms-326"><a href="#ROMDownloader.extract_roms-326"><span class="linenos">326</span></a>                <span class="c1"># 解压ROM文件</span>
</span><span id="ROMDownloader.extract_roms-327"><a href="#ROMDownloader.extract_roms-327"><span class="linenos">327</span></a>                <span class="k">for</span> <span class="n">rom_file</span> <span class="ow">in</span> <span class="n">rom_files_in_zip</span><span class="p">:</span>
</span><span id="ROMDownloader.extract_roms-328"><a href="#ROMDownloader.extract_roms-328"><span class="linenos">328</span></a>                    <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader.extract_roms-329"><a href="#ROMDownloader.extract_roms-329"><span class="linenos">329</span></a>                        <span class="n">zip_ref</span><span class="o">.</span><span class="n">extract</span><span class="p">(</span><span class="n">rom_file</span><span class="p">,</span> <span class="n">extract_dir</span><span class="p">)</span>
</span><span id="ROMDownloader.extract_roms-330"><a href="#ROMDownloader.extract_roms-330"><span class="linenos">330</span></a>                        <span class="n">rom_path</span> <span class="o">=</span> <span class="n">extract_dir</span> <span class="o">/</span> <span class="n">rom_file</span>
</span><span id="ROMDownloader.extract_roms-331"><a href="#ROMDownloader.extract_roms-331"><span class="linenos">331</span></a>                        <span class="n">rom_files</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">rom_path</span><span class="p">)</span>
</span><span id="ROMDownloader.extract_roms-332"><a href="#ROMDownloader.extract_roms-332"><span class="linenos">332</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压: </span><span class="si">{</span><span class="n">rom_file</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.extract_roms-333"><a href="#ROMDownloader.extract_roms-333"><span class="linenos">333</span></a>                    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader.extract_roms-334"><a href="#ROMDownloader.extract_roms-334"><span class="linenos">334</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压文件失败 </span><span class="si">{</span><span class="n">rom_file</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.extract_roms-335"><a href="#ROMDownloader.extract_roms-335"><span class="linenos">335</span></a>            
</span><span id="ROMDownloader.extract_roms-336"><a href="#ROMDownloader.extract_roms-336"><span class="linenos">336</span></a>            <span class="k">return</span> <span class="n">rom_files</span>
</span><span id="ROMDownloader.extract_roms-337"><a href="#ROMDownloader.extract_roms-337"><span class="linenos">337</span></a>            
</span><span id="ROMDownloader.extract_roms-338"><a href="#ROMDownloader.extract_roms-338"><span class="linenos">338</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader.extract_roms-339"><a href="#ROMDownloader.extract_roms-339"><span class="linenos">339</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.extract_roms-340"><a href="#ROMDownloader.extract_roms-340"><span class="linenos">340</span></a>            <span class="k">return</span> <span class="p">[]</span>
</span></pre></div>


            <div class="docstring"><p>解压ROM文件</p>
</div>


                            </div>
                            <div id="ROMDownloader.connect_sftp" class="classattr">
                                        <input id="ROMDownloader.connect_sftp-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">connect_sftp</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">) -> <span class="n">Optional</span><span class="p">[</span><span class="n">paramiko</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">SSHClient</span><span class="p">]</span>:</span></span>

                <label class="view-source-button" for="ROMDownloader.connect_sftp-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#ROMDownloader.connect_sftp"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="ROMDownloader.connect_sftp-342"><a href="#ROMDownloader.connect_sftp-342"><span class="linenos">342</span></a>    <span class="k">def</span> <span class="nf">connect_sftp</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">paramiko</span><span class="o">.</span><span class="n">SSHClient</span><span class="p">]:</span>
</span><span id="ROMDownloader.connect_sftp-343"><a href="#ROMDownloader.connect_sftp-343"><span class="linenos">343</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;连接SFTP服务器&quot;&quot;&quot;</span>
</span><span id="ROMDownloader.connect_sftp-344"><a href="#ROMDownloader.connect_sftp-344"><span class="linenos">344</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;连接树莓派SFTP服务器...&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.connect_sftp-345"><a href="#ROMDownloader.connect_sftp-345"><span class="linenos">345</span></a>        
</span><span id="ROMDownloader.connect_sftp-346"><a href="#ROMDownloader.connect_sftp-346"><span class="linenos">346</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader.connect_sftp-347"><a href="#ROMDownloader.connect_sftp-347"><span class="linenos">347</span></a>            <span class="n">ssh</span> <span class="o">=</span> <span class="n">paramiko</span><span class="o">.</span><span class="n">SSHClient</span><span class="p">()</span>
</span><span id="ROMDownloader.connect_sftp-348"><a href="#ROMDownloader.connect_sftp-348"><span class="linenos">348</span></a>            <span class="n">ssh</span><span class="o">.</span><span class="n">set_missing_host_key_policy</span><span class="p">(</span><span class="n">paramiko</span><span class="o">.</span><span class="n">AutoAddPolicy</span><span class="p">())</span>
</span><span id="ROMDownloader.connect_sftp-349"><a href="#ROMDownloader.connect_sftp-349"><span class="linenos">349</span></a>            
</span><span id="ROMDownloader.connect_sftp-350"><a href="#ROMDownloader.connect_sftp-350"><span class="linenos">350</span></a>            <span class="n">pi_config</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;raspberry_pi&quot;</span><span class="p">]</span>
</span><span id="ROMDownloader.connect_sftp-351"><a href="#ROMDownloader.connect_sftp-351"><span class="linenos">351</span></a>            
</span><span id="ROMDownloader.connect_sftp-352"><a href="#ROMDownloader.connect_sftp-352"><span class="linenos">352</span></a>            <span class="c1"># 使用密钥文件或密码</span>
</span><span id="ROMDownloader.connect_sftp-353"><a href="#ROMDownloader.connect_sftp-353"><span class="linenos">353</span></a>            <span class="k">if</span> <span class="n">pi_config</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;key_file&quot;</span><span class="p">)</span> <span class="ow">and</span> <span class="n">Path</span><span class="p">(</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;key_file&quot;</span><span class="p">])</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="ROMDownloader.connect_sftp-354"><a href="#ROMDownloader.connect_sftp-354"><span class="linenos">354</span></a>                <span class="n">ssh</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span>
</span><span id="ROMDownloader.connect_sftp-355"><a href="#ROMDownloader.connect_sftp-355"><span class="linenos">355</span></a>                    <span class="n">hostname</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;host&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader.connect_sftp-356"><a href="#ROMDownloader.connect_sftp-356"><span class="linenos">356</span></a>                    <span class="n">port</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;port&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader.connect_sftp-357"><a href="#ROMDownloader.connect_sftp-357"><span class="linenos">357</span></a>                    <span class="n">username</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;username&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader.connect_sftp-358"><a href="#ROMDownloader.connect_sftp-358"><span class="linenos">358</span></a>                    <span class="n">key_filename</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;key_file&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader.connect_sftp-359"><a href="#ROMDownloader.connect_sftp-359"><span class="linenos">359</span></a>                    <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span>
</span><span id="ROMDownloader.connect_sftp-360"><a href="#ROMDownloader.connect_sftp-360"><span class="linenos">360</span></a>                <span class="p">)</span>
</span><span id="ROMDownloader.connect_sftp-361"><a href="#ROMDownloader.connect_sftp-361"><span class="linenos">361</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="ROMDownloader.connect_sftp-362"><a href="#ROMDownloader.connect_sftp-362"><span class="linenos">362</span></a>                <span class="n">ssh</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span>
</span><span id="ROMDownloader.connect_sftp-363"><a href="#ROMDownloader.connect_sftp-363"><span class="linenos">363</span></a>                    <span class="n">hostname</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;host&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader.connect_sftp-364"><a href="#ROMDownloader.connect_sftp-364"><span class="linenos">364</span></a>                    <span class="n">port</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;port&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader.connect_sftp-365"><a href="#ROMDownloader.connect_sftp-365"><span class="linenos">365</span></a>                    <span class="n">username</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;username&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader.connect_sftp-366"><a href="#ROMDownloader.connect_sftp-366"><span class="linenos">366</span></a>                    <span class="n">password</span><span class="o">=</span><span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;password&quot;</span><span class="p">],</span>
</span><span id="ROMDownloader.connect_sftp-367"><a href="#ROMDownloader.connect_sftp-367"><span class="linenos">367</span></a>                    <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span>
</span><span id="ROMDownloader.connect_sftp-368"><a href="#ROMDownloader.connect_sftp-368"><span class="linenos">368</span></a>                <span class="p">)</span>
</span><span id="ROMDownloader.connect_sftp-369"><a href="#ROMDownloader.connect_sftp-369"><span class="linenos">369</span></a>            
</span><span id="ROMDownloader.connect_sftp-370"><a href="#ROMDownloader.connect_sftp-370"><span class="linenos">370</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;SFTP连接成功&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.connect_sftp-371"><a href="#ROMDownloader.connect_sftp-371"><span class="linenos">371</span></a>            <span class="k">return</span> <span class="n">ssh</span>
</span><span id="ROMDownloader.connect_sftp-372"><a href="#ROMDownloader.connect_sftp-372"><span class="linenos">372</span></a>            
</span><span id="ROMDownloader.connect_sftp-373"><a href="#ROMDownloader.connect_sftp-373"><span class="linenos">373</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader.connect_sftp-374"><a href="#ROMDownloader.connect_sftp-374"><span class="linenos">374</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;SFTP连接失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.connect_sftp-375"><a href="#ROMDownloader.connect_sftp-375"><span class="linenos">375</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span></pre></div>


            <div class="docstring"><p>连接SFTP服务器</p>
</div>


                            </div>
                            <div id="ROMDownloader.upload_roms" class="classattr">
                                        <input id="ROMDownloader.upload_roms-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">upload_roms</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">rom_files</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">]</span></span><span class="return-annotation">) -> <span class="nb">bool</span>:</span></span>

                <label class="view-source-button" for="ROMDownloader.upload_roms-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#ROMDownloader.upload_roms"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="ROMDownloader.upload_roms-377"><a href="#ROMDownloader.upload_roms-377"><span class="linenos">377</span></a>    <span class="k">def</span> <span class="nf">upload_roms</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">rom_files</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">Path</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-378"><a href="#ROMDownloader.upload_roms-378"><span class="linenos">378</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;上传ROM文件到树莓派&quot;&quot;&quot;</span>
</span><span id="ROMDownloader.upload_roms-379"><a href="#ROMDownloader.upload_roms-379"><span class="linenos">379</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">rom_files</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-380"><a href="#ROMDownloader.upload_roms-380"><span class="linenos">380</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;没有ROM文件需要上传&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.upload_roms-381"><a href="#ROMDownloader.upload_roms-381"><span class="linenos">381</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader.upload_roms-382"><a href="#ROMDownloader.upload_roms-382"><span class="linenos">382</span></a>        
</span><span id="ROMDownloader.upload_roms-383"><a href="#ROMDownloader.upload_roms-383"><span class="linenos">383</span></a>        <span class="n">ssh</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">connect_sftp</span><span class="p">()</span>
</span><span id="ROMDownloader.upload_roms-384"><a href="#ROMDownloader.upload_roms-384"><span class="linenos">384</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">ssh</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-385"><a href="#ROMDownloader.upload_roms-385"><span class="linenos">385</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader.upload_roms-386"><a href="#ROMDownloader.upload_roms-386"><span class="linenos">386</span></a>        
</span><span id="ROMDownloader.upload_roms-387"><a href="#ROMDownloader.upload_roms-387"><span class="linenos">387</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-388"><a href="#ROMDownloader.upload_roms-388"><span class="linenos">388</span></a>            <span class="n">sftp</span> <span class="o">=</span> <span class="n">ssh</span><span class="o">.</span><span class="n">open_sftp</span><span class="p">()</span>
</span><span id="ROMDownloader.upload_roms-389"><a href="#ROMDownloader.upload_roms-389"><span class="linenos">389</span></a>            <span class="n">pi_config</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;raspberry_pi&quot;</span><span class="p">]</span>
</span><span id="ROMDownloader.upload_roms-390"><a href="#ROMDownloader.upload_roms-390"><span class="linenos">390</span></a>            <span class="n">remote_path</span> <span class="o">=</span> <span class="n">pi_config</span><span class="p">[</span><span class="s2">&quot;roms_path&quot;</span><span class="p">]</span>
</span><span id="ROMDownloader.upload_roms-391"><a href="#ROMDownloader.upload_roms-391"><span class="linenos">391</span></a>            
</span><span id="ROMDownloader.upload_roms-392"><a href="#ROMDownloader.upload_roms-392"><span class="linenos">392</span></a>            <span class="c1"># 确保远程目录存在</span>
</span><span id="ROMDownloader.upload_roms-393"><a href="#ROMDownloader.upload_roms-393"><span class="linenos">393</span></a>            <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-394"><a href="#ROMDownloader.upload_roms-394"><span class="linenos">394</span></a>                <span class="n">sftp</span><span class="o">.</span><span class="n">stat</span><span class="p">(</span><span class="n">remote_path</span><span class="p">)</span>
</span><span id="ROMDownloader.upload_roms-395"><a href="#ROMDownloader.upload_roms-395"><span class="linenos">395</span></a>            <span class="k">except</span> <span class="ne">FileNotFoundError</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-396"><a href="#ROMDownloader.upload_roms-396"><span class="linenos">396</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;创建远程目录: </span><span class="si">{</span><span class="n">remote_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.upload_roms-397"><a href="#ROMDownloader.upload_roms-397"><span class="linenos">397</span></a>                <span class="n">sftp</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">remote_path</span><span class="p">)</span>
</span><span id="ROMDownloader.upload_roms-398"><a href="#ROMDownloader.upload_roms-398"><span class="linenos">398</span></a>            
</span><span id="ROMDownloader.upload_roms-399"><a href="#ROMDownloader.upload_roms-399"><span class="linenos">399</span></a>            <span class="n">uploaded_count</span> <span class="o">=</span> <span class="mi">0</span>
</span><span id="ROMDownloader.upload_roms-400"><a href="#ROMDownloader.upload_roms-400"><span class="linenos">400</span></a>            
</span><span id="ROMDownloader.upload_roms-401"><a href="#ROMDownloader.upload_roms-401"><span class="linenos">401</span></a>            <span class="k">for</span> <span class="n">rom_file</span> <span class="ow">in</span> <span class="n">rom_files</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-402"><a href="#ROMDownloader.upload_roms-402"><span class="linenos">402</span></a>                <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-403"><a href="#ROMDownloader.upload_roms-403"><span class="linenos">403</span></a>                    <span class="n">remote_file</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">remote_path</span><span class="si">}</span><span class="s2">/</span><span class="si">{</span><span class="n">rom_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="ROMDownloader.upload_roms-404"><a href="#ROMDownloader.upload_roms-404"><span class="linenos">404</span></a>                    
</span><span id="ROMDownloader.upload_roms-405"><a href="#ROMDownloader.upload_roms-405"><span class="linenos">405</span></a>                    <span class="c1"># 检查文件是否已存在</span>
</span><span id="ROMDownloader.upload_roms-406"><a href="#ROMDownloader.upload_roms-406"><span class="linenos">406</span></a>                    <span class="k">try</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-407"><a href="#ROMDownloader.upload_roms-407"><span class="linenos">407</span></a>                        <span class="n">remote_stat</span> <span class="o">=</span> <span class="n">sftp</span><span class="o">.</span><span class="n">stat</span><span class="p">(</span><span class="n">remote_file</span><span class="p">)</span>
</span><span id="ROMDownloader.upload_roms-408"><a href="#ROMDownloader.upload_roms-408"><span class="linenos">408</span></a>                        <span class="n">local_stat</span> <span class="o">=</span> <span class="n">rom_file</span><span class="o">.</span><span class="n">stat</span><span class="p">()</span>
</span><span id="ROMDownloader.upload_roms-409"><a href="#ROMDownloader.upload_roms-409"><span class="linenos">409</span></a>                        
</span><span id="ROMDownloader.upload_roms-410"><a href="#ROMDownloader.upload_roms-410"><span class="linenos">410</span></a>                        <span class="k">if</span> <span class="n">remote_stat</span><span class="o">.</span><span class="n">st_size</span> <span class="o">==</span> <span class="n">local_stat</span><span class="o">.</span><span class="n">st_size</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-411"><a href="#ROMDownloader.upload_roms-411"><span class="linenos">411</span></a>                            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;文件已存在，跳过: </span><span class="si">{</span><span class="n">rom_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.upload_roms-412"><a href="#ROMDownloader.upload_roms-412"><span class="linenos">412</span></a>                            <span class="n">uploaded_count</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="ROMDownloader.upload_roms-413"><a href="#ROMDownloader.upload_roms-413"><span class="linenos">413</span></a>                            <span class="k">continue</span>
</span><span id="ROMDownloader.upload_roms-414"><a href="#ROMDownloader.upload_roms-414"><span class="linenos">414</span></a>                    <span class="k">except</span> <span class="ne">FileNotFoundError</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-415"><a href="#ROMDownloader.upload_roms-415"><span class="linenos">415</span></a>                        <span class="k">pass</span>
</span><span id="ROMDownloader.upload_roms-416"><a href="#ROMDownloader.upload_roms-416"><span class="linenos">416</span></a>                    
</span><span id="ROMDownloader.upload_roms-417"><a href="#ROMDownloader.upload_roms-417"><span class="linenos">417</span></a>                    <span class="c1"># 上传文件</span>
</span><span id="ROMDownloader.upload_roms-418"><a href="#ROMDownloader.upload_roms-418"><span class="linenos">418</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;上传: </span><span class="si">{</span><span class="n">rom_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.upload_roms-419"><a href="#ROMDownloader.upload_roms-419"><span class="linenos">419</span></a>                    <span class="n">sftp</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">rom_file</span><span class="p">),</span> <span class="n">remote_file</span><span class="p">)</span>
</span><span id="ROMDownloader.upload_roms-420"><a href="#ROMDownloader.upload_roms-420"><span class="linenos">420</span></a>                    <span class="n">uploaded_count</span> <span class="o">+=</span> <span class="mi">1</span>
</span><span id="ROMDownloader.upload_roms-421"><a href="#ROMDownloader.upload_roms-421"><span class="linenos">421</span></a>                    
</span><span id="ROMDownloader.upload_roms-422"><a href="#ROMDownloader.upload_roms-422"><span class="linenos">422</span></a>                <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-423"><a href="#ROMDownloader.upload_roms-423"><span class="linenos">423</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;上传文件失败 </span><span class="si">{</span><span class="n">rom_file</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.upload_roms-424"><a href="#ROMDownloader.upload_roms-424"><span class="linenos">424</span></a>            
</span><span id="ROMDownloader.upload_roms-425"><a href="#ROMDownloader.upload_roms-425"><span class="linenos">425</span></a>            <span class="n">sftp</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</span><span id="ROMDownloader.upload_roms-426"><a href="#ROMDownloader.upload_roms-426"><span class="linenos">426</span></a>            <span class="n">ssh</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</span><span id="ROMDownloader.upload_roms-427"><a href="#ROMDownloader.upload_roms-427"><span class="linenos">427</span></a>            
</span><span id="ROMDownloader.upload_roms-428"><a href="#ROMDownloader.upload_roms-428"><span class="linenos">428</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;上传完成: </span><span class="si">{</span><span class="n">uploaded_count</span><span class="si">}</span><span class="s2">/</span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">rom_files</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个文件&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.upload_roms-429"><a href="#ROMDownloader.upload_roms-429"><span class="linenos">429</span></a>            <span class="k">return</span> <span class="n">uploaded_count</span> <span class="o">&gt;</span> <span class="mi">0</span>
</span><span id="ROMDownloader.upload_roms-430"><a href="#ROMDownloader.upload_roms-430"><span class="linenos">430</span></a>            
</span><span id="ROMDownloader.upload_roms-431"><a href="#ROMDownloader.upload_roms-431"><span class="linenos">431</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="ROMDownloader.upload_roms-432"><a href="#ROMDownloader.upload_roms-432"><span class="linenos">432</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;上传过程出错: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.upload_roms-433"><a href="#ROMDownloader.upload_roms-433"><span class="linenos">433</span></a>            <span class="n">ssh</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</span><span id="ROMDownloader.upload_roms-434"><a href="#ROMDownloader.upload_roms-434"><span class="linenos">434</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span></pre></div>


            <div class="docstring"><p>上传ROM文件到树莓派</p>
</div>


                            </div>
                            <div id="ROMDownloader.run" class="classattr">
                                        <input id="ROMDownloader.run-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">run</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">search_query</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s1">&#39;nes 100 in 1&#39;</span></span><span class="return-annotation">):</span></span>

                <label class="view-source-button" for="ROMDownloader.run-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#ROMDownloader.run"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="ROMDownloader.run-436"><a href="#ROMDownloader.run-436"><span class="linenos">436</span></a>    <span class="k">def</span> <span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">search_query</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;nes 100 in 1&quot;</span><span class="p">):</span>
</span><span id="ROMDownloader.run-437"><a href="#ROMDownloader.run-437"><span class="linenos">437</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;运行完整的下载和传输流程&quot;&quot;&quot;</span>
</span><span id="ROMDownloader.run-438"><a href="#ROMDownloader.run-438"><span class="linenos">438</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;=== NES ROM 下载和传输工具 ===&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-439"><a href="#ROMDownloader.run-439"><span class="linenos">439</span></a>
</span><span id="ROMDownloader.run-440"><a href="#ROMDownloader.run-440"><span class="linenos">440</span></a>        <span class="c1"># 1. 优先尝试配置文件中的下载地址</span>
</span><span id="ROMDownloader.run-441"><a href="#ROMDownloader.run-441"><span class="linenos">441</span></a>        <span class="n">nes_zip_urls</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">config</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;nes_zip_urls&quot;</span><span class="p">,</span> <span class="p">[])</span>
</span><span id="ROMDownloader.run-442"><a href="#ROMDownloader.run-442"><span class="linenos">442</span></a>        <span class="n">zip_path</span> <span class="o">=</span> <span class="kc">None</span>
</span><span id="ROMDownloader.run-443"><a href="#ROMDownloader.run-443"><span class="linenos">443</span></a>        <span class="n">download_url</span> <span class="o">=</span> <span class="kc">None</span>
</span><span id="ROMDownloader.run-444"><a href="#ROMDownloader.run-444"><span class="linenos">444</span></a>        <span class="k">if</span> <span class="n">nes_zip_urls</span><span class="p">:</span>
</span><span id="ROMDownloader.run-445"><a href="#ROMDownloader.run-445"><span class="linenos">445</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;配置文件中找到 </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">nes_zip_urls</span><span class="p">)</span><span class="si">}</span><span class="s2"> 个NES ZIP下载地址，优先尝试...&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-446"><a href="#ROMDownloader.run-446"><span class="linenos">446</span></a>            <span class="k">for</span> <span class="n">url</span> <span class="ow">in</span> <span class="n">nes_zip_urls</span><span class="p">:</span>
</span><span id="ROMDownloader.run-447"><a href="#ROMDownloader.run-447"><span class="linenos">447</span></a>                <span class="n">filename</span> <span class="o">=</span> <span class="n">url</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;/&#39;</span><span class="p">)[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
</span><span id="ROMDownloader.run-448"><a href="#ROMDownloader.run-448"><span class="linenos">448</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;尝试下载: </span><span class="si">{</span><span class="n">url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-449"><a href="#ROMDownloader.run-449"><span class="linenos">449</span></a>                <span class="n">zip_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_file</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">filename</span><span class="p">)</span>
</span><span id="ROMDownloader.run-450"><a href="#ROMDownloader.run-450"><span class="linenos">450</span></a>                <span class="k">if</span> <span class="n">zip_path</span><span class="p">:</span>
</span><span id="ROMDownloader.run-451"><a href="#ROMDownloader.run-451"><span class="linenos">451</span></a>                    <span class="n">download_url</span> <span class="o">=</span> <span class="n">url</span>
</span><span id="ROMDownloader.run-452"><a href="#ROMDownloader.run-452"><span class="linenos">452</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;成功下载: </span><span class="si">{</span><span class="n">url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-453"><a href="#ROMDownloader.run-453"><span class="linenos">453</span></a>                    <span class="k">break</span>
</span><span id="ROMDownloader.run-454"><a href="#ROMDownloader.run-454"><span class="linenos">454</span></a>                <span class="k">else</span><span class="p">:</span>
</span><span id="ROMDownloader.run-455"><a href="#ROMDownloader.run-455"><span class="linenos">455</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载失败: </span><span class="si">{</span><span class="n">url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-456"><a href="#ROMDownloader.run-456"><span class="linenos">456</span></a>        
</span><span id="ROMDownloader.run-457"><a href="#ROMDownloader.run-457"><span class="linenos">457</span></a>        <span class="c1"># 2. 如果配置地址全部失败，则自动搜索</span>
</span><span id="ROMDownloader.run-458"><a href="#ROMDownloader.run-458"><span class="linenos">458</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">zip_path</span><span class="p">:</span>
</span><span id="ROMDownloader.run-459"><a href="#ROMDownloader.run-459"><span class="linenos">459</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;配置地址全部失败，自动搜索可用ROM...&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-460"><a href="#ROMDownloader.run-460"><span class="linenos">460</span></a>            <span class="n">results</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">search_roms</span><span class="p">(</span><span class="n">search_query</span><span class="p">)</span>
</span><span id="ROMDownloader.run-461"><a href="#ROMDownloader.run-461"><span class="linenos">461</span></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">results</span><span class="p">:</span>
</span><span id="ROMDownloader.run-462"><a href="#ROMDownloader.run-462"><span class="linenos">462</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;未找到ROM文件&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-463"><a href="#ROMDownloader.run-463"><span class="linenos">463</span></a>                <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader.run-464"><a href="#ROMDownloader.run-464"><span class="linenos">464</span></a>            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">搜索结果:&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-465"><a href="#ROMDownloader.run-465"><span class="linenos">465</span></a>            <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">result</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">results</span><span class="p">[:</span><span class="mi">5</span><span class="p">]):</span>
</span><span id="ROMDownloader.run-466"><a href="#ROMDownloader.run-466"><span class="linenos">466</span></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">. </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;title&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2"> (下载次数: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;downloads&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">)&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-467"><a href="#ROMDownloader.run-467"><span class="linenos">467</span></a>            <span class="n">selected_result</span> <span class="o">=</span> <span class="n">results</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
</span><span id="ROMDownloader.run-468"><a href="#ROMDownloader.run-468"><span class="linenos">468</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;选择: </span><span class="si">{</span><span class="n">selected_result</span><span class="p">[</span><span class="s1">&#39;title&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-469"><a href="#ROMDownloader.run-469"><span class="linenos">469</span></a>            <span class="n">download_url</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_download_url</span><span class="p">(</span><span class="n">selected_result</span><span class="p">[</span><span class="s1">&#39;identifier&#39;</span><span class="p">])</span>
</span><span id="ROMDownloader.run-470"><a href="#ROMDownloader.run-470"><span class="linenos">470</span></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">download_url</span><span class="p">:</span>
</span><span id="ROMDownloader.run-471"><a href="#ROMDownloader.run-471"><span class="linenos">471</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;无法获取下载链接&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-472"><a href="#ROMDownloader.run-472"><span class="linenos">472</span></a>                <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader.run-473"><a href="#ROMDownloader.run-473"><span class="linenos">473</span></a>            <span class="n">filename</span> <span class="o">=</span> <span class="n">download_url</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;/&#39;</span><span class="p">)[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
</span><span id="ROMDownloader.run-474"><a href="#ROMDownloader.run-474"><span class="linenos">474</span></a>            <span class="n">zip_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_file</span><span class="p">(</span><span class="n">download_url</span><span class="p">,</span> <span class="n">filename</span><span class="p">)</span>
</span><span id="ROMDownloader.run-475"><a href="#ROMDownloader.run-475"><span class="linenos">475</span></a>            <span class="k">if</span> <span class="ow">not</span> <span class="n">zip_path</span><span class="p">:</span>
</span><span id="ROMDownloader.run-476"><a href="#ROMDownloader.run-476"><span class="linenos">476</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;下载失败&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-477"><a href="#ROMDownloader.run-477"><span class="linenos">477</span></a>                <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader.run-478"><a href="#ROMDownloader.run-478"><span class="linenos">478</span></a>
</span><span id="ROMDownloader.run-479"><a href="#ROMDownloader.run-479"><span class="linenos">479</span></a>        <span class="c1"># 3. 验证文件</span>
</span><span id="ROMDownloader.run-480"><a href="#ROMDownloader.run-480"><span class="linenos">480</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">verify_file</span><span class="p">(</span><span class="n">zip_path</span><span class="p">):</span>
</span><span id="ROMDownloader.run-481"><a href="#ROMDownloader.run-481"><span class="linenos">481</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;文件验证失败&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-482"><a href="#ROMDownloader.run-482"><span class="linenos">482</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader.run-483"><a href="#ROMDownloader.run-483"><span class="linenos">483</span></a>
</span><span id="ROMDownloader.run-484"><a href="#ROMDownloader.run-484"><span class="linenos">484</span></a>        <span class="c1"># 4. 解压ROM文件</span>
</span><span id="ROMDownloader.run-485"><a href="#ROMDownloader.run-485"><span class="linenos">485</span></a>        <span class="n">rom_files</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">extract_roms</span><span class="p">(</span><span class="n">zip_path</span><span class="p">)</span>
</span><span id="ROMDownloader.run-486"><a href="#ROMDownloader.run-486"><span class="linenos">486</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">rom_files</span><span class="p">:</span>
</span><span id="ROMDownloader.run-487"><a href="#ROMDownloader.run-487"><span class="linenos">487</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;解压失败或未找到ROM文件&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-488"><a href="#ROMDownloader.run-488"><span class="linenos">488</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="ROMDownloader.run-489"><a href="#ROMDownloader.run-489"><span class="linenos">489</span></a>
</span><span id="ROMDownloader.run-490"><a href="#ROMDownloader.run-490"><span class="linenos">490</span></a>        <span class="c1"># 5. 上传到树莓派</span>
</span><span id="ROMDownloader.run-491"><a href="#ROMDownloader.run-491"><span class="linenos">491</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">upload_roms</span><span class="p">(</span><span class="n">rom_files</span><span class="p">):</span>
</span><span id="ROMDownloader.run-492"><a href="#ROMDownloader.run-492"><span class="linenos">492</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;ROM传输完成！&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-493"><a href="#ROMDownloader.run-493"><span class="linenos">493</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="ROMDownloader.run-494"><a href="#ROMDownloader.run-494"><span class="linenos">494</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="ROMDownloader.run-495"><a href="#ROMDownloader.run-495"><span class="linenos">495</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;ROM传输失败&quot;</span><span class="p">)</span>
</span><span id="ROMDownloader.run-496"><a href="#ROMDownloader.run-496"><span class="linenos">496</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span></pre></div>


            <div class="docstring"><p>运行完整的下载和传输流程</p>
</div>


                            </div>
                </section>
                <section id="main">
                            <input id="main-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">main</span><span class="signature pdoc-code condensed">(<span class="return-annotation">):</span></span>

                <label class="view-source-button" for="main-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#main"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="main-498"><a href="#main-498"><span class="linenos">498</span></a><span class="k">def</span> <span class="nf">main</span><span class="p">():</span>
</span><span id="main-499"><a href="#main-499"><span class="linenos">499</span></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;主函数&quot;&quot;&quot;</span>
</span><span id="main-500"><a href="#main-500"><span class="linenos">500</span></a>    <span class="n">parser</span> <span class="o">=</span> <span class="n">argparse</span><span class="o">.</span><span class="n">ArgumentParser</span><span class="p">(</span><span class="n">description</span><span class="o">=</span><span class="s2">&quot;NES ROM 下载和传输工具&quot;</span><span class="p">)</span>
</span><span id="main-501"><a href="#main-501"><span class="linenos">501</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--config&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="s2">&quot;rom_config.json&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;配置文件路径&quot;</span><span class="p">)</span>
</span><span id="main-502"><a href="#main-502"><span class="linenos">502</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--search&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="s2">&quot;nes 100 in 1&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;搜索关键词&quot;</span><span class="p">)</span>
</span><span id="main-503"><a href="#main-503"><span class="linenos">503</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--download-only&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;仅下载，不上传&quot;</span><span class="p">)</span>
</span><span id="main-504"><a href="#main-504"><span class="linenos">504</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--upload-only&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;仅上传已下载的文件&quot;</span><span class="p">)</span>
</span><span id="main-505"><a href="#main-505"><span class="linenos">505</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--setup-config&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;设置配置文件&quot;</span><span class="p">)</span>
</span><span id="main-506"><a href="#main-506"><span class="linenos">506</span></a>    
</span><span id="main-507"><a href="#main-507"><span class="linenos">507</span></a>    <span class="n">args</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>
</span><span id="main-508"><a href="#main-508"><span class="linenos">508</span></a>    
</span><span id="main-509"><a href="#main-509"><span class="linenos">509</span></a>    <span class="n">downloader</span> <span class="o">=</span> <span class="n">ROMDownloader</span><span class="p">(</span><span class="n">args</span><span class="o">.</span><span class="n">config</span><span class="p">)</span>
</span><span id="main-510"><a href="#main-510"><span class="linenos">510</span></a>    
</span><span id="main-511"><a href="#main-511"><span class="linenos">511</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">setup_config</span><span class="p">:</span>
</span><span id="main-512"><a href="#main-512"><span class="linenos">512</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;请编辑配置文件:&quot;</span><span class="p">,</span> <span class="n">args</span><span class="o">.</span><span class="n">config</span><span class="p">)</span>
</span><span id="main-513"><a href="#main-513"><span class="linenos">513</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;配置项包括:&quot;</span><span class="p">)</span>
</span><span id="main-514"><a href="#main-514"><span class="linenos">514</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;- 树莓派连接信息&quot;</span><span class="p">)</span>
</span><span id="main-515"><a href="#main-515"><span class="linenos">515</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;- 下载参数&quot;</span><span class="p">)</span>
</span><span id="main-516"><a href="#main-516"><span class="linenos">516</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;- 验证设置&quot;</span><span class="p">)</span>
</span><span id="main-517"><a href="#main-517"><span class="linenos">517</span></a>        <span class="k">return</span>
</span><span id="main-518"><a href="#main-518"><span class="linenos">518</span></a>    
</span><span id="main-519"><a href="#main-519"><span class="linenos">519</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">upload_only</span><span class="p">:</span>
</span><span id="main-520"><a href="#main-520"><span class="linenos">520</span></a>        <span class="c1"># 查找已下载的ROM文件</span>
</span><span id="main-521"><a href="#main-521"><span class="linenos">521</span></a>        <span class="n">extract_dir</span> <span class="o">=</span> <span class="n">downloader</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="s2">&quot;extracted&quot;</span>
</span><span id="main-522"><a href="#main-522"><span class="linenos">522</span></a>        <span class="k">if</span> <span class="n">extract_dir</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="main-523"><a href="#main-523"><span class="linenos">523</span></a>            <span class="n">rom_files</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">extract_dir</span><span class="o">.</span><span class="n">glob</span><span class="p">(</span><span class="s2">&quot;*.nes&quot;</span><span class="p">))</span> <span class="o">+</span> <span class="nb">list</span><span class="p">(</span><span class="n">extract_dir</span><span class="o">.</span><span class="n">glob</span><span class="p">(</span><span class="s2">&quot;*.NES&quot;</span><span class="p">))</span>
</span><span id="main-524"><a href="#main-524"><span class="linenos">524</span></a>            <span class="k">if</span> <span class="n">rom_files</span><span class="p">:</span>
</span><span id="main-525"><a href="#main-525"><span class="linenos">525</span></a>                <span class="n">downloader</span><span class="o">.</span><span class="n">upload_roms</span><span class="p">(</span><span class="n">rom_files</span><span class="p">)</span>
</span><span id="main-526"><a href="#main-526"><span class="linenos">526</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="main-527"><a href="#main-527"><span class="linenos">527</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;未找到ROM文件&quot;</span><span class="p">)</span>
</span><span id="main-528"><a href="#main-528"><span class="linenos">528</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="main-529"><a href="#main-529"><span class="linenos">529</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;未找到解压目录&quot;</span><span class="p">)</span>
</span><span id="main-530"><a href="#main-530"><span class="linenos">530</span></a>        <span class="k">return</span>
</span><span id="main-531"><a href="#main-531"><span class="linenos">531</span></a>    
</span><span id="main-532"><a href="#main-532"><span class="linenos">532</span></a>    <span class="c1"># 运行完整流程</span>
</span><span id="main-533"><a href="#main-533"><span class="linenos">533</span></a>    <span class="n">success</span> <span class="o">=</span> <span class="n">downloader</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">args</span><span class="o">.</span><span class="n">search</span><span class="p">)</span>
</span><span id="main-534"><a href="#main-534"><span class="linenos">534</span></a>    
</span><span id="main-535"><a href="#main-535"><span class="linenos">535</span></a>    <span class="k">if</span> <span class="n">success</span><span class="p">:</span>
</span><span id="main-536"><a href="#main-536"><span class="linenos">536</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">🎉 ROM下载和传输完成！&quot;</span><span class="p">)</span>
</span><span id="main-537"><a href="#main-537"><span class="linenos">537</span></a>    <span class="k">else</span><span class="p">:</span>
</span><span id="main-538"><a href="#main-538"><span class="linenos">538</span></a>        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">❌ 操作失败，请查看日志文件&quot;</span><span class="p">)</span>
</span></pre></div>


            <div class="docstring"><p>主函数</p>
</div>


                </section>
    </main>
<script>
    function escapeHTML(html) {
        return document.createElement('div').appendChild(document.createTextNode(html)).parentNode.innerHTML;
    }

    const originalContent = document.querySelector("main.pdoc");
    let currentContent = originalContent;

    function setContent(innerHTML) {
        let elem;
        if (innerHTML) {
            elem = document.createElement("main");
            elem.classList.add("pdoc");
            elem.innerHTML = innerHTML;
        } else {
            elem = originalContent;
        }
        if (currentContent !== elem) {
            currentContent.replaceWith(elem);
            currentContent = elem;
        }
    }

    function getSearchTerm() {
        return (new URL(window.location)).searchParams.get("search");
    }

    const searchBox = document.querySelector(".pdoc input[type=search]");
    searchBox.addEventListener("input", function () {
        let url = new URL(window.location);
        if (searchBox.value.trim()) {
            url.hash = "";
            url.searchParams.set("search", searchBox.value);
        } else {
            url.searchParams.delete("search");
        }
        history.replaceState("", "", url.toString());
        onInput();
    });
    window.addEventListener("popstate", onInput);


    let search, searchErr;

    async function initialize() {
        try {
            search = await new Promise((resolve, reject) => {
                const script = document.createElement("script");
                script.type = "text/javascript";
                script.async = true;
                script.onload = () => resolve(window.pdocSearch);
                script.onerror = (e) => reject(e);
                script.src = "../search.js";
                document.getElementsByTagName("head")[0].appendChild(script);
            });
        } catch (e) {
            console.error("Cannot fetch pdoc search index");
            searchErr = "Cannot fetch search index.";
        }
        onInput();

        document.querySelector("nav.pdoc").addEventListener("click", e => {
            if (e.target.hash) {
                searchBox.value = "";
                searchBox.dispatchEvent(new Event("input"));
            }
        });
    }

    function onInput() {
        setContent((() => {
            const term = getSearchTerm();
            if (!term) {
                return null
            }
            if (searchErr) {
                return `<h3>Error: ${searchErr}</h3>`
            }
            if (!search) {
                return "<h3>Searching...</h3>"
            }

            window.scrollTo({top: 0, left: 0, behavior: 'auto'});

            const results = search(term);

            let html;
            if (results.length === 0) {
                html = `No search results for '${escapeHTML(term)}'.`
            } else {
                html = `<h4>${results.length} search result${results.length > 1 ? "s" : ""} for '${escapeHTML(term)}'.</h4>`;
            }
            for (let result of results.slice(0, 10)) {
                let doc = result.doc;
                let url = `../${doc.modulename.replaceAll(".", "/")}.html`;
                if (doc.qualname) {
                    url += `#${doc.qualname}`;
                }

                let heading;
                switch (result.doc.kind) {
                    case "function":
                        if (doc.fullname.endsWith(".__init__")) {
                            heading = `<span class="name">${doc.fullname.replace(/\.__init__$/, "")}</span>${doc.signature}`;
                        } else {
                            heading = `<span class="def">${doc.funcdef}</span> <span class="name">${doc.fullname}</span>${doc.signature}`;
                        }
                        break;
                    case "class":
                        heading = `<span class="def">class</span> <span class="name">${doc.fullname}</span>`;
                        if (doc.bases)
                            heading += `<wbr>(<span class="base">${doc.bases}</span>)`;
                        heading += `:`;
                        break;
                    case "variable":
                        heading = `<span class="name">${doc.fullname}</span>`;
                        if (doc.annotation)
                            heading += `<span class="annotation">${doc.annotation}</span>`;
                        if (doc.default_value)
                            heading += `<span class="default_value"> = ${doc.default_value}</span>`;
                        break;
                    default:
                        heading = `<span class="name">${doc.fullname}</span>`;
                        break;
                }
                html += `
                        <section class="search-result">
                        <a href="${url}" class="attr ${doc.kind}">${heading}</a>
                        <div class="docstring">${doc.doc}</div>
                        </section>
                    `;

            }
            return html;
        })());
    }

    if (getSearchTerm()) {
        initialize();
        searchBox.value = getSearchTerm();
        onInput();
    } else {
        searchBox.addEventListener("focus", initialize, {once: true});
    }

    searchBox.addEventListener("keydown", e => {
        if (["ArrowDown", "ArrowUp", "Enter"].includes(e.key)) {
            let focused = currentContent.querySelector(".search-result.focused");
            if (!focused) {
                currentContent.querySelector(".search-result").classList.add("focused");
            } else if (
                e.key === "ArrowDown"
                && focused.nextElementSibling
                && focused.nextElementSibling.classList.contains("search-result")
            ) {
                focused.classList.remove("focused");
                focused.nextElementSibling.classList.add("focused");
                focused.nextElementSibling.scrollIntoView({
                    behavior: "smooth",
                    block: "nearest",
                    inline: "nearest"
                });
            } else if (
                e.key === "ArrowUp"
                && focused.previousElementSibling
                && focused.previousElementSibling.classList.contains("search-result")
            ) {
                focused.classList.remove("focused");
                focused.previousElementSibling.classList.add("focused");
                focused.previousElementSibling.scrollIntoView({
                    behavior: "smooth",
                    block: "nearest",
                    inline: "nearest"
                });
            } else if (
                e.key === "Enter"
            ) {
                focused.querySelector("a").click();
            }
        }
    });
</script></body>
</html>