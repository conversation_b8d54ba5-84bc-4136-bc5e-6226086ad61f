<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="generator" content="pdoc 15.0.4"/>
    <title>core.retropie_installer API documentation</title>

    <style>/*! * Bootstrap Reboot v5.0.0 (https://getbootstrap.com/) * Copyright 2011-2021 The Bootstrap Authors * Copyright 2011-2021 Twitter, Inc. * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE) * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md) */*,::after,::before{box-sizing:border-box}@media (prefers-reduced-motion:no-preference){:root{scroll-behavior:smooth}}body{margin:0;font-family:system-ui,-apple-system,"Segoe UI",<PERSON><PERSON>,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans","Liberation Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-size:1rem;font-weight:400;line-height:1.5;color:#212529;background-color:#fff;-webkit-text-size-adjust:100%;-webkit-tap-highlight-color:transparent}hr{margin:1rem 0;color:inherit;background-color:currentColor;border:0;opacity:.25}hr:not([size]){height:1px}h1,h2,h3,h4,h5,h6{margin-top:0;margin-bottom:.5rem;font-weight:500;line-height:1.2}h1{font-size:calc(1.375rem + 1.5vw)}@media (min-width:1200px){h1{font-size:2.5rem}}h2{font-size:calc(1.325rem + .9vw)}@media (min-width:1200px){h2{font-size:2rem}}h3{font-size:calc(1.3rem + .6vw)}@media (min-width:1200px){h3{font-size:1.75rem}}h4{font-size:calc(1.275rem + .3vw)}@media (min-width:1200px){h4{font-size:1.5rem}}h5{font-size:1.25rem}h6{font-size:1rem}p{margin-top:0;margin-bottom:1rem}abbr[data-bs-original-title],abbr[title]{-webkit-text-decoration:underline dotted;text-decoration:underline dotted;cursor:help;-webkit-text-decoration-skip-ink:none;text-decoration-skip-ink:none}address{margin-bottom:1rem;font-style:normal;line-height:inherit}ol,ul{padding-left:2rem}dl,ol,ul{margin-top:0;margin-bottom:1rem}ol ol,ol ul,ul ol,ul ul{margin-bottom:0}dt{font-weight:700}dd{margin-bottom:.5rem;margin-left:0}blockquote{margin:0 0 1rem}b,strong{font-weight:bolder}small{font-size:.875em}mark{padding:.2em;background-color:#fcf8e3}sub,sup{position:relative;font-size:.75em;line-height:0;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}a{color:#0d6efd;text-decoration:underline}a:hover{color:#0a58ca}a:not([href]):not([class]),a:not([href]):not([class]):hover{color:inherit;text-decoration:none}code,kbd,pre,samp{font-family:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;font-size:1em;direction:ltr;unicode-bidi:bidi-override}pre{display:block;margin-top:0;margin-bottom:1rem;overflow:auto;font-size:.875em}pre code{font-size:inherit;color:inherit;word-break:normal}code{font-size:.875em;color:#d63384;word-wrap:break-word}a>code{color:inherit}kbd{padding:.2rem .4rem;font-size:.875em;color:#fff;background-color:#212529;border-radius:.2rem}kbd kbd{padding:0;font-size:1em;font-weight:700}figure{margin:0 0 1rem}img,svg{vertical-align:middle}table{caption-side:bottom;border-collapse:collapse}caption{padding-top:.5rem;padding-bottom:.5rem;color:#6c757d;text-align:left}th{text-align:inherit;text-align:-webkit-match-parent}tbody,td,tfoot,th,thead,tr{border-color:inherit;border-style:solid;border-width:0}label{display:inline-block}button{border-radius:0}button:focus:not(:focus-visible){outline:0}button,input,optgroup,select,textarea{margin:0;font-family:inherit;font-size:inherit;line-height:inherit}button,select{text-transform:none}[role=button]{cursor:pointer}select{word-wrap:normal}select:disabled{opacity:1}[list]::-webkit-calendar-picker-indicator{display:none}[type=button],[type=reset],[type=submit],button{-webkit-appearance:button}[type=button]:not(:disabled),[type=reset]:not(:disabled),[type=submit]:not(:disabled),button:not(:disabled){cursor:pointer}::-moz-focus-inner{padding:0;border-style:none}textarea{resize:vertical}fieldset{min-width:0;padding:0;margin:0;border:0}legend{float:left;width:100%;padding:0;margin-bottom:.5rem;font-size:calc(1.275rem + .3vw);line-height:inherit}@media (min-width:1200px){legend{font-size:1.5rem}}legend+*{clear:left}::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-fields-wrapper,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-text,::-webkit-datetime-edit-year-field{padding:0}::-webkit-inner-spin-button{height:auto}[type=search]{outline-offset:-2px;-webkit-appearance:textfield}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-color-swatch-wrapper{padding:0}::file-selector-button{font:inherit}::-webkit-file-upload-button{font:inherit;-webkit-appearance:button}output{display:inline-block}iframe{border:0}summary{display:list-item;cursor:pointer}progress{vertical-align:baseline}[hidden]{display:none!important}</style>
    <style>/*! syntax-highlighting.css */pre{line-height:125%;}span.linenos{color:inherit; background-color:transparent; padding-left:5px; padding-right:20px;}.pdoc-code .hll{background-color:#ffffcc}.pdoc-code{background:#f8f8f8;}.pdoc-code .c{color:#3D7B7B; font-style:italic}.pdoc-code .err{border:1px solid #FF0000}.pdoc-code .k{color:#008000; font-weight:bold}.pdoc-code .o{color:#666666}.pdoc-code .ch{color:#3D7B7B; font-style:italic}.pdoc-code .cm{color:#3D7B7B; font-style:italic}.pdoc-code .cp{color:#9C6500}.pdoc-code .cpf{color:#3D7B7B; font-style:italic}.pdoc-code .c1{color:#3D7B7B; font-style:italic}.pdoc-code .cs{color:#3D7B7B; font-style:italic}.pdoc-code .gd{color:#A00000}.pdoc-code .ge{font-style:italic}.pdoc-code .gr{color:#E40000}.pdoc-code .gh{color:#000080; font-weight:bold}.pdoc-code .gi{color:#008400}.pdoc-code .go{color:#717171}.pdoc-code .gp{color:#000080; font-weight:bold}.pdoc-code .gs{font-weight:bold}.pdoc-code .gu{color:#800080; font-weight:bold}.pdoc-code .gt{color:#0044DD}.pdoc-code .kc{color:#008000; font-weight:bold}.pdoc-code .kd{color:#008000; font-weight:bold}.pdoc-code .kn{color:#008000; font-weight:bold}.pdoc-code .kp{color:#008000}.pdoc-code .kr{color:#008000; font-weight:bold}.pdoc-code .kt{color:#B00040}.pdoc-code .m{color:#666666}.pdoc-code .s{color:#BA2121}.pdoc-code .na{color:#687822}.pdoc-code .nb{color:#008000}.pdoc-code .nc{color:#0000FF; font-weight:bold}.pdoc-code .no{color:#880000}.pdoc-code .nd{color:#AA22FF}.pdoc-code .ni{color:#717171; font-weight:bold}.pdoc-code .ne{color:#CB3F38; font-weight:bold}.pdoc-code .nf{color:#0000FF}.pdoc-code .nl{color:#767600}.pdoc-code .nn{color:#0000FF; font-weight:bold}.pdoc-code .nt{color:#008000; font-weight:bold}.pdoc-code .nv{color:#19177C}.pdoc-code .ow{color:#AA22FF; font-weight:bold}.pdoc-code .w{color:#bbbbbb}.pdoc-code .mb{color:#666666}.pdoc-code .mf{color:#666666}.pdoc-code .mh{color:#666666}.pdoc-code .mi{color:#666666}.pdoc-code .mo{color:#666666}.pdoc-code .sa{color:#BA2121}.pdoc-code .sb{color:#BA2121}.pdoc-code .sc{color:#BA2121}.pdoc-code .dl{color:#BA2121}.pdoc-code .sd{color:#BA2121; font-style:italic}.pdoc-code .s2{color:#BA2121}.pdoc-code .se{color:#AA5D1F; font-weight:bold}.pdoc-code .sh{color:#BA2121}.pdoc-code .si{color:#A45A77; font-weight:bold}.pdoc-code .sx{color:#008000}.pdoc-code .sr{color:#A45A77}.pdoc-code .s1{color:#BA2121}.pdoc-code .ss{color:#19177C}.pdoc-code .bp{color:#008000}.pdoc-code .fm{color:#0000FF}.pdoc-code .vc{color:#19177C}.pdoc-code .vg{color:#19177C}.pdoc-code .vi{color:#19177C}.pdoc-code .vm{color:#19177C}.pdoc-code .il{color:#666666}</style>
    <style>/*! theme.css */:root{--pdoc-background:#fff;}.pdoc{--text:#212529;--muted:#6c757d;--link:#3660a5;--link-hover:#1659c5;--code:#f8f8f8;--active:#fff598;--accent:#eee;--accent2:#c1c1c1;--nav-hover:rgba(255, 255, 255, 0.5);--name:#0066BB;--def:#008800;--annotation:#007020;}</style>
    <style>/*! layout.css */html, body{width:100%;height:100%;}html, main{scroll-behavior:smooth;}body{background-color:var(--pdoc-background);}@media (max-width:769px){#navtoggle{cursor:pointer;position:absolute;width:50px;height:40px;top:1rem;right:1rem;border-color:var(--text);color:var(--text);display:flex;opacity:0.8;z-index:999;}#navtoggle:hover{opacity:1;}#togglestate + div{display:none;}#togglestate:checked + div{display:inherit;}main, header{padding:2rem 3vw;}header + main{margin-top:-3rem;}.git-button{display:none !important;}nav input[type="search"]{max-width:77%;}nav input[type="search"]:first-child{margin-top:-6px;}nav input[type="search"]:valid ~ *{display:none !important;}}@media (min-width:770px){:root{--sidebar-width:clamp(12.5rem, 28vw, 22rem);}nav{position:fixed;overflow:auto;height:100vh;width:var(--sidebar-width);}main, header{padding:3rem 2rem 3rem calc(var(--sidebar-width) + 3rem);width:calc(54rem + var(--sidebar-width));max-width:100%;}header + main{margin-top:-4rem;}#navtoggle{display:none;}}#togglestate{position:absolute;height:0;opacity:0;}nav.pdoc{--pad:clamp(0.5rem, 2vw, 1.75rem);--indent:1.5rem;background-color:var(--accent);border-right:1px solid var(--accent2);box-shadow:0 0 20px rgba(50, 50, 50, .2) inset;padding:0 0 0 var(--pad);overflow-wrap:anywhere;scrollbar-width:thin; scrollbar-color:var(--accent2) transparent; z-index:1}nav.pdoc::-webkit-scrollbar{width:.4rem; }nav.pdoc::-webkit-scrollbar-thumb{background-color:var(--accent2); }nav.pdoc > div{padding:var(--pad) 0;}nav.pdoc .module-list-button{display:inline-flex;align-items:center;color:var(--text);border-color:var(--muted);margin-bottom:1rem;}nav.pdoc .module-list-button:hover{border-color:var(--text);}nav.pdoc input[type=search]{display:block;outline-offset:0;width:calc(100% - var(--pad));}nav.pdoc .logo{max-width:calc(100% - var(--pad));max-height:35vh;display:block;margin:0 auto 1rem;transform:translate(calc(-.5 * var(--pad)), 0);}nav.pdoc ul{list-style:none;padding-left:0;}nav.pdoc > div > ul{margin-left:calc(0px - var(--pad));}nav.pdoc li a{padding:.2rem 0 .2rem calc(var(--pad) + var(--indent));}nav.pdoc > div > ul > li > a{padding-left:var(--pad);}nav.pdoc li{transition:all 100ms;}nav.pdoc li:hover{background-color:var(--nav-hover);}nav.pdoc a, nav.pdoc a:hover{color:var(--text);}nav.pdoc a{display:block;}nav.pdoc > h2:first-of-type{margin-top:1.5rem;}nav.pdoc .class:before{content:"class ";color:var(--muted);}nav.pdoc .function:after{content:"()";color:var(--muted);}nav.pdoc footer:before{content:"";display:block;width:calc(100% - var(--pad));border-top:solid var(--accent2) 1px;margin-top:1.5rem;padding-top:.5rem;}nav.pdoc footer{font-size:small;}</style>
    <style>/*! content.css */.pdoc{color:var(--text);box-sizing:border-box;line-height:1.5;background:none;}.pdoc .pdoc-button{cursor:pointer;display:inline-block;border:solid black 1px;border-radius:2px;font-size:.75rem;padding:calc(0.5em - 1px) 1em;transition:100ms all;}.pdoc .alert{padding:1rem 1rem 1rem calc(1.5rem + 24px);border:1px solid transparent;border-radius:.25rem;background-repeat:no-repeat;background-position:.75rem center;margin-bottom:1rem;}.pdoc .alert > em{display:none;}.pdoc .alert > *:last-child{margin-bottom:0;}.pdoc .alert.note{color:#084298;background-color:#cfe2ff;border-color:#b6d4fe;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23084298%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M8%2016A8%208%200%201%200%208%200a8%208%200%200%200%200%2016zm.93-9.412-1%204.705c-.07.34.029.533.304.533.194%200%20.487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703%200-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381%202.29-.287zM8%205.5a1%201%200%201%201%200-2%201%201%200%200%201%200%202z%22/%3E%3C/svg%3E");}.pdoc .alert.tip{color:#0a3622;background-color:#d1e7dd;border-color:#a3cfbb;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%230a3622%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M2%206a6%206%200%201%201%2010.174%204.31c-.203.196-.359.4-.453.619l-.762%201.769A.5.5%200%200%201%2010.5%2013a.5.5%200%200%201%200%201%20.5.5%200%200%201%200%201l-.224.447a1%201%200%200%201-.894.553H6.618a1%201%200%200%201-.894-.553L5.5%2015a.5.5%200%200%201%200-1%20.5.5%200%200%201%200-1%20.5.5%200%200%201-.46-.302l-.761-1.77a2%202%200%200%200-.453-.618A5.98%205.98%200%200%201%202%206m6-5a5%205%200%200%200-3.479%208.592c.263.254.514.564.676.941L5.83%2012h4.342l.632-1.467c.162-.377.413-.687.676-.941A5%205%200%200%200%208%201%22/%3E%3C/svg%3E");}.pdoc .alert.important{color:#055160;background-color:#cff4fc;border-color:#9eeaf9;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23055160%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M2%200a2%202%200%200%200-2%202v12a2%202%200%200%200%202%202h12a2%202%200%200%200%202-2V2a2%202%200%200%200-2-2zm6%204c.535%200%20.954.462.9.995l-.35%203.507a.552.552%200%200%201-1.1%200L7.1%204.995A.905.905%200%200%201%208%204m.002%206a1%201%200%201%201%200%202%201%201%200%200%201%200-2%22/%3E%3C/svg%3E");}.pdoc .alert.warning{color:#664d03;background-color:#fff3cd;border-color:#ffecb5;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23664d03%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M8.982%201.566a1.13%201.13%200%200%200-1.96%200L.165%2013.233c-.457.778.091%201.767.98%201.767h13.713c.889%200%201.438-.99.98-1.767L8.982%201.566zM8%205c.535%200%20.954.462.9.995l-.35%203.507a.552.552%200%200%201-1.1%200L7.1%205.995A.905.905%200%200%201%208%205zm.002%206a1%201%200%201%201%200%202%201%201%200%200%201%200-2z%22/%3E%3C/svg%3E");}.pdoc .alert.caution{color:#842029;background-color:#f8d7da;border-color:#f5c2c7;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23842029%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M11.46.146A.5.5%200%200%200%2011.107%200H4.893a.5.5%200%200%200-.353.146L.146%204.54A.5.5%200%200%200%200%204.893v6.214a.5.5%200%200%200%20.146.353l4.394%204.394a.5.5%200%200%200%20.353.146h6.214a.5.5%200%200%200%20.353-.146l4.394-4.394a.5.5%200%200%200%20.146-.353V4.893a.5.5%200%200%200-.146-.353zM8%204c.535%200%20.954.462.9.995l-.35%203.507a.552.552%200%200%201-1.1%200L7.1%204.995A.905.905%200%200%201%208%204m.002%206a1%201%200%201%201%200%202%201%201%200%200%201%200-2%22/%3E%3C/svg%3E");}.pdoc .alert.danger{color:#842029;background-color:#f8d7da;border-color:#f5c2c7;background-image:url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2224%22%20height%3D%2224%22%20fill%3D%22%23842029%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M5.52.359A.5.5%200%200%201%206%200h4a.5.5%200%200%201%20.474.658L8.694%206H12.5a.5.5%200%200%201%20.395.807l-7%209a.5.5%200%200%201-.873-.454L6.823%209.5H3.5a.5.5%200%200%201-.48-.641l2.5-8.5z%22/%3E%3C/svg%3E");}.pdoc .visually-hidden{position:absolute !important;width:1px !important;height:1px !important;padding:0 !important;margin:-1px !important;overflow:hidden !important;clip:rect(0, 0, 0, 0) !important;white-space:nowrap !important;border:0 !important;}.pdoc h1, .pdoc h2, .pdoc h3{font-weight:300;margin:.3em 0;padding:.2em 0;}.pdoc > section:not(.module-info) h1{font-size:1.5rem;font-weight:500;}.pdoc > section:not(.module-info) h2{font-size:1.4rem;font-weight:500;}.pdoc > section:not(.module-info) h3{font-size:1.3rem;font-weight:500;}.pdoc > section:not(.module-info) h4{font-size:1.2rem;}.pdoc > section:not(.module-info) h5{font-size:1.1rem;}.pdoc a{text-decoration:none;color:var(--link);}.pdoc a:hover{color:var(--link-hover);}.pdoc blockquote{margin-left:2rem;}.pdoc pre{border-top:1px solid var(--accent2);border-bottom:1px solid var(--accent2);margin-top:0;margin-bottom:1em;padding:.5rem 0 .5rem .5rem;overflow-x:auto;background-color:var(--code);}.pdoc code{color:var(--text);padding:.2em .4em;margin:0;font-size:85%;background-color:var(--accent);border-radius:6px;}.pdoc a > code{color:inherit;}.pdoc pre > code{display:inline-block;font-size:inherit;background:none;border:none;padding:0;}.pdoc > section:not(.module-info){margin-bottom:1.5rem;}.pdoc .modulename{margin-top:0;font-weight:bold;}.pdoc .modulename a{color:var(--link);transition:100ms all;}.pdoc .git-button{float:right;border:solid var(--link) 1px;}.pdoc .git-button:hover{background-color:var(--link);color:var(--pdoc-background);}.view-source-toggle-state,.view-source-toggle-state ~ .pdoc-code{display:none;}.view-source-toggle-state:checked ~ .pdoc-code{display:block;}.view-source-button{display:inline-block;float:right;font-size:.75rem;line-height:1.5rem;color:var(--muted);padding:0 .4rem 0 1.3rem;cursor:pointer;text-indent:-2px;}.view-source-button > span{visibility:hidden;}.module-info .view-source-button{float:none;display:flex;justify-content:flex-end;margin:-1.2rem .4rem -.2rem 0;}.view-source-button::before{position:absolute;content:"View Source";display:list-item;list-style-type:disclosure-closed;}.view-source-toggle-state:checked ~ .attr .view-source-button::before,.view-source-toggle-state:checked ~ .view-source-button::before{list-style-type:disclosure-open;}.pdoc .docstring{margin-bottom:1.5rem;}.pdoc section:not(.module-info) .docstring{margin-left:clamp(0rem, 5vw - 2rem, 1rem);}.pdoc .docstring .pdoc-code{margin-left:1em;margin-right:1em;}.pdoc h1:target,.pdoc h2:target,.pdoc h3:target,.pdoc h4:target,.pdoc h5:target,.pdoc h6:target,.pdoc .pdoc-code > pre > span:target{background-color:var(--active);box-shadow:-1rem 0 0 0 var(--active);}.pdoc .pdoc-code > pre > span:target{display:block;}.pdoc div:target > .attr,.pdoc section:target > .attr,.pdoc dd:target > a{background-color:var(--active);}.pdoc *{scroll-margin:2rem;}.pdoc .pdoc-code .linenos{user-select:none;}.pdoc .attr:hover{filter:contrast(0.95);}.pdoc section, .pdoc .classattr{position:relative;}.pdoc .headerlink{--width:clamp(1rem, 3vw, 2rem);position:absolute;top:0;left:calc(0rem - var(--width));transition:all 100ms ease-in-out;opacity:0;}.pdoc .headerlink::before{content:"#";display:block;text-align:center;width:var(--width);height:2.3rem;line-height:2.3rem;font-size:1.5rem;}.pdoc .attr:hover ~ .headerlink,.pdoc *:target > .headerlink,.pdoc .headerlink:hover{opacity:1;}.pdoc .attr{display:block;margin:.5rem 0 .5rem;padding:.4rem .4rem .4rem 1rem;background-color:var(--accent);overflow-x:auto;}.pdoc .classattr{margin-left:2rem;}.pdoc .decorator-deprecated{color:#842029;}.pdoc .decorator-deprecated ~ span{filter:grayscale(1) opacity(0.8);}.pdoc .name{color:var(--name);font-weight:bold;}.pdoc .def{color:var(--def);font-weight:bold;}.pdoc .signature{background-color:transparent;}.pdoc .param, .pdoc .return-annotation{white-space:pre;}.pdoc .signature.multiline .param{display:block;}.pdoc .signature.condensed .param{display:inline-block;}.pdoc .annotation{color:var(--annotation);}.pdoc .view-value-toggle-state,.pdoc .view-value-toggle-state ~ .default_value{display:none;}.pdoc .view-value-toggle-state:checked ~ .default_value{display:inherit;}.pdoc .view-value-button{font-size:.5rem;vertical-align:middle;border-style:dashed;margin-top:-0.1rem;}.pdoc .view-value-button:hover{background:white;}.pdoc .view-value-button::before{content:"show";text-align:center;width:2.2em;display:inline-block;}.pdoc .view-value-toggle-state:checked ~ .view-value-button::before{content:"hide";}.pdoc .inherited{margin-left:2rem;}.pdoc .inherited dt{font-weight:700;}.pdoc .inherited dt, .pdoc .inherited dd{display:inline;margin-left:0;margin-bottom:.5rem;}.pdoc .inherited dd:not(:last-child):after{content:", ";}.pdoc .inherited .class:before{content:"class ";}.pdoc .inherited .function a:after{content:"()";}.pdoc .search-result .docstring{overflow:auto;max-height:25vh;}.pdoc .search-result.focused > .attr{background-color:var(--active);}.pdoc .attribution{margin-top:2rem;display:block;opacity:0.5;transition:all 200ms;filter:grayscale(100%);}.pdoc .attribution:hover{opacity:1;filter:grayscale(0%);}.pdoc .attribution img{margin-left:5px;height:35px;vertical-align:middle;width:70px;transition:all 200ms;}.pdoc table{display:block;width:max-content;max-width:100%;overflow:auto;margin-bottom:1rem;}.pdoc table th{font-weight:600;}.pdoc table th, .pdoc table td{padding:6px 13px;border:1px solid var(--accent2);}</style>
    <style>/*! custom.css */</style></head>
<body>
    <nav class="pdoc">
        <label id="navtoggle" for="togglestate" class="pdoc-button"><svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke-linecap='round' stroke="currentColor" stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg></label>
        <input id="togglestate" type="checkbox" aria-hidden="true" tabindex="-1">
        <div>            <a class="pdoc-button module-list-button" href="../core.html">
<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-box-arrow-in-left" viewBox="0 0 16 16">
  <path fill-rule="evenodd" d="M10 3.5a.5.5 0 0 0-.5-.5h-8a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5h8a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 1 1 0v2A1.5 1.5 0 0 1 9.5 14h-8A1.5 1.5 0 0 1 0 12.5v-9A1.5 1.5 0 0 1 1.5 2h8A1.5 1.5 0 0 1 11 3.5v2a.5.5 0 0 1-1 0v-2z"/>
  <path fill-rule="evenodd" d="M4.146 8.354a.5.5 0 0 1 0-.708l3-3a.5.5 0 1 1 .708.708L5.707 7.5H14.5a.5.5 0 0 1 0 1H5.707l2.147 2.146a.5.5 0 0 1-.708.708l-3-3z"/>
</svg>                &nbsp;core</a>


            <input type="search" placeholder="Search..." role="searchbox" aria-label="search"
                   pattern=".+" required>



            <h2>API Documentation</h2>
                <ul class="memberlist">
            <li>
                    <a class="variable" href="#logger">logger</a>
            </li>
            <li>
                    <a class="class" href="#RetroPieInstaller">RetroPieInstaller</a>
                            <ul class="memberlist">
                        <li>
                                <a class="function" href="#RetroPieInstaller.__init__">RetroPieInstaller</a>
                        </li>
                        <li>
                                <a class="variable" href="#RetroPieInstaller.system">system</a>
                        </li>
                        <li>
                                <a class="variable" href="#RetroPieInstaller.retropie_url">retropie_url</a>
                        </li>
                        <li>
                                <a class="variable" href="#RetroPieInstaller.download_dir">download_dir</a>
                        </li>
                        <li>
                                <a class="function" href="#RetroPieInstaller.check_dependencies">check_dependencies</a>
                        </li>
                        <li>
                                <a class="function" href="#RetroPieInstaller.get_retropie_download_url">get_retropie_download_url</a>
                        </li>
                        <li>
                                <a class="function" href="#RetroPieInstaller.download_image">download_image</a>
                        </li>
                        <li>
                                <a class="function" href="#RetroPieInstaller.extract_image">extract_image</a>
                        </li>
                        <li>
                                <a class="function" href="#RetroPieInstaller.list_available_disks">list_available_disks</a>
                        </li>
                        <li>
                                <a class="function" href="#RetroPieInstaller.burn_image">burn_image</a>
                        </li>
                        <li>
                                <a class="function" href="#RetroPieInstaller.run">run</a>
                        </li>
                </ul>

            </li>
            <li>
                    <a class="function" href="#main">main</a>
            </li>
    </ul>



        <a class="attribution" title="pdoc: Python API documentation generator" href="https://pdoc.dev" target="_blank">
            built with <span class="visually-hidden">pdoc</span><img
                alt="pdoc logo"
                src="data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20role%3D%22img%22%20aria-label%3D%22pdoc%20logo%22%20width%3D%22300%22%20height%3D%22150%22%20viewBox%3D%22-1%200%2060%2030%22%3E%3Ctitle%3Epdoc%3C/title%3E%3Cpath%20d%3D%22M29.621%2021.293c-.011-.273-.214-.475-.511-.481a.5.5%200%200%200-.489.503l-.044%201.393c-.097.551-.695%201.215-1.566%201.704-.577.428-1.306.486-2.193.182-1.426-.617-2.467-1.654-3.304-2.487l-.173-.172a3.43%203.43%200%200%200-.365-.306.49.49%200%200%200-.286-.196c-1.718-1.06-4.931-1.47-7.353.191l-.219.15c-1.707%201.187-3.413%202.131-4.328%201.03-.02-.027-.49-.685-.141-1.763.233-.721.546-2.408.772-4.076.042-.09.067-.187.046-.288.166-1.347.277-2.625.241-3.351%201.378-1.008%202.271-2.586%202.271-4.362%200-.976-.272-1.935-.788-2.774-.057-.094-.122-.18-.184-.268.033-.167.052-.339.052-.516%200-1.477-1.202-2.679-2.679-2.679-.791%200-1.496.352-1.987.9a6.3%206.3%200%200%200-1.001.029c-.492-.564-1.207-.929-2.012-.929-1.477%200-2.679%201.202-2.679%202.679A2.65%202.65%200%200%200%20.97%206.554c-.383.747-.595%201.572-.595%202.41%200%202.311%201.507%204.29%203.635%205.107-.037.699-.147%202.27-.423%203.294l-.137.461c-.622%202.042-2.515%208.257%201.727%2010.643%201.614.908%203.06%201.248%204.317%201.248%202.665%200%204.492-1.524%205.322-2.401%201.476-1.559%202.886-1.854%206.491.82%201.877%201.393%203.514%201.753%204.861%201.068%202.223-1.713%202.811-3.867%203.399-6.374.077-.846.056-1.469.054-1.537zm-4.835%204.313c-.054.305-.156.586-.242.629-.034-.007-.131-.022-.307-.157-.145-.111-.314-.478-.456-.908.221.121.432.25.675.355.115.039.219.051.33.081zm-2.251-1.238c-.05.33-.158.648-.252.694-.022.001-.125-.018-.307-.157-.217-.166-.488-.906-.639-1.573.358.344.754.693%201.198%201.036zm-3.887-2.337c-.006-.116-.018-.231-.041-.342.635.145%201.189.368%201.599.625.097.231.166.481.174.642-.03.049-.055.101-.067.158-.046.013-.128.026-.298.004-.278-.037-.901-.57-1.367-1.087zm-1.127-.497c.116.306.176.625.12.71-.019.014-.117.045-.345.016-.206-.027-.604-.332-.986-.695.41-.051.816-.056%201.211-.031zm-4.535%201.535c.209.22.379.47.358.598-.006.041-.088.138-.351.234-.144.055-.539-.063-.979-.259a11.66%2011.66%200%200%200%20.972-.573zm.983-.664c.359-.237.738-.418%201.126-.554.25.237.479.548.457.694-.006.042-.087.138-.351.235-.174.064-.694-.105-1.232-.375zm-3.381%201.794c-.022.145-.061.29-.149.401-.133.166-.358.248-.69.251h-.002c-.133%200-.306-.26-.45-.621.417.091.854.07%201.291-.031zm-2.066-8.077a4.78%204.78%200%200%201-.775-.584c.172-.115.505-.254.88-.378l-.105.962zm-.331%202.302a10.32%2010.32%200%200%201-.828-.502c.202-.143.576-.328.984-.49l-.156.992zm-.45%202.157l-.701-.403c.214-.115.536-.249.891-.376a11.57%2011.57%200%200%201-.19.779zm-.181%201.716c.064.398.194.702.298.893-.194-.051-.435-.162-.736-.398.061-.119.224-.3.438-.495zM8.87%204.141c0%20.152-.123.276-.276.276s-.275-.124-.275-.276.123-.276.276-.276.275.124.275.276zm-.735-.389a1.15%201.15%200%200%200-.314.783%201.16%201.16%200%200%200%201.162%201.162c.457%200%20.842-.27%201.032-.653.026.117.042.238.042.362a1.68%201.68%200%200%201-1.679%201.679%201.68%201.68%200%200%201-1.679-1.679c0-.843.626-1.535%201.436-1.654zM5.059%205.406A1.68%201.68%200%200%201%203.38%207.085a1.68%201.68%200%200%201-1.679-1.679c0-.037.009-.072.011-.109.21.3.541.508.935.508a1.16%201.16%200%200%200%201.162-1.162%201.14%201.14%200%200%200-.474-.912c.015%200%20.03-.005.045-.005.926.001%201.679.754%201.679%201.68zM3.198%204.141c0%20.152-.123.276-.276.276s-.275-.124-.275-.276.123-.276.276-.276.275.124.275.276zM1.375%208.964c0-.52.103-1.035.288-1.52.466.394%201.06.64%201.717.64%201.144%200%202.116-.725%202.499-1.738.383%201.012%201.355%201.738%202.499%201.738.867%200%201.631-.421%202.121-1.062.307.605.478%201.267.478%201.942%200%202.486-2.153%204.51-4.801%204.51s-4.801-2.023-4.801-4.51zm24.342%2019.349c-.985.498-2.267.168-3.813-.979-3.073-2.281-5.453-3.199-7.813-.705-1.315%201.391-4.163%203.365-8.423.97-3.174-1.786-2.239-6.266-1.261-9.479l.146-.492c.276-1.02.395-2.457.444-3.268a6.11%206.11%200%200%200%201.18.115%206.01%206.01%200%200%200%202.536-.562l-.006.175c-.802.215-1.848.612-2.021%201.25-.079.295.021.601.274.837.219.203.415.364.598.501-.667.304-1.243.698-1.311%201.179-.02.144-.022.507.393.787.213.144.395.26.564.365-1.285.521-1.361.96-1.381%201.126-.018.142-.011.496.427.746l.854.489c-.473.389-.971.914-.999%201.429-.018.278.095.532.316.713.675.556%201.231.721%201.653.721.059%200%20.104-.014.158-.02.207.707.641%201.64%201.513%201.64h.013c.8-.008%201.236-.345%201.462-.626.173-.216.268-.457.325-.692.424.195.93.374%201.372.374.151%200%20.294-.021.423-.068.732-.27.944-.704.993-1.021.009-.061.003-.119.002-.179.266.086.538.147.789.147.15%200%20.294-.021.423-.069.542-.2.797-.489.914-.754.237.147.478.258.704.288.106.014.205.021.296.021.356%200%20.595-.101.767-.229.438.435%201.094.992%201.656%201.067.106.014.205.021.296.021a1.56%201.56%200%200%200%20.323-.035c.17.575.453%201.289.866%201.605.358.273.665.362.914.362a.99.99%200%200%200%20.421-.093%201.03%201.03%200%200%200%20.245-.164c.168.428.39.846.68%201.068.358.273.665.362.913.362a.99.99%200%200%200%20.421-.093c.317-.148.512-.448.639-.762.251.157.495.257.726.257.127%200%20.25-.024.37-.071.427-.17.706-.617.841-1.314.022-.015.047-.022.068-.038.067-.051.133-.104.196-.159-.443%201.486-1.107%202.761-2.086%203.257zM8.66%209.925a.5.5%200%201%200-1%200c0%20.653-.818%201.205-1.787%201.205s-1.787-.552-1.787-1.205a.5.5%200%201%200-1%200c0%201.216%201.25%202.205%202.787%202.205s2.787-.989%202.787-2.205zm4.4%2015.965l-.208.097c-2.661%201.258-4.708%201.436-6.086.527-1.542-1.017-1.88-3.19-1.844-4.198a.4.4%200%200%200-.385-.414c-.242-.029-.406.164-.414.385-.046%201.249.367%203.686%202.202%204.896.708.467%201.547.7%202.51.7%201.248%200%202.706-.392%204.362-1.174l.185-.086a.4.4%200%200%200%20.205-.527c-.089-.204-.326-.291-.527-.206zM9.547%202.292c.093.077.205.114.317.114a.5.5%200%200%200%20.318-.886L8.817.397a.5.5%200%200%200-.703.068.5.5%200%200%200%20.069.703l1.364%201.124zm-7.661-.065c.086%200%20.173-.022.253-.068l1.523-.893a.5.5%200%200%200-.506-.863l-1.523.892a.5.5%200%200%200-.179.685c.094.158.261.247.432.247z%22%20transform%3D%22matrix%28-1%200%200%201%2058%200%29%22%20fill%3D%22%233bb300%22/%3E%3Cpath%20d%3D%22M.3%2021.86V10.18q0-.46.02-.68.04-.22.18-.5.28-.54%201.34-.54%201.06%200%201.42.28.38.26.44.78.76-1.04%202.38-1.04%201.64%200%203.1%201.54%201.46%201.54%201.46%203.58%200%202.04-1.46%203.58-1.44%201.54-3.08%201.54-1.64%200-2.38-.92v4.04q0%20.46-.04.68-.02.22-.18.5-.14.3-.5.42-.36.12-.98.12-.62%200-1-.12-.36-.12-.52-.4-.14-.28-.18-.5-.02-.22-.02-.68zm3.96-9.42q-.46.54-.46%201.18%200%20.64.46%201.18.48.52%201.2.52.74%200%201.24-.52.52-.52.52-1.18%200-.66-.48-1.18-.48-.54-1.26-.54-.76%200-1.22.54zm14.741-8.36q.16-.3.54-.42.38-.12%201-.12.64%200%201.02.12.38.12.52.42.16.3.18.54.04.22.04.68v11.94q0%20.46-.04.7-.02.22-.18.5-.3.54-1.7.54-1.38%200-1.54-.98-.84.96-2.34.96-1.8%200-3.28-1.56-1.48-1.58-1.48-3.66%200-2.1%201.48-3.68%201.5-1.58%203.28-1.58%201.48%200%202.3%201v-4.2q0-.46.02-.68.04-.24.18-.52zm-3.24%2010.86q.52.54%201.26.54.74%200%201.22-.54.5-.54.5-1.18%200-.66-.48-1.22-.46-.56-1.26-.56-.8%200-1.28.56-.48.54-.48%201.2%200%20.66.52%201.2zm7.833-1.2q0-2.4%201.68-3.96%201.68-1.56%203.84-1.56%202.16%200%203.82%201.56%201.66%201.54%201.66%203.94%200%201.66-.86%202.96-.86%201.28-2.1%201.9-1.22.6-2.54.6-1.32%200-2.56-.64-1.24-.66-2.1-1.92-.84-1.28-.84-2.88zm4.18%201.44q.64.48%201.3.48.66%200%201.32-.5.66-.5.66-1.48%200-.98-.62-1.46-.62-.48-1.34-.48-.72%200-1.34.5-.62.5-.62%201.48%200%20.96.64%201.46zm11.412-1.44q0%20.84.56%201.32.56.46%201.18.46.64%200%201.18-.36.56-.38.9-.38.6%200%201.46%201.06.46.58.46%201.04%200%20.76-1.1%201.42-1.14.8-2.8.8-1.86%200-3.58-1.34-.82-.64-1.34-1.7-.52-1.08-.52-2.36%200-1.3.52-2.34.52-1.06%201.34-1.7%201.66-1.32%203.54-1.32.76%200%201.48.22.72.2%201.06.4l.32.2q.36.24.56.38.52.4.52.92%200%20.5-.42%201.14-.72%201.1-1.38%201.1-.38%200-1.08-.44-.36-.34-1.04-.34-.66%200-1.24.48-.58.48-.58%201.34z%22%20fill%3D%22green%22/%3E%3C/svg%3E"/>
        </a>
</div>
    </nav>
    <main class="pdoc">
            <section class="module-info">
                    <h1 class="modulename">
<a href="./../core.html">core</a><wbr>.retropie_installer    </h1>

                        <div class="docstring"><p>RetroPie 镜像下载和烧录工具</p>

<p>这是一个跨平台的Python脚本，用于自动化下载RetroPie镜像并烧录到SD卡。
支持Windows、Linux和macOS三平台，提供完整的自动化安装流程。</p>

<p>主要功能：</p>

<ul>
<li>自动检测系统依赖和必要工具</li>
<li>从RetroPie官网自动获取最新镜像下载链接</li>
<li>支持断点续传的镜像下载</li>
<li>自动解压.img.gz和.zip格式的镜像文件</li>
<li>智能扫描和列出可用的SD卡设备</li>
<li>安全的镜像烧录操作（Linux/macOS使用dd命令）</li>
<li>完整的操作日志记录和错误处理</li>
</ul>

<p>系统要求：</p>

<ul>
<li>Python 3.7+</li>
<li>Windows: 需要手动安装Win32DiskImager或balenaEtcher</li>
<li>Linux/macOS: 需要dd命令和sudo权限</li>
</ul>

<p>作者: AI Assistant
版本: 2.0.0
许可证: MIT</p>
</div>

                        <input id="mod-retropie_installer-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">

                        <label class="view-source-button" for="mod-retropie_installer-view-source"><span>View Source</span></label>

                        <div class="pdoc-code codehilite"><pre><span></span><span id="L-1"><a href="#L-1"><span class="linenos">  1</span></a><span class="ch">#!/usr/bin/env python3</span>
</span><span id="L-2"><a href="#L-2"><span class="linenos">  2</span></a><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-3"><a href="#L-3"><span class="linenos">  3</span></a><span class="sd">RetroPie 镜像下载和烧录工具</span>
</span><span id="L-4"><a href="#L-4"><span class="linenos">  4</span></a>
</span><span id="L-5"><a href="#L-5"><span class="linenos">  5</span></a><span class="sd">这是一个跨平台的Python脚本，用于自动化下载RetroPie镜像并烧录到SD卡。</span>
</span><span id="L-6"><a href="#L-6"><span class="linenos">  6</span></a><span class="sd">支持Windows、Linux和macOS三平台，提供完整的自动化安装流程。</span>
</span><span id="L-7"><a href="#L-7"><span class="linenos">  7</span></a>
</span><span id="L-8"><a href="#L-8"><span class="linenos">  8</span></a><span class="sd">主要功能：</span>
</span><span id="L-9"><a href="#L-9"><span class="linenos">  9</span></a><span class="sd">- 自动检测系统依赖和必要工具</span>
</span><span id="L-10"><a href="#L-10"><span class="linenos"> 10</span></a><span class="sd">- 从RetroPie官网自动获取最新镜像下载链接</span>
</span><span id="L-11"><a href="#L-11"><span class="linenos"> 11</span></a><span class="sd">- 支持断点续传的镜像下载</span>
</span><span id="L-12"><a href="#L-12"><span class="linenos"> 12</span></a><span class="sd">- 自动解压.img.gz和.zip格式的镜像文件</span>
</span><span id="L-13"><a href="#L-13"><span class="linenos"> 13</span></a><span class="sd">- 智能扫描和列出可用的SD卡设备</span>
</span><span id="L-14"><a href="#L-14"><span class="linenos"> 14</span></a><span class="sd">- 安全的镜像烧录操作（Linux/macOS使用dd命令）</span>
</span><span id="L-15"><a href="#L-15"><span class="linenos"> 15</span></a><span class="sd">- 完整的操作日志记录和错误处理</span>
</span><span id="L-16"><a href="#L-16"><span class="linenos"> 16</span></a>
</span><span id="L-17"><a href="#L-17"><span class="linenos"> 17</span></a><span class="sd">系统要求：</span>
</span><span id="L-18"><a href="#L-18"><span class="linenos"> 18</span></a><span class="sd">- Python 3.7+</span>
</span><span id="L-19"><a href="#L-19"><span class="linenos"> 19</span></a><span class="sd">- Windows: 需要手动安装Win32DiskImager或balenaEtcher</span>
</span><span id="L-20"><a href="#L-20"><span class="linenos"> 20</span></a><span class="sd">- Linux/macOS: 需要dd命令和sudo权限</span>
</span><span id="L-21"><a href="#L-21"><span class="linenos"> 21</span></a>
</span><span id="L-22"><a href="#L-22"><span class="linenos"> 22</span></a><span class="sd">作者: AI Assistant</span>
</span><span id="L-23"><a href="#L-23"><span class="linenos"> 23</span></a><span class="sd">版本: 2.0.0</span>
</span><span id="L-24"><a href="#L-24"><span class="linenos"> 24</span></a><span class="sd">许可证: MIT</span>
</span><span id="L-25"><a href="#L-25"><span class="linenos"> 25</span></a><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-26"><a href="#L-26"><span class="linenos"> 26</span></a>
</span><span id="L-27"><a href="#L-27"><span class="linenos"> 27</span></a><span class="kn">import</span> <span class="nn">os</span>
</span><span id="L-28"><a href="#L-28"><span class="linenos"> 28</span></a><span class="kn">import</span> <span class="nn">sys</span>
</span><span id="L-29"><a href="#L-29"><span class="linenos"> 29</span></a><span class="kn">import</span> <span class="nn">platform</span>
</span><span id="L-30"><a href="#L-30"><span class="linenos"> 30</span></a><span class="kn">import</span> <span class="nn">subprocess</span>
</span><span id="L-31"><a href="#L-31"><span class="linenos"> 31</span></a><span class="kn">import</span> <span class="nn">requests</span>
</span><span id="L-32"><a href="#L-32"><span class="linenos"> 32</span></a><span class="kn">import</span> <span class="nn">zipfile</span>
</span><span id="L-33"><a href="#L-33"><span class="linenos"> 33</span></a><span class="kn">import</span> <span class="nn">tarfile</span>
</span><span id="L-34"><a href="#L-34"><span class="linenos"> 34</span></a><span class="kn">import</span> <span class="nn">hashlib</span>
</span><span id="L-35"><a href="#L-35"><span class="linenos"> 35</span></a><span class="kn">import</span> <span class="nn">time</span>
</span><span id="L-36"><a href="#L-36"><span class="linenos"> 36</span></a><span class="kn">from</span> <span class="nn">pathlib</span> <span class="kn">import</span> <span class="n">Path</span>
</span><span id="L-37"><a href="#L-37"><span class="linenos"> 37</span></a><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">List</span><span class="p">,</span> <span class="n">Tuple</span>
</span><span id="L-38"><a href="#L-38"><span class="linenos"> 38</span></a><span class="kn">import</span> <span class="nn">argparse</span>
</span><span id="L-39"><a href="#L-39"><span class="linenos"> 39</span></a><span class="n">sys</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">insert</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">abspath</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">dirname</span><span class="p">(</span><span class="vm">__file__</span><span class="p">)))</span>
</span><span id="L-40"><a href="#L-40"><span class="linenos"> 40</span></a><span class="kn">from</span> <span class="nn">logger_config</span> <span class="kn">import</span> <span class="n">get_logger</span>
</span><span id="L-41"><a href="#L-41"><span class="linenos"> 41</span></a>
</span><span id="L-42"><a href="#L-42"><span class="linenos"> 42</span></a><span class="c1"># 配置日志</span>
</span><span id="L-43"><a href="#L-43"><span class="linenos"> 43</span></a><span class="n">logger</span> <span class="o">=</span> <span class="n">get_logger</span><span class="p">(</span><span class="s2">&quot;retropie_installer&quot;</span><span class="p">,</span> <span class="s2">&quot;retropie_installer.log&quot;</span><span class="p">)</span>
</span><span id="L-44"><a href="#L-44"><span class="linenos"> 44</span></a>
</span><span id="L-45"><a href="#L-45"><span class="linenos"> 45</span></a><span class="k">try</span><span class="p">:</span>
</span><span id="L-46"><a href="#L-46"><span class="linenos"> 46</span></a>    <span class="kn">import</span> <span class="nn">requests</span>
</span><span id="L-47"><a href="#L-47"><span class="linenos"> 47</span></a><span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
</span><span id="L-48"><a href="#L-48"><span class="linenos"> 48</span></a>    <span class="k">raise</span> <span class="ne">ImportError</span><span class="p">(</span><span class="s2">&quot;缺少requests库，请运行 pip install requests 安装。&quot;</span><span class="p">);</span>
</span><span id="L-49"><a href="#L-49"><span class="linenos"> 49</span></a>
</span><span id="L-50"><a href="#L-50"><span class="linenos"> 50</span></a><span class="k">class</span> <span class="nc">RetroPieInstaller</span><span class="p">:</span>
</span><span id="L-51"><a href="#L-51"><span class="linenos"> 51</span></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-52"><a href="#L-52"><span class="linenos"> 52</span></a><span class="sd">    RetroPie 镜像下载和烧录工具类</span>
</span><span id="L-53"><a href="#L-53"><span class="linenos"> 53</span></a><span class="sd">    </span>
</span><span id="L-54"><a href="#L-54"><span class="linenos"> 54</span></a><span class="sd">    提供完整的RetroPie镜像自动化安装流程，包括：</span>
</span><span id="L-55"><a href="#L-55"><span class="linenos"> 55</span></a><span class="sd">    - 系统依赖检查</span>
</span><span id="L-56"><a href="#L-56"><span class="linenos"> 56</span></a><span class="sd">    - 镜像下载和解压</span>
</span><span id="L-57"><a href="#L-57"><span class="linenos"> 57</span></a><span class="sd">    - 磁盘设备管理</span>
</span><span id="L-58"><a href="#L-58"><span class="linenos"> 58</span></a><span class="sd">    - 镜像烧录操作</span>
</span><span id="L-59"><a href="#L-59"><span class="linenos"> 59</span></a><span class="sd">    </span>
</span><span id="L-60"><a href="#L-60"><span class="linenos"> 60</span></a><span class="sd">    属性:</span>
</span><span id="L-61"><a href="#L-61"><span class="linenos"> 61</span></a><span class="sd">        system (str): 当前操作系统名称</span>
</span><span id="L-62"><a href="#L-62"><span class="linenos"> 62</span></a><span class="sd">        retropie_url (str): RetroPie官网URL</span>
</span><span id="L-63"><a href="#L-63"><span class="linenos"> 63</span></a><span class="sd">        download_dir (Path): 下载目录路径</span>
</span><span id="L-64"><a href="#L-64"><span class="linenos"> 64</span></a><span class="sd">    &quot;&quot;&quot;</span>
</span><span id="L-65"><a href="#L-65"><span class="linenos"> 65</span></a>    
</span><span id="L-66"><a href="#L-66"><span class="linenos"> 66</span></a>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
</span><span id="L-67"><a href="#L-67"><span class="linenos"> 67</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-68"><a href="#L-68"><span class="linenos"> 68</span></a><span class="sd">        初始化RetroPie安装器</span>
</span><span id="L-69"><a href="#L-69"><span class="linenos"> 69</span></a><span class="sd">        </span>
</span><span id="L-70"><a href="#L-70"><span class="linenos"> 70</span></a><span class="sd">        设置基本配置和创建必要的目录结构。</span>
</span><span id="L-71"><a href="#L-71"><span class="linenos"> 71</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="L-72"><a href="#L-72"><span class="linenos"> 72</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">=</span> <span class="n">platform</span><span class="o">.</span><span class="n">system</span><span class="p">()</span>
</span><span id="L-73"><a href="#L-73"><span class="linenos"> 73</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">retropie_url</span> <span class="o">=</span> <span class="s2">&quot;https://retropie.org.uk/download/&quot;</span>
</span><span id="L-74"><a href="#L-74"><span class="linenos"> 74</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s2">&quot;downloads&quot;</span><span class="p">)</span>
</span><span id="L-75"><a href="#L-75"><span class="linenos"> 75</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="L-76"><a href="#L-76"><span class="linenos"> 76</span></a>        
</span><span id="L-77"><a href="#L-77"><span class="linenos"> 77</span></a>    <span class="k">def</span> <span class="nf">check_dependencies</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-78"><a href="#L-78"><span class="linenos"> 78</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-79"><a href="#L-79"><span class="linenos"> 79</span></a><span class="sd">        检查系统依赖是否满足</span>
</span><span id="L-80"><a href="#L-80"><span class="linenos"> 80</span></a><span class="sd">        </span>
</span><span id="L-81"><a href="#L-81"><span class="linenos"> 81</span></a><span class="sd">        根据操作系统类型检查必要的工具和权限。</span>
</span><span id="L-82"><a href="#L-82"><span class="linenos"> 82</span></a><span class="sd">        </span>
</span><span id="L-83"><a href="#L-83"><span class="linenos"> 83</span></a><span class="sd">        Returns:</span>
</span><span id="L-84"><a href="#L-84"><span class="linenos"> 84</span></a><span class="sd">            bool: 依赖检查是否通过</span>
</span><span id="L-85"><a href="#L-85"><span class="linenos"> 85</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="L-86"><a href="#L-86"><span class="linenos"> 86</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;检查系统依赖...&quot;</span><span class="p">)</span>
</span><span id="L-87"><a href="#L-87"><span class="linenos"> 87</span></a>        
</span><span id="L-88"><a href="#L-88"><span class="linenos"> 88</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Windows&quot;</span><span class="p">:</span>
</span><span id="L-89"><a href="#L-89"><span class="linenos"> 89</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_windows_dependencies</span><span class="p">()</span>
</span><span id="L-90"><a href="#L-90"><span class="linenos"> 90</span></a>        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;Linux&quot;</span><span class="p">,</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">]:</span>  <span class="c1"># Darwin = macOS</span>
</span><span id="L-91"><a href="#L-91"><span class="linenos"> 91</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_unix_dependencies</span><span class="p">()</span>
</span><span id="L-92"><a href="#L-92"><span class="linenos"> 92</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="L-93"><a href="#L-93"><span class="linenos"> 93</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;不支持的操作系统: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">system</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-94"><a href="#L-94"><span class="linenos"> 94</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-95"><a href="#L-95"><span class="linenos"> 95</span></a>    
</span><span id="L-96"><a href="#L-96"><span class="linenos"> 96</span></a>    <span class="k">def</span> <span class="nf">_check_windows_dependencies</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-97"><a href="#L-97"><span class="linenos"> 97</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-98"><a href="#L-98"><span class="linenos"> 98</span></a><span class="sd">        检查Windows系统依赖</span>
</span><span id="L-99"><a href="#L-99"><span class="linenos"> 99</span></a><span class="sd">        </span>
</span><span id="L-100"><a href="#L-100"><span class="linenos">100</span></a><span class="sd">        检查是否有可用的镜像烧录工具。</span>
</span><span id="L-101"><a href="#L-101"><span class="linenos">101</span></a><span class="sd">        </span>
</span><span id="L-102"><a href="#L-102"><span class="linenos">102</span></a><span class="sd">        Returns:</span>
</span><span id="L-103"><a href="#L-103"><span class="linenos">103</span></a><span class="sd">            bool: Windows依赖检查结果</span>
</span><span id="L-104"><a href="#L-104"><span class="linenos">104</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="L-105"><a href="#L-105"><span class="linenos">105</span></a>        <span class="c1"># 检查是否有Win32DiskImager或类似工具</span>
</span><span id="L-106"><a href="#L-106"><span class="linenos">106</span></a>        <span class="n">possible_tools</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="L-107"><a href="#L-107"><span class="linenos">107</span></a>            <span class="s2">&quot;Win32DiskImager.exe&quot;</span><span class="p">,</span>
</span><span id="L-108"><a href="#L-108"><span class="linenos">108</span></a>            <span class="s2">&quot;balenaEtcher.exe&quot;</span><span class="p">,</span>
</span><span id="L-109"><a href="#L-109"><span class="linenos">109</span></a>            <span class="s2">&quot;Rufus.exe&quot;</span>
</span><span id="L-110"><a href="#L-110"><span class="linenos">110</span></a>        <span class="p">]</span>
</span><span id="L-111"><a href="#L-111"><span class="linenos">111</span></a>        
</span><span id="L-112"><a href="#L-112"><span class="linenos">112</span></a>        <span class="k">for</span> <span class="n">tool</span> <span class="ow">in</span> <span class="n">possible_tools</span><span class="p">:</span>
</span><span id="L-113"><a href="#L-113"><span class="linenos">113</span></a>            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_find_executable</span><span class="p">(</span><span class="n">tool</span><span class="p">):</span>
</span><span id="L-114"><a href="#L-114"><span class="linenos">114</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到烧录工具: </span><span class="si">{</span><span class="n">tool</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-115"><a href="#L-115"><span class="linenos">115</span></a>                <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-116"><a href="#L-116"><span class="linenos">116</span></a>        
</span><span id="L-117"><a href="#L-117"><span class="linenos">117</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;未找到烧录工具，请手动安装 Win32DiskImager 或 balenaEtcher&quot;</span><span class="p">)</span>
</span><span id="L-118"><a href="#L-118"><span class="linenos">118</span></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-119"><a href="#L-119"><span class="linenos">119</span></a>    
</span><span id="L-120"><a href="#L-120"><span class="linenos">120</span></a>    <span class="k">def</span> <span class="nf">_check_unix_dependencies</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-121"><a href="#L-121"><span class="linenos">121</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-122"><a href="#L-122"><span class="linenos">122</span></a><span class="sd">        检查Unix系统依赖（Linux/macOS）</span>
</span><span id="L-123"><a href="#L-123"><span class="linenos">123</span></a><span class="sd">        </span>
</span><span id="L-124"><a href="#L-124"><span class="linenos">124</span></a><span class="sd">        检查dd命令是否可用。</span>
</span><span id="L-125"><a href="#L-125"><span class="linenos">125</span></a><span class="sd">        </span>
</span><span id="L-126"><a href="#L-126"><span class="linenos">126</span></a><span class="sd">        Returns:</span>
</span><span id="L-127"><a href="#L-127"><span class="linenos">127</span></a><span class="sd">            bool: Unix依赖检查结果</span>
</span><span id="L-128"><a href="#L-128"><span class="linenos">128</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="L-129"><a href="#L-129"><span class="linenos">129</span></a>        <span class="c1"># 检查dd命令</span>
</span><span id="L-130"><a href="#L-130"><span class="linenos">130</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_run_command</span><span class="p">([</span><span class="s2">&quot;which&quot;</span><span class="p">,</span> <span class="s2">&quot;dd&quot;</span><span class="p">])[</span><span class="mi">0</span><span class="p">]</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="L-131"><a href="#L-131"><span class="linenos">131</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;找到 dd 命令&quot;</span><span class="p">)</span>
</span><span id="L-132"><a href="#L-132"><span class="linenos">132</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-133"><a href="#L-133"><span class="linenos">133</span></a>        
</span><span id="L-134"><a href="#L-134"><span class="linenos">134</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;未找到 dd 命令&quot;</span><span class="p">)</span>
</span><span id="L-135"><a href="#L-135"><span class="linenos">135</span></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-136"><a href="#L-136"><span class="linenos">136</span></a>    
</span><span id="L-137"><a href="#L-137"><span class="linenos">137</span></a>    <span class="k">def</span> <span class="nf">_find_executable</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">name</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="L-138"><a href="#L-138"><span class="linenos">138</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-139"><a href="#L-139"><span class="linenos">139</span></a><span class="sd">        查找可执行文件</span>
</span><span id="L-140"><a href="#L-140"><span class="linenos">140</span></a><span class="sd">        </span>
</span><span id="L-141"><a href="#L-141"><span class="linenos">141</span></a><span class="sd">        Args:</span>
</span><span id="L-142"><a href="#L-142"><span class="linenos">142</span></a><span class="sd">            name (str): 可执行文件名</span>
</span><span id="L-143"><a href="#L-143"><span class="linenos">143</span></a><span class="sd">            </span>
</span><span id="L-144"><a href="#L-144"><span class="linenos">144</span></a><span class="sd">        Returns:</span>
</span><span id="L-145"><a href="#L-145"><span class="linenos">145</span></a><span class="sd">            Optional[str]: 可执行文件的完整路径，如果未找到则返回None</span>
</span><span id="L-146"><a href="#L-146"><span class="linenos">146</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="L-147"><a href="#L-147"><span class="linenos">147</span></a>        <span class="k">for</span> <span class="n">path</span> <span class="ow">in</span> <span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;PATH&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">pathsep</span><span class="p">):</span>
</span><span id="L-148"><a href="#L-148"><span class="linenos">148</span></a>            <span class="n">exe_path</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">path</span><span class="p">)</span> <span class="o">/</span> <span class="n">name</span>
</span><span id="L-149"><a href="#L-149"><span class="linenos">149</span></a>            <span class="k">if</span> <span class="n">exe_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-150"><a href="#L-150"><span class="linenos">150</span></a>                <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">exe_path</span><span class="p">)</span>
</span><span id="L-151"><a href="#L-151"><span class="linenos">151</span></a>        <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-152"><a href="#L-152"><span class="linenos">152</span></a>    
</span><span id="L-153"><a href="#L-153"><span class="linenos">153</span></a>    <span class="k">def</span> <span class="nf">_run_command</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">cmd</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="n">check</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]:</span>
</span><span id="L-154"><a href="#L-154"><span class="linenos">154</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-155"><a href="#L-155"><span class="linenos">155</span></a><span class="sd">        运行命令并返回结果</span>
</span><span id="L-156"><a href="#L-156"><span class="linenos">156</span></a><span class="sd">        </span>
</span><span id="L-157"><a href="#L-157"><span class="linenos">157</span></a><span class="sd">        Args:</span>
</span><span id="L-158"><a href="#L-158"><span class="linenos">158</span></a><span class="sd">            cmd (List[str]): 要执行的命令列表</span>
</span><span id="L-159"><a href="#L-159"><span class="linenos">159</span></a><span class="sd">            check (bool): 是否在命令失败时抛出异常</span>
</span><span id="L-160"><a href="#L-160"><span class="linenos">160</span></a><span class="sd">            </span>
</span><span id="L-161"><a href="#L-161"><span class="linenos">161</span></a><span class="sd">        Returns:</span>
</span><span id="L-162"><a href="#L-162"><span class="linenos">162</span></a><span class="sd">            Tuple[int, str, str]: (返回码, 标准输出, 标准错误)</span>
</span><span id="L-163"><a href="#L-163"><span class="linenos">163</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="L-164"><a href="#L-164"><span class="linenos">164</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-165"><a href="#L-165"><span class="linenos">165</span></a>            <span class="n">result</span> <span class="o">=</span> <span class="n">subprocess</span><span class="o">.</span><span class="n">run</span><span class="p">(</span>
</span><span id="L-166"><a href="#L-166"><span class="linenos">166</span></a>                <span class="n">cmd</span><span class="p">,</span> 
</span><span id="L-167"><a href="#L-167"><span class="linenos">167</span></a>                <span class="n">capture_output</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> 
</span><span id="L-168"><a href="#L-168"><span class="linenos">168</span></a>                <span class="n">text</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> 
</span><span id="L-169"><a href="#L-169"><span class="linenos">169</span></a>                <span class="n">check</span><span class="o">=</span><span class="n">check</span>
</span><span id="L-170"><a href="#L-170"><span class="linenos">170</span></a>            <span class="p">)</span>
</span><span id="L-171"><a href="#L-171"><span class="linenos">171</span></a>            <span class="k">return</span> <span class="n">result</span><span class="o">.</span><span class="n">returncode</span><span class="p">,</span> <span class="n">result</span><span class="o">.</span><span class="n">stdout</span><span class="p">,</span> <span class="n">result</span><span class="o">.</span><span class="n">stderr</span>
</span><span id="L-172"><a href="#L-172"><span class="linenos">172</span></a>        <span class="k">except</span> <span class="n">subprocess</span><span class="o">.</span><span class="n">CalledProcessError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-173"><a href="#L-173"><span class="linenos">173</span></a>            <span class="k">return</span> <span class="n">e</span><span class="o">.</span><span class="n">returncode</span><span class="p">,</span> <span class="n">e</span><span class="o">.</span><span class="n">stdout</span><span class="p">,</span> <span class="n">e</span><span class="o">.</span><span class="n">stderr</span>
</span><span id="L-174"><a href="#L-174"><span class="linenos">174</span></a>    
</span><span id="L-175"><a href="#L-175"><span class="linenos">175</span></a>    <span class="k">def</span> <span class="nf">get_retropie_download_url</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="L-176"><a href="#L-176"><span class="linenos">176</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-177"><a href="#L-177"><span class="linenos">177</span></a><span class="sd">        获取RetroPie镜像下载链接</span>
</span><span id="L-178"><a href="#L-178"><span class="linenos">178</span></a><span class="sd">        </span>
</span><span id="L-179"><a href="#L-179"><span class="linenos">179</span></a><span class="sd">        From RetroPie官网解析页面内容，查找适合树莓派4B的镜像下载链接。</span>
</span><span id="L-180"><a href="#L-180"><span class="linenos">180</span></a><span class="sd">        </span>
</span><span id="L-181"><a href="#L-181"><span class="linenos">181</span></a><span class="sd">        Returns:</span>
</span><span id="L-182"><a href="#L-182"><span class="linenos">182</span></a><span class="sd">            Optional[str]: 下载链接URL，如果未找到则返回None</span>
</span><span id="L-183"><a href="#L-183"><span class="linenos">183</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="L-184"><a href="#L-184"><span class="linenos">184</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;获取RetroPie下载链接...&quot;</span><span class="p">)</span>
</span><span id="L-185"><a href="#L-185"><span class="linenos">185</span></a>        
</span><span id="L-186"><a href="#L-186"><span class="linenos">186</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-187"><a href="#L-187"><span class="linenos">187</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">retropie_url</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="L-188"><a href="#L-188"><span class="linenos">188</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="L-189"><a href="#L-189"><span class="linenos">189</span></a>            
</span><span id="L-190"><a href="#L-190"><span class="linenos">190</span></a>            <span class="c1"># 解析页面内容，查找树莓派4B的镜像链接</span>
</span><span id="L-191"><a href="#L-191"><span class="linenos">191</span></a>            <span class="n">content</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">text</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span>
</span><span id="L-192"><a href="#L-192"><span class="linenos">192</span></a>            
</span><span id="L-193"><a href="#L-193"><span class="linenos">193</span></a>            <span class="c1"># 常见的RetroPie镜像文件名模式</span>
</span><span id="L-194"><a href="#L-194"><span class="linenos">194</span></a>            <span class="n">patterns</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="L-195"><a href="#L-195"><span class="linenos">195</span></a>                <span class="s2">&quot;retropie-buster-4.8-rpi4_400.img.gz&quot;</span><span class="p">,</span>
</span><span id="L-196"><a href="#L-196"><span class="linenos">196</span></a>                <span class="s2">&quot;retropie-buster-4.8-rpi4.img.gz&quot;</span><span class="p">,</span>
</span><span id="L-197"><a href="#L-197"><span class="linenos">197</span></a>                <span class="s2">&quot;retropie-4.8-rpi4.img.gz&quot;</span><span class="p">,</span>
</span><span id="L-198"><a href="#L-198"><span class="linenos">198</span></a>                <span class="s2">&quot;retropie-buster-rpi4.img.gz&quot;</span>
</span><span id="L-199"><a href="#L-199"><span class="linenos">199</span></a>            <span class="p">]</span>
</span><span id="L-200"><a href="#L-200"><span class="linenos">200</span></a>            
</span><span id="L-201"><a href="#L-201"><span class="linenos">201</span></a>            <span class="k">for</span> <span class="n">pattern</span> <span class="ow">in</span> <span class="n">patterns</span><span class="p">:</span>
</span><span id="L-202"><a href="#L-202"><span class="linenos">202</span></a>                <span class="k">if</span> <span class="n">pattern</span> <span class="ow">in</span> <span class="n">content</span><span class="p">:</span>
</span><span id="L-203"><a href="#L-203"><span class="linenos">203</span></a>                    <span class="c1"># 构建完整下载URL</span>
</span><span id="L-204"><a href="#L-204"><span class="linenos">204</span></a>                    <span class="n">download_url</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;https://github.com/RetroPie/RetroPie-Setup/releases/download/4.8/</span><span class="si">{</span><span class="n">pattern</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="L-205"><a href="#L-205"><span class="linenos">205</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到下载链接: </span><span class="si">{</span><span class="n">download_url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-206"><a href="#L-206"><span class="linenos">206</span></a>                    <span class="k">return</span> <span class="n">download_url</span>
</span><span id="L-207"><a href="#L-207"><span class="linenos">207</span></a>            
</span><span id="L-208"><a href="#L-208"><span class="linenos">208</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;未找到合适的镜像链接，请手动下载&quot;</span><span class="p">)</span>
</span><span id="L-209"><a href="#L-209"><span class="linenos">209</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-210"><a href="#L-210"><span class="linenos">210</span></a>            
</span><span id="L-211"><a href="#L-211"><span class="linenos">211</span></a>        <span class="k">except</span> <span class="n">requests</span><span class="o">.</span><span class="n">RequestException</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-212"><a href="#L-212"><span class="linenos">212</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;获取下载链接失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-213"><a href="#L-213"><span class="linenos">213</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-214"><a href="#L-214"><span class="linenos">214</span></a>    
</span><span id="L-215"><a href="#L-215"><span class="linenos">215</span></a>    <span class="k">def</span> <span class="nf">download_image</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">url</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Path</span><span class="p">]:</span>
</span><span id="L-216"><a href="#L-216"><span class="linenos">216</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="L-217"><a href="#L-217"><span class="linenos">217</span></a><span class="sd">        下载RetroPie镜像</span>
</span><span id="L-218"><a href="#L-218"><span class="linenos">218</span></a><span class="sd">        </span>
</span><span id="L-219"><a href="#L-219"><span class="linenos">219</span></a><span class="sd">        Support断点续传的镜像下载，显示下载进度。</span>
</span><span id="L-220"><a href="#L-220"><span class="linenos">220</span></a><span class="sd">        </span>
</span><span id="L-221"><a href="#L-221"><span class="linenos">221</span></a><span class="sd">        Args:</span>
</span><span id="L-222"><a href="#L-222"><span class="linenos">222</span></a><span class="sd">            url (str): 镜像下载链接</span>
</span><span id="L-223"><a href="#L-223"><span class="linenos">223</span></a><span class="sd">            </span>
</span><span id="L-224"><a href="#L-224"><span class="linenos">224</span></a><span class="sd">        Returns:</span>
</span><span id="L-225"><a href="#L-225"><span class="linenos">225</span></a><span class="sd">            Optional[Path]: 下载文件的路径，如果下载失败则返回None</span>
</span><span id="L-226"><a href="#L-226"><span class="linenos">226</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="L-227"><a href="#L-227"><span class="linenos">227</span></a>        <span class="n">filename</span> <span class="o">=</span> <span class="n">url</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">)[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
</span><span id="L-228"><a href="#L-228"><span class="linenos">228</span></a>        <span class="n">file_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="n">filename</span>
</span><span id="L-229"><a href="#L-229"><span class="linenos">229</span></a>        
</span><span id="L-230"><a href="#L-230"><span class="linenos">230</span></a>        <span class="k">if</span> <span class="n">file_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-231"><a href="#L-231"><span class="linenos">231</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像文件已存在: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-232"><a href="#L-232"><span class="linenos">232</span></a>            <span class="k">return</span> <span class="n">file_path</span>
</span><span id="L-233"><a href="#L-233"><span class="linenos">233</span></a>        
</span><span id="L-234"><a href="#L-234"><span class="linenos">234</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;开始下载镜像: </span><span class="si">{</span><span class="n">url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-235"><a href="#L-235"><span class="linenos">235</span></a>        
</span><span id="L-236"><a href="#L-236"><span class="linenos">236</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-237"><a href="#L-237"><span class="linenos">237</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">stream</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="L-238"><a href="#L-238"><span class="linenos">238</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="L-239"><a href="#L-239"><span class="linenos">239</span></a>            
</span><span id="L-240"><a href="#L-240"><span class="linenos">240</span></a>            <span class="n">total_size</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">headers</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;content-length&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
</span><span id="L-241"><a href="#L-241"><span class="linenos">241</span></a>            <span class="n">downloaded</span> <span class="o">=</span> <span class="mi">0</span>
</span><span id="L-242"><a href="#L-242"><span class="linenos">242</span></a>            
</span><span id="L-243"><a href="#L-243"><span class="linenos">243</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">file_path</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="L-244"><a href="#L-244"><span class="linenos">244</span></a>                <span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="n">response</span><span class="o">.</span><span class="n">iter_content</span><span class="p">(</span><span class="n">chunk_size</span><span class="o">=</span><span class="mi">8192</span><span class="p">):</span>
</span><span id="L-245"><a href="#L-245"><span class="linenos">245</span></a>                    <span class="k">if</span> <span class="n">chunk</span><span class="p">:</span>
</span><span id="L-246"><a href="#L-246"><span class="linenos">246</span></a>                        <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</span><span id="L-247"><a href="#L-247"><span class="linenos">247</span></a>                        <span class="n">downloaded</span> <span class="o">+=</span> <span class="nb">len</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</span><span id="L-248"><a href="#L-248"><span class="linenos">248</span></a>                        
</span><span id="L-249"><a href="#L-249"><span class="linenos">249</span></a>                        <span class="k">if</span> <span class="n">total_size</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="L-250"><a href="#L-250"><span class="linenos">250</span></a>                            <span class="n">progress</span> <span class="o">=</span> <span class="p">(</span><span class="n">downloaded</span> <span class="o">/</span> <span class="n">total_size</span><span class="p">)</span> <span class="o">*</span> <span class="mi">100</span>
</span><span id="L-251"><a href="#L-251"><span class="linenos">251</span></a>                            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="se">\r</span><span class="s2">下载进度: </span><span class="si">{</span><span class="n">progress</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">%&quot;</span><span class="p">,</span> <span class="n">end</span><span class="o">=</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">flush</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="L-252"><a href="#L-252"><span class="linenos">252</span></a>            
</span><span id="L-253"><a href="#L-253"><span class="linenos">253</span></a>            <span class="nb">print</span><span class="p">()</span>  <span class="c1"># 换行</span>
</span><span id="L-254"><a href="#L-254"><span class="linenos">254</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载完成: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-255"><a href="#L-255"><span class="linenos">255</span></a>            <span class="k">return</span> <span class="n">file_path</span>
</span><span id="L-256"><a href="#L-256"><span class="linenos">256</span></a>            
</span><span id="L-257"><a href="#L-257"><span class="linenos">257</span></a>        <span class="k">except</span> <span class="n">requests</span><span class="o">.</span><span class="n">RequestException</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-258"><a href="#L-258"><span class="linenos">258</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-259"><a href="#L-259"><span class="linenos">259</span></a>            <span class="k">if</span> <span class="n">file_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-260"><a href="#L-260"><span class="linenos">260</span></a>                <span class="n">file_path</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
</span><span id="L-261"><a href="#L-261"><span class="linenos">261</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-262"><a href="#L-262"><span class="linenos">262</span></a>    
</span><span id="L-263"><a href="#L-263"><span class="linenos">263</span></a>    <span class="k">def</span> <span class="nf">extract_image</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">archive_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Path</span><span class="p">]:</span>
</span><span id="L-264"><a href="#L-264"><span class="linenos">264</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;解压镜像文件&quot;&quot;&quot;</span>
</span><span id="L-265"><a href="#L-265"><span class="linenos">265</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压镜像文件: </span><span class="si">{</span><span class="n">archive_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-266"><a href="#L-266"><span class="linenos">266</span></a>        
</span><span id="L-267"><a href="#L-267"><span class="linenos">267</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-268"><a href="#L-268"><span class="linenos">268</span></a>            <span class="k">if</span> <span class="n">archive_path</span><span class="o">.</span><span class="n">suffix</span> <span class="o">==</span> <span class="s1">&#39;.gz&#39;</span><span class="p">:</span>
</span><span id="L-269"><a href="#L-269"><span class="linenos">269</span></a>                <span class="c1"># 处理 .img.gz 文件</span>
</span><span id="L-270"><a href="#L-270"><span class="linenos">270</span></a>                <span class="n">img_path</span> <span class="o">=</span> <span class="n">archive_path</span><span class="o">.</span><span class="n">with_suffix</span><span class="p">(</span><span class="s1">&#39;&#39;</span><span class="p">)</span>
</span><span id="L-271"><a href="#L-271"><span class="linenos">271</span></a>                <span class="k">if</span> <span class="n">img_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-272"><a href="#L-272"><span class="linenos">272</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像文件已存在: </span><span class="si">{</span><span class="n">img_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-273"><a href="#L-273"><span class="linenos">273</span></a>                    <span class="k">return</span> <span class="n">img_path</span>
</span><span id="L-274"><a href="#L-274"><span class="linenos">274</span></a>                
</span><span id="L-275"><a href="#L-275"><span class="linenos">275</span></a>                <span class="kn">import</span> <span class="nn">gzip</span>
</span><span id="L-276"><a href="#L-276"><span class="linenos">276</span></a>                <span class="k">with</span> <span class="n">gzip</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="n">archive_path</span><span class="p">,</span> <span class="s1">&#39;rb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f_in</span><span class="p">:</span>
</span><span id="L-277"><a href="#L-277"><span class="linenos">277</span></a>                    <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">img_path</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f_out</span><span class="p">:</span>
</span><span id="L-278"><a href="#L-278"><span class="linenos">278</span></a>                        <span class="n">f_out</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">f_in</span><span class="o">.</span><span class="n">read</span><span class="p">())</span>
</span><span id="L-279"><a href="#L-279"><span class="linenos">279</span></a>                
</span><span id="L-280"><a href="#L-280"><span class="linenos">280</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压完成: </span><span class="si">{</span><span class="n">img_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-281"><a href="#L-281"><span class="linenos">281</span></a>                <span class="k">return</span> <span class="n">img_path</span>
</span><span id="L-282"><a href="#L-282"><span class="linenos">282</span></a>                
</span><span id="L-283"><a href="#L-283"><span class="linenos">283</span></a>            <span class="k">elif</span> <span class="n">archive_path</span><span class="o">.</span><span class="n">suffix</span> <span class="o">==</span> <span class="s1">&#39;.zip&#39;</span><span class="p">:</span>
</span><span id="L-284"><a href="#L-284"><span class="linenos">284</span></a>                <span class="c1"># 处理 .zip 文件</span>
</span><span id="L-285"><a href="#L-285"><span class="linenos">285</span></a>                <span class="k">with</span> <span class="n">zipfile</span><span class="o">.</span><span class="n">ZipFile</span><span class="p">(</span><span class="n">archive_path</span><span class="p">,</span> <span class="s1">&#39;r&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">zip_ref</span><span class="p">:</span>
</span><span id="L-286"><a href="#L-286"><span class="linenos">286</span></a>                    <span class="c1"># 查找 .img 文件</span>
</span><span id="L-287"><a href="#L-287"><span class="linenos">287</span></a>                    <span class="n">img_files</span> <span class="o">=</span> <span class="p">[</span><span class="n">f</span> <span class="k">for</span> <span class="n">f</span> <span class="ow">in</span> <span class="n">zip_ref</span><span class="o">.</span><span class="n">namelist</span><span class="p">()</span> <span class="k">if</span> <span class="n">f</span><span class="o">.</span><span class="n">endswith</span><span class="p">(</span><span class="s1">&#39;.img&#39;</span><span class="p">)]</span>
</span><span id="L-288"><a href="#L-288"><span class="linenos">288</span></a>                    <span class="k">if</span> <span class="ow">not</span> <span class="n">img_files</span><span class="p">:</span>
</span><span id="L-289"><a href="#L-289"><span class="linenos">289</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;ZIP文件中未找到.img文件&quot;</span><span class="p">)</span>
</span><span id="L-290"><a href="#L-290"><span class="linenos">290</span></a>                        <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-291"><a href="#L-291"><span class="linenos">291</span></a>                    
</span><span id="L-292"><a href="#L-292"><span class="linenos">292</span></a>                    <span class="n">img_filename</span> <span class="o">=</span> <span class="n">img_files</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
</span><span id="L-293"><a href="#L-293"><span class="linenos">293</span></a>                    <span class="n">img_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="n">Path</span><span class="p">(</span><span class="n">img_filename</span><span class="p">)</span><span class="o">.</span><span class="n">name</span>
</span><span id="L-294"><a href="#L-294"><span class="linenos">294</span></a>                    
</span><span id="L-295"><a href="#L-295"><span class="linenos">295</span></a>                    <span class="k">if</span> <span class="n">img_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="L-296"><a href="#L-296"><span class="linenos">296</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像文件已存在: </span><span class="si">{</span><span class="n">img_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-297"><a href="#L-297"><span class="linenos">297</span></a>                        <span class="k">return</span> <span class="n">img_path</span>
</span><span id="L-298"><a href="#L-298"><span class="linenos">298</span></a>                    
</span><span id="L-299"><a href="#L-299"><span class="linenos">299</span></a>                    <span class="k">with</span> <span class="n">zip_ref</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="n">img_filename</span><span class="p">)</span> <span class="k">as</span> <span class="n">source</span><span class="p">,</span> <span class="nb">open</span><span class="p">(</span><span class="n">img_path</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">target</span><span class="p">:</span>
</span><span id="L-300"><a href="#L-300"><span class="linenos">300</span></a>                        <span class="n">target</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">source</span><span class="o">.</span><span class="n">read</span><span class="p">())</span>
</span><span id="L-301"><a href="#L-301"><span class="linenos">301</span></a>                    
</span><span id="L-302"><a href="#L-302"><span class="linenos">302</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压完成: </span><span class="si">{</span><span class="n">img_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-303"><a href="#L-303"><span class="linenos">303</span></a>                    <span class="k">return</span> <span class="n">img_path</span>
</span><span id="L-304"><a href="#L-304"><span class="linenos">304</span></a>                    
</span><span id="L-305"><a href="#L-305"><span class="linenos">305</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="L-306"><a href="#L-306"><span class="linenos">306</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;不支持的文件格式: </span><span class="si">{</span><span class="n">archive_path</span><span class="o">.</span><span class="n">suffix</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-307"><a href="#L-307"><span class="linenos">307</span></a>                <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-308"><a href="#L-308"><span class="linenos">308</span></a>                
</span><span id="L-309"><a href="#L-309"><span class="linenos">309</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-310"><a href="#L-310"><span class="linenos">310</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-311"><a href="#L-311"><span class="linenos">311</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="L-312"><a href="#L-312"><span class="linenos">312</span></a>    
</span><span id="L-313"><a href="#L-313"><span class="linenos">313</span></a>    <span class="k">def</span> <span class="nf">list_available_disks</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]:</span>
</span><span id="L-314"><a href="#L-314"><span class="linenos">314</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;列出可用的磁盘&quot;&quot;&quot;</span>
</span><span id="L-315"><a href="#L-315"><span class="linenos">315</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;扫描可用磁盘...&quot;</span><span class="p">)</span>
</span><span id="L-316"><a href="#L-316"><span class="linenos">316</span></a>        <span class="n">disks</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="L-317"><a href="#L-317"><span class="linenos">317</span></a>        
</span><span id="L-318"><a href="#L-318"><span class="linenos">318</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Windows&quot;</span><span class="p">:</span>
</span><span id="L-319"><a href="#L-319"><span class="linenos">319</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_list_windows_disks</span><span class="p">()</span>
</span><span id="L-320"><a href="#L-320"><span class="linenos">320</span></a>        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;Linux&quot;</span><span class="p">,</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">]:</span>
</span><span id="L-321"><a href="#L-321"><span class="linenos">321</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_list_unix_disks</span><span class="p">()</span>
</span><span id="L-322"><a href="#L-322"><span class="linenos">322</span></a>        
</span><span id="L-323"><a href="#L-323"><span class="linenos">323</span></a>        <span class="k">return</span> <span class="n">disks</span>
</span><span id="L-324"><a href="#L-324"><span class="linenos">324</span></a>    
</span><span id="L-325"><a href="#L-325"><span class="linenos">325</span></a>    <span class="k">def</span> <span class="nf">_list_windows_disks</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]:</span>
</span><span id="L-326"><a href="#L-326"><span class="linenos">326</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;列出Windows磁盘&quot;&quot;&quot;</span>
</span><span id="L-327"><a href="#L-327"><span class="linenos">327</span></a>        <span class="n">disks</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="L-328"><a href="#L-328"><span class="linenos">328</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-329"><a href="#L-329"><span class="linenos">329</span></a>            <span class="c1"># 使用 PowerShell 获取磁盘信息</span>
</span><span id="L-330"><a href="#L-330"><span class="linenos">330</span></a>            <span class="n">cmd</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="L-331"><a href="#L-331"><span class="linenos">331</span></a>                <span class="s2">&quot;powershell&quot;</span><span class="p">,</span> 
</span><span id="L-332"><a href="#L-332"><span class="linenos">332</span></a>                <span class="s2">&quot;Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID,Size,FreeSpace,VolumeName | ConvertTo-Json&quot;</span>
</span><span id="L-333"><a href="#L-333"><span class="linenos">333</span></a>            <span class="p">]</span>
</span><span id="L-334"><a href="#L-334"><span class="linenos">334</span></a>            
</span><span id="L-335"><a href="#L-335"><span class="linenos">335</span></a>            <span class="n">returncode</span><span class="p">,</span> <span class="n">stdout</span><span class="p">,</span> <span class="n">stderr</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_run_command</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="n">check</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</span><span id="L-336"><a href="#L-336"><span class="linenos">336</span></a>            
</span><span id="L-337"><a href="#L-337"><span class="linenos">337</span></a>            <span class="k">if</span> <span class="n">returncode</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="L-338"><a href="#L-338"><span class="linenos">338</span></a>                <span class="kn">import</span> <span class="nn">json</span>
</span><span id="L-339"><a href="#L-339"><span class="linenos">339</span></a>                <span class="n">disks_data</span> <span class="o">=</span> <span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="n">stdout</span><span class="p">)</span>
</span><span id="L-340"><a href="#L-340"><span class="linenos">340</span></a>                <span class="k">if</span> <span class="ow">not</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">disks_data</span><span class="p">,</span> <span class="nb">list</span><span class="p">):</span>
</span><span id="L-341"><a href="#L-341"><span class="linenos">341</span></a>                    <span class="n">disks_data</span> <span class="o">=</span> <span class="p">[</span><span class="n">disks_data</span><span class="p">]</span>
</span><span id="L-342"><a href="#L-342"><span class="linenos">342</span></a>                
</span><span id="L-343"><a href="#L-343"><span class="linenos">343</span></a>                <span class="k">for</span> <span class="n">disk</span> <span class="ow">in</span> <span class="n">disks_data</span><span class="p">:</span>
</span><span id="L-344"><a href="#L-344"><span class="linenos">344</span></a>                    <span class="n">device_id</span> <span class="o">=</span> <span class="n">disk</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;DeviceID&#39;</span><span class="p">,</span> <span class="s1">&#39;&#39;</span><span class="p">)</span>
</span><span id="L-345"><a href="#L-345"><span class="linenos">345</span></a>                    <span class="n">size</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">disk</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;Size&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
</span><span id="L-346"><a href="#L-346"><span class="linenos">346</span></a>                    <span class="n">free_space</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">disk</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;FreeSpace&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
</span><span id="L-347"><a href="#L-347"><span class="linenos">347</span></a>                    <span class="n">volume_name</span> <span class="o">=</span> <span class="n">disk</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;VolumeName&#39;</span><span class="p">,</span> <span class="s1">&#39;Unknown&#39;</span><span class="p">)</span>
</span><span id="L-348"><a href="#L-348"><span class="linenos">348</span></a>                    
</span><span id="L-349"><a href="#L-349"><span class="linenos">349</span></a>                    <span class="c1"># 只显示可移动磁盘或大容量磁盘</span>
</span><span id="L-350"><a href="#L-350"><span class="linenos">350</span></a>                    <span class="k">if</span> <span class="n">size</span> <span class="o">&gt;</span> <span class="mi">1024</span><span class="o">**</span><span class="mi">3</span><span class="p">:</span>  <span class="c1"># 大于1GB</span>
</span><span id="L-351"><a href="#L-351"><span class="linenos">351</span></a>                        <span class="n">size_gb</span> <span class="o">=</span> <span class="n">size</span> <span class="o">/</span> <span class="p">(</span><span class="mi">1024</span><span class="o">**</span><span class="mi">3</span><span class="p">)</span>
</span><span id="L-352"><a href="#L-352"><span class="linenos">352</span></a>                        <span class="n">free_gb</span> <span class="o">=</span> <span class="n">free_space</span> <span class="o">/</span> <span class="p">(</span><span class="mi">1024</span><span class="o">**</span><span class="mi">3</span><span class="p">)</span>
</span><span id="L-353"><a href="#L-353"><span class="linenos">353</span></a>                        <span class="n">disks</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">device_id</span><span class="p">,</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">size_gb</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">GB&quot;</span><span class="p">,</span> <span class="n">volume_name</span><span class="p">))</span>
</span><span id="L-354"><a href="#L-354"><span class="linenos">354</span></a>                
</span><span id="L-355"><a href="#L-355"><span class="linenos">355</span></a>                <span class="k">return</span> <span class="n">disks</span>
</span><span id="L-356"><a href="#L-356"><span class="linenos">356</span></a>                
</span><span id="L-357"><a href="#L-357"><span class="linenos">357</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-358"><a href="#L-358"><span class="linenos">358</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;获取Windows磁盘信息失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-359"><a href="#L-359"><span class="linenos">359</span></a>        
</span><span id="L-360"><a href="#L-360"><span class="linenos">360</span></a>        <span class="k">return</span> <span class="n">disks</span>
</span><span id="L-361"><a href="#L-361"><span class="linenos">361</span></a>    
</span><span id="L-362"><a href="#L-362"><span class="linenos">362</span></a>    <span class="k">def</span> <span class="nf">_list_unix_disks</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]:</span>
</span><span id="L-363"><a href="#L-363"><span class="linenos">363</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;列出Unix磁盘&quot;&quot;&quot;</span>
</span><span id="L-364"><a href="#L-364"><span class="linenos">364</span></a>        <span class="n">disks</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="L-365"><a href="#L-365"><span class="linenos">365</span></a>        
</span><span id="L-366"><a href="#L-366"><span class="linenos">366</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-367"><a href="#L-367"><span class="linenos">367</span></a>            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">:</span>  <span class="c1"># macOS</span>
</span><span id="L-368"><a href="#L-368"><span class="linenos">368</span></a>                <span class="n">cmd</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;diskutil&quot;</span><span class="p">,</span> <span class="s2">&quot;list&quot;</span><span class="p">]</span>
</span><span id="L-369"><a href="#L-369"><span class="linenos">369</span></a>            <span class="k">else</span><span class="p">:</span>  <span class="c1"># Linux</span>
</span><span id="L-370"><a href="#L-370"><span class="linenos">370</span></a>                <span class="n">cmd</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;lsblk&quot;</span><span class="p">,</span> <span class="s2">&quot;-o&quot;</span><span class="p">,</span> <span class="s2">&quot;NAME,SIZE,TYPE,MOUNTPOINT&quot;</span><span class="p">,</span> <span class="s2">&quot;-J&quot;</span><span class="p">]</span>
</span><span id="L-371"><a href="#L-371"><span class="linenos">371</span></a>            
</span><span id="L-372"><a href="#L-372"><span class="linenos">372</span></a>            <span class="n">returncode</span><span class="p">,</span> <span class="n">stdout</span><span class="p">,</span> <span class="n">stderr</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_run_command</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="n">check</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</span><span id="L-373"><a href="#L-373"><span class="linenos">373</span></a>            
</span><span id="L-374"><a href="#L-374"><span class="linenos">374</span></a>            <span class="k">if</span> <span class="n">returncode</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="L-375"><a href="#L-375"><span class="linenos">375</span></a>                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">:</span>
</span><span id="L-376"><a href="#L-376"><span class="linenos">376</span></a>                    <span class="c1"># 解析 macOS diskutil 输出</span>
</span><span id="L-377"><a href="#L-377"><span class="linenos">377</span></a>                    <span class="n">lines</span> <span class="o">=</span> <span class="n">stdout</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;</span><span class="se">\n</span><span class="s1">&#39;</span><span class="p">)</span>
</span><span id="L-378"><a href="#L-378"><span class="linenos">378</span></a>                    <span class="k">for</span> <span class="n">line</span> <span class="ow">in</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="L-379"><a href="#L-379"><span class="linenos">379</span></a>                        <span class="k">if</span> <span class="s1">&#39;/dev/disk&#39;</span> <span class="ow">in</span> <span class="n">line</span> <span class="ow">and</span> <span class="s1">&#39;external&#39;</span> <span class="ow">in</span> <span class="n">line</span><span class="o">.</span><span class="n">lower</span><span class="p">():</span>
</span><span id="L-380"><a href="#L-380"><span class="linenos">380</span></a>                            <span class="n">parts</span> <span class="o">=</span> <span class="n">line</span><span class="o">.</span><span class="n">split</span><span class="p">()</span>
</span><span id="L-381"><a href="#L-381"><span class="linenos">381</span></a>                            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">parts</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="mi">3</span><span class="p">:</span>
</span><span id="L-382"><a href="#L-382"><span class="linenos">382</span></a>                                <span class="n">disk_name</span> <span class="o">=</span> <span class="n">parts</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
</span><span id="L-383"><a href="#L-383"><span class="linenos">383</span></a>                                <span class="n">size</span> <span class="o">=</span> <span class="n">parts</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">parts</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">1</span> <span class="k">else</span> <span class="s2">&quot;Unknown&quot;</span>
</span><span id="L-384"><a href="#L-384"><span class="linenos">384</span></a>                                <span class="n">disks</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">disk_name</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="s2">&quot;External Disk&quot;</span><span class="p">))</span>
</span><span id="L-385"><a href="#L-385"><span class="linenos">385</span></a>                <span class="k">else</span><span class="p">:</span>
</span><span id="L-386"><a href="#L-386"><span class="linenos">386</span></a>                    <span class="c1"># 解析 Linux lsblk 输出</span>
</span><span id="L-387"><a href="#L-387"><span class="linenos">387</span></a>                    <span class="kn">import</span> <span class="nn">json</span>
</span><span id="L-388"><a href="#L-388"><span class="linenos">388</span></a>                    <span class="k">try</span><span class="p">:</span>
</span><span id="L-389"><a href="#L-389"><span class="linenos">389</span></a>                        <span class="n">data</span> <span class="o">=</span> <span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="n">stdout</span><span class="p">)</span>
</span><span id="L-390"><a href="#L-390"><span class="linenos">390</span></a>                        <span class="k">for</span> <span class="n">device</span> <span class="ow">in</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;blockdevices&#39;</span><span class="p">,</span> <span class="p">[]):</span>
</span><span id="L-391"><a href="#L-391"><span class="linenos">391</span></a>                            <span class="k">if</span> <span class="n">device</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;type&#39;</span><span class="p">)</span> <span class="o">==</span> <span class="s1">&#39;disk&#39;</span><span class="p">:</span>
</span><span id="L-392"><a href="#L-392"><span class="linenos">392</span></a>                                <span class="n">name</span> <span class="o">=</span> <span class="n">device</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;name&#39;</span><span class="p">,</span> <span class="s1">&#39;&#39;</span><span class="p">)</span>
</span><span id="L-393"><a href="#L-393"><span class="linenos">393</span></a>                                <span class="n">size</span> <span class="o">=</span> <span class="n">device</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;size&#39;</span><span class="p">,</span> <span class="s1">&#39;Unknown&#39;</span><span class="p">)</span>
</span><span id="L-394"><a href="#L-394"><span class="linenos">394</span></a>                                <span class="n">mountpoint</span> <span class="o">=</span> <span class="n">device</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;mountpoint&#39;</span><span class="p">,</span> <span class="s1">&#39;&#39;</span><span class="p">)</span>
</span><span id="L-395"><a href="#L-395"><span class="linenos">395</span></a>                                <span class="n">disks</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">name</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="n">mountpoint</span> <span class="ow">or</span> <span class="s2">&quot;Unmounted&quot;</span><span class="p">))</span>
</span><span id="L-396"><a href="#L-396"><span class="linenos">396</span></a>                    <span class="k">except</span> <span class="n">json</span><span class="o">.</span><span class="n">JSONDecodeError</span><span class="p">:</span>
</span><span id="L-397"><a href="#L-397"><span class="linenos">397</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;无法解析lsblk输出&quot;</span><span class="p">)</span>
</span><span id="L-398"><a href="#L-398"><span class="linenos">398</span></a>            
</span><span id="L-399"><a href="#L-399"><span class="linenos">399</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-400"><a href="#L-400"><span class="linenos">400</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;获取Unix磁盘信息失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-401"><a href="#L-401"><span class="linenos">401</span></a>        
</span><span id="L-402"><a href="#L-402"><span class="linenos">402</span></a>        <span class="k">return</span> <span class="n">disks</span>
</span><span id="L-403"><a href="#L-403"><span class="linenos">403</span></a>    
</span><span id="L-404"><a href="#L-404"><span class="linenos">404</span></a>    <span class="k">def</span> <span class="nf">burn_image</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">image_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-405"><a href="#L-405"><span class="linenos">405</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;烧录镜像到指定磁盘&quot;&quot;&quot;</span>
</span><span id="L-406"><a href="#L-406"><span class="linenos">406</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;开始烧录镜像到 </span><span class="si">{</span><span class="n">target_disk</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-407"><a href="#L-407"><span class="linenos">407</span></a>        
</span><span id="L-408"><a href="#L-408"><span class="linenos">408</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Windows&quot;</span><span class="p">:</span>
</span><span id="L-409"><a href="#L-409"><span class="linenos">409</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_burn_windows</span><span class="p">(</span><span class="n">image_path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">)</span>
</span><span id="L-410"><a href="#L-410"><span class="linenos">410</span></a>        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;Linux&quot;</span><span class="p">,</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">]:</span>
</span><span id="L-411"><a href="#L-411"><span class="linenos">411</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_burn_unix</span><span class="p">(</span><span class="n">image_path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">)</span>
</span><span id="L-412"><a href="#L-412"><span class="linenos">412</span></a>        
</span><span id="L-413"><a href="#L-413"><span class="linenos">413</span></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-414"><a href="#L-414"><span class="linenos">414</span></a>    
</span><span id="L-415"><a href="#L-415"><span class="linenos">415</span></a>    <span class="k">def</span> <span class="nf">_burn_windows</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">image_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-416"><a href="#L-416"><span class="linenos">416</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;Windows系统烧录&quot;&quot;&quot;</span>
</span><span id="L-417"><a href="#L-417"><span class="linenos">417</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;Windows系统需要手动烧录&quot;</span><span class="p">)</span>
</span><span id="L-418"><a href="#L-418"><span class="linenos">418</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;请使用 Win32DiskImager 或 balenaEtcher 将镜像烧录到 </span><span class="si">{</span><span class="n">target_disk</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-419"><a href="#L-419"><span class="linenos">419</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像文件路径: </span><span class="si">{</span><span class="n">image_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-420"><a href="#L-420"><span class="linenos">420</span></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-421"><a href="#L-421"><span class="linenos">421</span></a>    
</span><span id="L-422"><a href="#L-422"><span class="linenos">422</span></a>    <span class="k">def</span> <span class="nf">_burn_unix</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">image_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="L-423"><a href="#L-423"><span class="linenos">423</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;Unix系统烧录&quot;&quot;&quot;</span>
</span><span id="L-424"><a href="#L-424"><span class="linenos">424</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;即将烧录镜像到 </span><span class="si">{</span><span class="n">target_disk</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-425"><a href="#L-425"><span class="linenos">425</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;此操作将擦除目标磁盘的所有数据！&quot;</span><span class="p">)</span>
</span><span id="L-426"><a href="#L-426"><span class="linenos">426</span></a>        
</span><span id="L-427"><a href="#L-427"><span class="linenos">427</span></a>        <span class="c1"># 确认操作</span>
</span><span id="L-428"><a href="#L-428"><span class="linenos">428</span></a>        <span class="n">confirm</span> <span class="o">=</span> <span class="nb">input</span><span class="p">(</span><span class="s2">&quot;确认继续？(输入 &#39;yes&#39; 确认): &quot;</span><span class="p">)</span>
</span><span id="L-429"><a href="#L-429"><span class="linenos">429</span></a>        <span class="k">if</span> <span class="n">confirm</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="o">!=</span> <span class="s1">&#39;yes&#39;</span><span class="p">:</span>
</span><span id="L-430"><a href="#L-430"><span class="linenos">430</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;操作已取消&quot;</span><span class="p">)</span>
</span><span id="L-431"><a href="#L-431"><span class="linenos">431</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-432"><a href="#L-432"><span class="linenos">432</span></a>        
</span><span id="L-433"><a href="#L-433"><span class="linenos">433</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-434"><a href="#L-434"><span class="linenos">434</span></a>            <span class="c1"># 卸载磁盘（如果已挂载）</span>
</span><span id="L-435"><a href="#L-435"><span class="linenos">435</span></a>            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">:</span>
</span><span id="L-436"><a href="#L-436"><span class="linenos">436</span></a>                <span class="n">unmount_cmd</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;diskutil&quot;</span><span class="p">,</span> <span class="s2">&quot;unmountDisk&quot;</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">]</span>
</span><span id="L-437"><a href="#L-437"><span class="linenos">437</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="L-438"><a href="#L-438"><span class="linenos">438</span></a>                <span class="n">unmount_cmd</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;sudo&quot;</span><span class="p">,</span> <span class="s2">&quot;umount&quot;</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">]</span>
</span><span id="L-439"><a href="#L-439"><span class="linenos">439</span></a>            
</span><span id="L-440"><a href="#L-440"><span class="linenos">440</span></a>            <span class="bp">self</span><span class="o">.</span><span class="n">_run_command</span><span class="p">(</span><span class="n">unmount_cmd</span><span class="p">,</span> <span class="n">check</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</span><span id="L-441"><a href="#L-441"><span class="linenos">441</span></a>            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
</span><span id="L-442"><a href="#L-442"><span class="linenos">442</span></a>            
</span><span id="L-443"><a href="#L-443"><span class="linenos">443</span></a>            <span class="c1"># 使用dd命令烧录</span>
</span><span id="L-444"><a href="#L-444"><span class="linenos">444</span></a>            <span class="n">dd_cmd</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="L-445"><a href="#L-445"><span class="linenos">445</span></a>                <span class="s2">&quot;sudo&quot;</span><span class="p">,</span> <span class="s2">&quot;dd&quot;</span><span class="p">,</span> 
</span><span id="L-446"><a href="#L-446"><span class="linenos">446</span></a>                <span class="sa">f</span><span class="s2">&quot;if=</span><span class="si">{</span><span class="n">image_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> 
</span><span id="L-447"><a href="#L-447"><span class="linenos">447</span></a>                <span class="sa">f</span><span class="s2">&quot;of=</span><span class="si">{</span><span class="n">target_disk</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> 
</span><span id="L-448"><a href="#L-448"><span class="linenos">448</span></a>                <span class="s2">&quot;bs=4M&quot;</span><span class="p">,</span> 
</span><span id="L-449"><a href="#L-449"><span class="linenos">449</span></a>                <span class="s2">&quot;status=progress&quot;</span>
</span><span id="L-450"><a href="#L-450"><span class="linenos">450</span></a>            <span class="p">]</span>
</span><span id="L-451"><a href="#L-451"><span class="linenos">451</span></a>            
</span><span id="L-452"><a href="#L-452"><span class="linenos">452</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;开始烧录，这可能需要几分钟...&quot;</span><span class="p">)</span>
</span><span id="L-453"><a href="#L-453"><span class="linenos">453</span></a>            <span class="n">returncode</span><span class="p">,</span> <span class="n">stdout</span><span class="p">,</span> <span class="n">stderr</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_run_command</span><span class="p">(</span><span class="n">dd_cmd</span><span class="p">)</span>
</span><span id="L-454"><a href="#L-454"><span class="linenos">454</span></a>            
</span><span id="L-455"><a href="#L-455"><span class="linenos">455</span></a>            <span class="k">if</span> <span class="n">returncode</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="L-456"><a href="#L-456"><span class="linenos">456</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;烧录完成！&quot;</span><span class="p">)</span>
</span><span id="L-457"><a href="#L-457"><span class="linenos">457</span></a>                <span class="k">return</span> <span class="kc">True</span>
</span><span id="L-458"><a href="#L-458"><span class="linenos">458</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="L-459"><a href="#L-459"><span class="linenos">459</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;烧录失败: </span><span class="si">{</span><span class="n">stderr</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-460"><a href="#L-460"><span class="linenos">460</span></a>                <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-461"><a href="#L-461"><span class="linenos">461</span></a>                
</span><span id="L-462"><a href="#L-462"><span class="linenos">462</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="L-463"><a href="#L-463"><span class="linenos">463</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;烧录过程中出错: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-464"><a href="#L-464"><span class="linenos">464</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="L-465"><a href="#L-465"><span class="linenos">465</span></a>    
</span><span id="L-466"><a href="#L-466"><span class="linenos">466</span></a>    <span class="k">def</span> <span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
</span><span id="L-467"><a href="#L-467"><span class="linenos">467</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;运行主程序&quot;&quot;&quot;</span>
</span><span id="L-468"><a href="#L-468"><span class="linenos">468</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;=== RetroPie 镜像下载和烧录工具 ===&quot;</span><span class="p">)</span>
</span><span id="L-469"><a href="#L-469"><span class="linenos">469</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;操作系统: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">system</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-470"><a href="#L-470"><span class="linenos">470</span></a>        
</span><span id="L-471"><a href="#L-471"><span class="linenos">471</span></a>        <span class="c1"># 检查依赖</span>
</span><span id="L-472"><a href="#L-472"><span class="linenos">472</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">check_dependencies</span><span class="p">():</span>
</span><span id="L-473"><a href="#L-473"><span class="linenos">473</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;系统依赖检查失败&quot;</span><span class="p">)</span>
</span><span id="L-474"><a href="#L-474"><span class="linenos">474</span></a>            <span class="k">return</span>
</span><span id="L-475"><a href="#L-475"><span class="linenos">475</span></a>        
</span><span id="L-476"><a href="#L-476"><span class="linenos">476</span></a>        <span class="c1"># 获取下载链接</span>
</span><span id="L-477"><a href="#L-477"><span class="linenos">477</span></a>        <span class="n">download_url</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_retropie_download_url</span><span class="p">()</span>
</span><span id="L-478"><a href="#L-478"><span class="linenos">478</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">download_url</span><span class="p">:</span>
</span><span id="L-479"><a href="#L-479"><span class="linenos">479</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;无法获取下载链接&quot;</span><span class="p">)</span>
</span><span id="L-480"><a href="#L-480"><span class="linenos">480</span></a>            <span class="k">return</span>
</span><span id="L-481"><a href="#L-481"><span class="linenos">481</span></a>        
</span><span id="L-482"><a href="#L-482"><span class="linenos">482</span></a>        <span class="c1"># 下载镜像</span>
</span><span id="L-483"><a href="#L-483"><span class="linenos">483</span></a>        <span class="n">archive_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_image</span><span class="p">(</span><span class="n">download_url</span><span class="p">)</span>
</span><span id="L-484"><a href="#L-484"><span class="linenos">484</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">archive_path</span><span class="p">:</span>
</span><span id="L-485"><a href="#L-485"><span class="linenos">485</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;镜像下载失败&quot;</span><span class="p">)</span>
</span><span id="L-486"><a href="#L-486"><span class="linenos">486</span></a>            <span class="k">return</span>
</span><span id="L-487"><a href="#L-487"><span class="linenos">487</span></a>        
</span><span id="L-488"><a href="#L-488"><span class="linenos">488</span></a>        <span class="c1"># 解压镜像</span>
</span><span id="L-489"><a href="#L-489"><span class="linenos">489</span></a>        <span class="n">image_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">extract_image</span><span class="p">(</span><span class="n">archive_path</span><span class="p">)</span>
</span><span id="L-490"><a href="#L-490"><span class="linenos">490</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">image_path</span><span class="p">:</span>
</span><span id="L-491"><a href="#L-491"><span class="linenos">491</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;镜像解压失败&quot;</span><span class="p">)</span>
</span><span id="L-492"><a href="#L-492"><span class="linenos">492</span></a>            <span class="k">return</span>
</span><span id="L-493"><a href="#L-493"><span class="linenos">493</span></a>        
</span><span id="L-494"><a href="#L-494"><span class="linenos">494</span></a>        <span class="c1"># 列出可用磁盘</span>
</span><span id="L-495"><a href="#L-495"><span class="linenos">495</span></a>        <span class="n">disks</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">list_available_disks</span><span class="p">()</span>
</span><span id="L-496"><a href="#L-496"><span class="linenos">496</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">disks</span><span class="p">:</span>
</span><span id="L-497"><a href="#L-497"><span class="linenos">497</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;未找到可用磁盘&quot;</span><span class="p">)</span>
</span><span id="L-498"><a href="#L-498"><span class="linenos">498</span></a>            <span class="k">return</span>
</span><span id="L-499"><a href="#L-499"><span class="linenos">499</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;可用磁盘:&quot;</span><span class="p">)</span>
</span><span id="L-500"><a href="#L-500"><span class="linenos">500</span></a>        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="p">(</span><span class="n">device</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="n">name</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">disks</span><span class="p">):</span>
</span><span id="L-501"><a href="#L-501"><span class="linenos">501</span></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">. </span><span class="si">{</span><span class="n">device</span><span class="si">}</span><span class="s2"> (</span><span class="si">{</span><span class="n">size</span><span class="si">}</span><span class="s2">) - </span><span class="si">{</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-502"><a href="#L-502"><span class="linenos">502</span></a>        
</span><span id="L-503"><a href="#L-503"><span class="linenos">503</span></a>        <span class="c1"># 选择目标磁盘</span>
</span><span id="L-504"><a href="#L-504"><span class="linenos">504</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="L-505"><a href="#L-505"><span class="linenos">505</span></a>            <span class="n">choice</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="nb">input</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;请选择目标磁盘 (1-</span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">disks</span><span class="p">)</span><span class="si">}</span><span class="s2">): &quot;</span><span class="p">))</span> <span class="o">-</span> <span class="mi">1</span>
</span><span id="L-506"><a href="#L-506"><span class="linenos">506</span></a>            <span class="k">if</span> <span class="mi">0</span> <span class="o">&lt;=</span> <span class="n">choice</span> <span class="o">&lt;</span> <span class="nb">len</span><span class="p">(</span><span class="n">disks</span><span class="p">):</span>
</span><span id="L-507"><a href="#L-507"><span class="linenos">507</span></a>                <span class="n">target_disk</span> <span class="o">=</span> <span class="n">disks</span><span class="p">[</span><span class="n">choice</span><span class="p">][</span><span class="mi">0</span><span class="p">]</span>
</span><span id="L-508"><a href="#L-508"><span class="linenos">508</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="L-509"><a href="#L-509"><span class="linenos">509</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;无效的选择&quot;</span><span class="p">)</span>
</span><span id="L-510"><a href="#L-510"><span class="linenos">510</span></a>                <span class="k">return</span>
</span><span id="L-511"><a href="#L-511"><span class="linenos">511</span></a>        <span class="k">except</span> <span class="ne">ValueError</span><span class="p">:</span>
</span><span id="L-512"><a href="#L-512"><span class="linenos">512</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;请输入有效的数字&quot;</span><span class="p">)</span>
</span><span id="L-513"><a href="#L-513"><span class="linenos">513</span></a>            <span class="k">return</span>
</span><span id="L-514"><a href="#L-514"><span class="linenos">514</span></a>        
</span><span id="L-515"><a href="#L-515"><span class="linenos">515</span></a>        <span class="c1"># 烧录镜像</span>
</span><span id="L-516"><a href="#L-516"><span class="linenos">516</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">burn_image</span><span class="p">(</span><span class="n">image_path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">):</span>
</span><span id="L-517"><a href="#L-517"><span class="linenos">517</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;RetroPie 安装完成！&quot;</span><span class="p">)</span>
</span><span id="L-518"><a href="#L-518"><span class="linenos">518</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="L-519"><a href="#L-519"><span class="linenos">519</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;烧录失败&quot;</span><span class="p">)</span>
</span><span id="L-520"><a href="#L-520"><span class="linenos">520</span></a>
</span><span id="L-521"><a href="#L-521"><span class="linenos">521</span></a><span class="k">def</span> <span class="nf">main</span><span class="p">():</span>
</span><span id="L-522"><a href="#L-522"><span class="linenos">522</span></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;主函数&quot;&quot;&quot;</span>
</span><span id="L-523"><a href="#L-523"><span class="linenos">523</span></a>    <span class="n">parser</span> <span class="o">=</span> <span class="n">argparse</span><span class="o">.</span><span class="n">ArgumentParser</span><span class="p">(</span><span class="n">description</span><span class="o">=</span><span class="s2">&quot;RetroPie 镜像下载和烧录工具&quot;</span><span class="p">)</span>
</span><span id="L-524"><a href="#L-524"><span class="linenos">524</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--check-only&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;仅检查系统依赖&quot;</span><span class="p">)</span>
</span><span id="L-525"><a href="#L-525"><span class="linenos">525</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--download-only&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;仅下载镜像&quot;</span><span class="p">)</span>
</span><span id="L-526"><a href="#L-526"><span class="linenos">526</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--list-disks&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;列出可用磁盘&quot;</span><span class="p">)</span>
</span><span id="L-527"><a href="#L-527"><span class="linenos">527</span></a>    
</span><span id="L-528"><a href="#L-528"><span class="linenos">528</span></a>    <span class="n">args</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>
</span><span id="L-529"><a href="#L-529"><span class="linenos">529</span></a>    
</span><span id="L-530"><a href="#L-530"><span class="linenos">530</span></a>    <span class="n">installer</span> <span class="o">=</span> <span class="n">RetroPieInstaller</span><span class="p">()</span>
</span><span id="L-531"><a href="#L-531"><span class="linenos">531</span></a>    
</span><span id="L-532"><a href="#L-532"><span class="linenos">532</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">check_only</span><span class="p">:</span>
</span><span id="L-533"><a href="#L-533"><span class="linenos">533</span></a>        <span class="k">if</span> <span class="n">installer</span><span class="o">.</span><span class="n">check_dependencies</span><span class="p">():</span>
</span><span id="L-534"><a href="#L-534"><span class="linenos">534</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;系统依赖检查通过&quot;</span><span class="p">)</span>
</span><span id="L-535"><a href="#L-535"><span class="linenos">535</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="L-536"><a href="#L-536"><span class="linenos">536</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;系统依赖检查失败&quot;</span><span class="p">)</span>
</span><span id="L-537"><a href="#L-537"><span class="linenos">537</span></a>        <span class="k">return</span>
</span><span id="L-538"><a href="#L-538"><span class="linenos">538</span></a>    
</span><span id="L-539"><a href="#L-539"><span class="linenos">539</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">list_disks</span><span class="p">:</span>
</span><span id="L-540"><a href="#L-540"><span class="linenos">540</span></a>        <span class="n">disks</span> <span class="o">=</span> <span class="n">installer</span><span class="o">.</span><span class="n">list_available_disks</span><span class="p">()</span>
</span><span id="L-541"><a href="#L-541"><span class="linenos">541</span></a>        <span class="k">if</span> <span class="n">disks</span><span class="p">:</span>
</span><span id="L-542"><a href="#L-542"><span class="linenos">542</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;可用磁盘:&quot;</span><span class="p">)</span>
</span><span id="L-543"><a href="#L-543"><span class="linenos">543</span></a>            <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="p">(</span><span class="n">device</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="n">name</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">disks</span><span class="p">):</span>
</span><span id="L-544"><a href="#L-544"><span class="linenos">544</span></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">. </span><span class="si">{</span><span class="n">device</span><span class="si">}</span><span class="s2"> (</span><span class="si">{</span><span class="n">size</span><span class="si">}</span><span class="s2">) - </span><span class="si">{</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-545"><a href="#L-545"><span class="linenos">545</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="L-546"><a href="#L-546"><span class="linenos">546</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;未找到可用磁盘&quot;</span><span class="p">)</span>
</span><span id="L-547"><a href="#L-547"><span class="linenos">547</span></a>        <span class="k">return</span>
</span><span id="L-548"><a href="#L-548"><span class="linenos">548</span></a>    
</span><span id="L-549"><a href="#L-549"><span class="linenos">549</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">download_only</span><span class="p">:</span>
</span><span id="L-550"><a href="#L-550"><span class="linenos">550</span></a>        <span class="n">download_url</span> <span class="o">=</span> <span class="n">installer</span><span class="o">.</span><span class="n">get_retropie_download_url</span><span class="p">()</span>
</span><span id="L-551"><a href="#L-551"><span class="linenos">551</span></a>        <span class="k">if</span> <span class="n">download_url</span><span class="p">:</span>
</span><span id="L-552"><a href="#L-552"><span class="linenos">552</span></a>            <span class="n">archive_path</span> <span class="o">=</span> <span class="n">installer</span><span class="o">.</span><span class="n">download_image</span><span class="p">(</span><span class="n">download_url</span><span class="p">)</span>
</span><span id="L-553"><a href="#L-553"><span class="linenos">553</span></a>            <span class="k">if</span> <span class="n">archive_path</span><span class="p">:</span>
</span><span id="L-554"><a href="#L-554"><span class="linenos">554</span></a>                <span class="n">image_path</span> <span class="o">=</span> <span class="n">installer</span><span class="o">.</span><span class="n">extract_image</span><span class="p">(</span><span class="n">archive_path</span><span class="p">)</span>
</span><span id="L-555"><a href="#L-555"><span class="linenos">555</span></a>                <span class="k">if</span> <span class="n">image_path</span><span class="p">:</span>
</span><span id="L-556"><a href="#L-556"><span class="linenos">556</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像下载完成: </span><span class="si">{</span><span class="n">image_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="L-557"><a href="#L-557"><span class="linenos">557</span></a>        <span class="k">return</span>
</span><span id="L-558"><a href="#L-558"><span class="linenos">558</span></a>    
</span><span id="L-559"><a href="#L-559"><span class="linenos">559</span></a>    <span class="c1"># 运行完整流程</span>
</span><span id="L-560"><a href="#L-560"><span class="linenos">560</span></a>    <span class="n">installer</span><span class="o">.</span><span class="n">run</span><span class="p">()</span>
</span><span id="L-561"><a href="#L-561"><span class="linenos">561</span></a>
</span><span id="L-562"><a href="#L-562"><span class="linenos">562</span></a><span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
</span><span id="L-563"><a href="#L-563"><span class="linenos">563</span></a>    <span class="n">main</span><span class="p">()</span> 
</span></pre></div>


            </section>
                <section id="logger">
                    <div class="attr variable">
            <span class="name">logger</span>        =
<span class="default_value">&lt;Logger retropie_installer (INFO)&gt;</span>

        
    </div>
    <a class="headerlink" href="#logger"></a>
    
    

                </section>
                <section id="RetroPieInstaller">
                            <input id="RetroPieInstaller-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr class">
            
    <span class="def">class</span>
    <span class="name">RetroPieInstaller</span>:

                <label class="view-source-button" for="RetroPieInstaller-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#RetroPieInstaller"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="RetroPieInstaller-51"><a href="#RetroPieInstaller-51"><span class="linenos"> 51</span></a><span class="k">class</span> <span class="nc">RetroPieInstaller</span><span class="p">:</span>
</span><span id="RetroPieInstaller-52"><a href="#RetroPieInstaller-52"><span class="linenos"> 52</span></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-53"><a href="#RetroPieInstaller-53"><span class="linenos"> 53</span></a><span class="sd">    RetroPie 镜像下载和烧录工具类</span>
</span><span id="RetroPieInstaller-54"><a href="#RetroPieInstaller-54"><span class="linenos"> 54</span></a><span class="sd">    </span>
</span><span id="RetroPieInstaller-55"><a href="#RetroPieInstaller-55"><span class="linenos"> 55</span></a><span class="sd">    提供完整的RetroPie镜像自动化安装流程，包括：</span>
</span><span id="RetroPieInstaller-56"><a href="#RetroPieInstaller-56"><span class="linenos"> 56</span></a><span class="sd">    - 系统依赖检查</span>
</span><span id="RetroPieInstaller-57"><a href="#RetroPieInstaller-57"><span class="linenos"> 57</span></a><span class="sd">    - 镜像下载和解压</span>
</span><span id="RetroPieInstaller-58"><a href="#RetroPieInstaller-58"><span class="linenos"> 58</span></a><span class="sd">    - 磁盘设备管理</span>
</span><span id="RetroPieInstaller-59"><a href="#RetroPieInstaller-59"><span class="linenos"> 59</span></a><span class="sd">    - 镜像烧录操作</span>
</span><span id="RetroPieInstaller-60"><a href="#RetroPieInstaller-60"><span class="linenos"> 60</span></a><span class="sd">    </span>
</span><span id="RetroPieInstaller-61"><a href="#RetroPieInstaller-61"><span class="linenos"> 61</span></a><span class="sd">    属性:</span>
</span><span id="RetroPieInstaller-62"><a href="#RetroPieInstaller-62"><span class="linenos"> 62</span></a><span class="sd">        system (str): 当前操作系统名称</span>
</span><span id="RetroPieInstaller-63"><a href="#RetroPieInstaller-63"><span class="linenos"> 63</span></a><span class="sd">        retropie_url (str): RetroPie官网URL</span>
</span><span id="RetroPieInstaller-64"><a href="#RetroPieInstaller-64"><span class="linenos"> 64</span></a><span class="sd">        download_dir (Path): 下载目录路径</span>
</span><span id="RetroPieInstaller-65"><a href="#RetroPieInstaller-65"><span class="linenos"> 65</span></a><span class="sd">    &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-66"><a href="#RetroPieInstaller-66"><span class="linenos"> 66</span></a>    
</span><span id="RetroPieInstaller-67"><a href="#RetroPieInstaller-67"><span class="linenos"> 67</span></a>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
</span><span id="RetroPieInstaller-68"><a href="#RetroPieInstaller-68"><span class="linenos"> 68</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-69"><a href="#RetroPieInstaller-69"><span class="linenos"> 69</span></a><span class="sd">        初始化RetroPie安装器</span>
</span><span id="RetroPieInstaller-70"><a href="#RetroPieInstaller-70"><span class="linenos"> 70</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-71"><a href="#RetroPieInstaller-71"><span class="linenos"> 71</span></a><span class="sd">        设置基本配置和创建必要的目录结构。</span>
</span><span id="RetroPieInstaller-72"><a href="#RetroPieInstaller-72"><span class="linenos"> 72</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-73"><a href="#RetroPieInstaller-73"><span class="linenos"> 73</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">=</span> <span class="n">platform</span><span class="o">.</span><span class="n">system</span><span class="p">()</span>
</span><span id="RetroPieInstaller-74"><a href="#RetroPieInstaller-74"><span class="linenos"> 74</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">retropie_url</span> <span class="o">=</span> <span class="s2">&quot;https://retropie.org.uk/download/&quot;</span>
</span><span id="RetroPieInstaller-75"><a href="#RetroPieInstaller-75"><span class="linenos"> 75</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s2">&quot;downloads&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-76"><a href="#RetroPieInstaller-76"><span class="linenos"> 76</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="RetroPieInstaller-77"><a href="#RetroPieInstaller-77"><span class="linenos"> 77</span></a>        
</span><span id="RetroPieInstaller-78"><a href="#RetroPieInstaller-78"><span class="linenos"> 78</span></a>    <span class="k">def</span> <span class="nf">check_dependencies</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="RetroPieInstaller-79"><a href="#RetroPieInstaller-79"><span class="linenos"> 79</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-80"><a href="#RetroPieInstaller-80"><span class="linenos"> 80</span></a><span class="sd">        检查系统依赖是否满足</span>
</span><span id="RetroPieInstaller-81"><a href="#RetroPieInstaller-81"><span class="linenos"> 81</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-82"><a href="#RetroPieInstaller-82"><span class="linenos"> 82</span></a><span class="sd">        根据操作系统类型检查必要的工具和权限。</span>
</span><span id="RetroPieInstaller-83"><a href="#RetroPieInstaller-83"><span class="linenos"> 83</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-84"><a href="#RetroPieInstaller-84"><span class="linenos"> 84</span></a><span class="sd">        Returns:</span>
</span><span id="RetroPieInstaller-85"><a href="#RetroPieInstaller-85"><span class="linenos"> 85</span></a><span class="sd">            bool: 依赖检查是否通过</span>
</span><span id="RetroPieInstaller-86"><a href="#RetroPieInstaller-86"><span class="linenos"> 86</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-87"><a href="#RetroPieInstaller-87"><span class="linenos"> 87</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;检查系统依赖...&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-88"><a href="#RetroPieInstaller-88"><span class="linenos"> 88</span></a>        
</span><span id="RetroPieInstaller-89"><a href="#RetroPieInstaller-89"><span class="linenos"> 89</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Windows&quot;</span><span class="p">:</span>
</span><span id="RetroPieInstaller-90"><a href="#RetroPieInstaller-90"><span class="linenos"> 90</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_windows_dependencies</span><span class="p">()</span>
</span><span id="RetroPieInstaller-91"><a href="#RetroPieInstaller-91"><span class="linenos"> 91</span></a>        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;Linux&quot;</span><span class="p">,</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">]:</span>  <span class="c1"># Darwin = macOS</span>
</span><span id="RetroPieInstaller-92"><a href="#RetroPieInstaller-92"><span class="linenos"> 92</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_unix_dependencies</span><span class="p">()</span>
</span><span id="RetroPieInstaller-93"><a href="#RetroPieInstaller-93"><span class="linenos"> 93</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="RetroPieInstaller-94"><a href="#RetroPieInstaller-94"><span class="linenos"> 94</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;不支持的操作系统: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">system</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-95"><a href="#RetroPieInstaller-95"><span class="linenos"> 95</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="RetroPieInstaller-96"><a href="#RetroPieInstaller-96"><span class="linenos"> 96</span></a>    
</span><span id="RetroPieInstaller-97"><a href="#RetroPieInstaller-97"><span class="linenos"> 97</span></a>    <span class="k">def</span> <span class="nf">_check_windows_dependencies</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="RetroPieInstaller-98"><a href="#RetroPieInstaller-98"><span class="linenos"> 98</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-99"><a href="#RetroPieInstaller-99"><span class="linenos"> 99</span></a><span class="sd">        检查Windows系统依赖</span>
</span><span id="RetroPieInstaller-100"><a href="#RetroPieInstaller-100"><span class="linenos">100</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-101"><a href="#RetroPieInstaller-101"><span class="linenos">101</span></a><span class="sd">        检查是否有可用的镜像烧录工具。</span>
</span><span id="RetroPieInstaller-102"><a href="#RetroPieInstaller-102"><span class="linenos">102</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-103"><a href="#RetroPieInstaller-103"><span class="linenos">103</span></a><span class="sd">        Returns:</span>
</span><span id="RetroPieInstaller-104"><a href="#RetroPieInstaller-104"><span class="linenos">104</span></a><span class="sd">            bool: Windows依赖检查结果</span>
</span><span id="RetroPieInstaller-105"><a href="#RetroPieInstaller-105"><span class="linenos">105</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-106"><a href="#RetroPieInstaller-106"><span class="linenos">106</span></a>        <span class="c1"># 检查是否有Win32DiskImager或类似工具</span>
</span><span id="RetroPieInstaller-107"><a href="#RetroPieInstaller-107"><span class="linenos">107</span></a>        <span class="n">possible_tools</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="RetroPieInstaller-108"><a href="#RetroPieInstaller-108"><span class="linenos">108</span></a>            <span class="s2">&quot;Win32DiskImager.exe&quot;</span><span class="p">,</span>
</span><span id="RetroPieInstaller-109"><a href="#RetroPieInstaller-109"><span class="linenos">109</span></a>            <span class="s2">&quot;balenaEtcher.exe&quot;</span><span class="p">,</span>
</span><span id="RetroPieInstaller-110"><a href="#RetroPieInstaller-110"><span class="linenos">110</span></a>            <span class="s2">&quot;Rufus.exe&quot;</span>
</span><span id="RetroPieInstaller-111"><a href="#RetroPieInstaller-111"><span class="linenos">111</span></a>        <span class="p">]</span>
</span><span id="RetroPieInstaller-112"><a href="#RetroPieInstaller-112"><span class="linenos">112</span></a>        
</span><span id="RetroPieInstaller-113"><a href="#RetroPieInstaller-113"><span class="linenos">113</span></a>        <span class="k">for</span> <span class="n">tool</span> <span class="ow">in</span> <span class="n">possible_tools</span><span class="p">:</span>
</span><span id="RetroPieInstaller-114"><a href="#RetroPieInstaller-114"><span class="linenos">114</span></a>            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_find_executable</span><span class="p">(</span><span class="n">tool</span><span class="p">):</span>
</span><span id="RetroPieInstaller-115"><a href="#RetroPieInstaller-115"><span class="linenos">115</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到烧录工具: </span><span class="si">{</span><span class="n">tool</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-116"><a href="#RetroPieInstaller-116"><span class="linenos">116</span></a>                <span class="k">return</span> <span class="kc">True</span>
</span><span id="RetroPieInstaller-117"><a href="#RetroPieInstaller-117"><span class="linenos">117</span></a>        
</span><span id="RetroPieInstaller-118"><a href="#RetroPieInstaller-118"><span class="linenos">118</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;未找到烧录工具，请手动安装 Win32DiskImager 或 balenaEtcher&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-119"><a href="#RetroPieInstaller-119"><span class="linenos">119</span></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="RetroPieInstaller-120"><a href="#RetroPieInstaller-120"><span class="linenos">120</span></a>    
</span><span id="RetroPieInstaller-121"><a href="#RetroPieInstaller-121"><span class="linenos">121</span></a>    <span class="k">def</span> <span class="nf">_check_unix_dependencies</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="RetroPieInstaller-122"><a href="#RetroPieInstaller-122"><span class="linenos">122</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-123"><a href="#RetroPieInstaller-123"><span class="linenos">123</span></a><span class="sd">        检查Unix系统依赖（Linux/macOS）</span>
</span><span id="RetroPieInstaller-124"><a href="#RetroPieInstaller-124"><span class="linenos">124</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-125"><a href="#RetroPieInstaller-125"><span class="linenos">125</span></a><span class="sd">        检查dd命令是否可用。</span>
</span><span id="RetroPieInstaller-126"><a href="#RetroPieInstaller-126"><span class="linenos">126</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-127"><a href="#RetroPieInstaller-127"><span class="linenos">127</span></a><span class="sd">        Returns:</span>
</span><span id="RetroPieInstaller-128"><a href="#RetroPieInstaller-128"><span class="linenos">128</span></a><span class="sd">            bool: Unix依赖检查结果</span>
</span><span id="RetroPieInstaller-129"><a href="#RetroPieInstaller-129"><span class="linenos">129</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-130"><a href="#RetroPieInstaller-130"><span class="linenos">130</span></a>        <span class="c1"># 检查dd命令</span>
</span><span id="RetroPieInstaller-131"><a href="#RetroPieInstaller-131"><span class="linenos">131</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_run_command</span><span class="p">([</span><span class="s2">&quot;which&quot;</span><span class="p">,</span> <span class="s2">&quot;dd&quot;</span><span class="p">])[</span><span class="mi">0</span><span class="p">]</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="RetroPieInstaller-132"><a href="#RetroPieInstaller-132"><span class="linenos">132</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;找到 dd 命令&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-133"><a href="#RetroPieInstaller-133"><span class="linenos">133</span></a>            <span class="k">return</span> <span class="kc">True</span>
</span><span id="RetroPieInstaller-134"><a href="#RetroPieInstaller-134"><span class="linenos">134</span></a>        
</span><span id="RetroPieInstaller-135"><a href="#RetroPieInstaller-135"><span class="linenos">135</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;未找到 dd 命令&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-136"><a href="#RetroPieInstaller-136"><span class="linenos">136</span></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="RetroPieInstaller-137"><a href="#RetroPieInstaller-137"><span class="linenos">137</span></a>    
</span><span id="RetroPieInstaller-138"><a href="#RetroPieInstaller-138"><span class="linenos">138</span></a>    <span class="k">def</span> <span class="nf">_find_executable</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">name</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="RetroPieInstaller-139"><a href="#RetroPieInstaller-139"><span class="linenos">139</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-140"><a href="#RetroPieInstaller-140"><span class="linenos">140</span></a><span class="sd">        查找可执行文件</span>
</span><span id="RetroPieInstaller-141"><a href="#RetroPieInstaller-141"><span class="linenos">141</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-142"><a href="#RetroPieInstaller-142"><span class="linenos">142</span></a><span class="sd">        Args:</span>
</span><span id="RetroPieInstaller-143"><a href="#RetroPieInstaller-143"><span class="linenos">143</span></a><span class="sd">            name (str): 可执行文件名</span>
</span><span id="RetroPieInstaller-144"><a href="#RetroPieInstaller-144"><span class="linenos">144</span></a><span class="sd">            </span>
</span><span id="RetroPieInstaller-145"><a href="#RetroPieInstaller-145"><span class="linenos">145</span></a><span class="sd">        Returns:</span>
</span><span id="RetroPieInstaller-146"><a href="#RetroPieInstaller-146"><span class="linenos">146</span></a><span class="sd">            Optional[str]: 可执行文件的完整路径，如果未找到则返回None</span>
</span><span id="RetroPieInstaller-147"><a href="#RetroPieInstaller-147"><span class="linenos">147</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-148"><a href="#RetroPieInstaller-148"><span class="linenos">148</span></a>        <span class="k">for</span> <span class="n">path</span> <span class="ow">in</span> <span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;PATH&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">pathsep</span><span class="p">):</span>
</span><span id="RetroPieInstaller-149"><a href="#RetroPieInstaller-149"><span class="linenos">149</span></a>            <span class="n">exe_path</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">path</span><span class="p">)</span> <span class="o">/</span> <span class="n">name</span>
</span><span id="RetroPieInstaller-150"><a href="#RetroPieInstaller-150"><span class="linenos">150</span></a>            <span class="k">if</span> <span class="n">exe_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="RetroPieInstaller-151"><a href="#RetroPieInstaller-151"><span class="linenos">151</span></a>                <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">exe_path</span><span class="p">)</span>
</span><span id="RetroPieInstaller-152"><a href="#RetroPieInstaller-152"><span class="linenos">152</span></a>        <span class="k">return</span> <span class="kc">None</span>
</span><span id="RetroPieInstaller-153"><a href="#RetroPieInstaller-153"><span class="linenos">153</span></a>    
</span><span id="RetroPieInstaller-154"><a href="#RetroPieInstaller-154"><span class="linenos">154</span></a>    <span class="k">def</span> <span class="nf">_run_command</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">cmd</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="n">check</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]:</span>
</span><span id="RetroPieInstaller-155"><a href="#RetroPieInstaller-155"><span class="linenos">155</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-156"><a href="#RetroPieInstaller-156"><span class="linenos">156</span></a><span class="sd">        运行命令并返回结果</span>
</span><span id="RetroPieInstaller-157"><a href="#RetroPieInstaller-157"><span class="linenos">157</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-158"><a href="#RetroPieInstaller-158"><span class="linenos">158</span></a><span class="sd">        Args:</span>
</span><span id="RetroPieInstaller-159"><a href="#RetroPieInstaller-159"><span class="linenos">159</span></a><span class="sd">            cmd (List[str]): 要执行的命令列表</span>
</span><span id="RetroPieInstaller-160"><a href="#RetroPieInstaller-160"><span class="linenos">160</span></a><span class="sd">            check (bool): 是否在命令失败时抛出异常</span>
</span><span id="RetroPieInstaller-161"><a href="#RetroPieInstaller-161"><span class="linenos">161</span></a><span class="sd">            </span>
</span><span id="RetroPieInstaller-162"><a href="#RetroPieInstaller-162"><span class="linenos">162</span></a><span class="sd">        Returns:</span>
</span><span id="RetroPieInstaller-163"><a href="#RetroPieInstaller-163"><span class="linenos">163</span></a><span class="sd">            Tuple[int, str, str]: (返回码, 标准输出, 标准错误)</span>
</span><span id="RetroPieInstaller-164"><a href="#RetroPieInstaller-164"><span class="linenos">164</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-165"><a href="#RetroPieInstaller-165"><span class="linenos">165</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller-166"><a href="#RetroPieInstaller-166"><span class="linenos">166</span></a>            <span class="n">result</span> <span class="o">=</span> <span class="n">subprocess</span><span class="o">.</span><span class="n">run</span><span class="p">(</span>
</span><span id="RetroPieInstaller-167"><a href="#RetroPieInstaller-167"><span class="linenos">167</span></a>                <span class="n">cmd</span><span class="p">,</span> 
</span><span id="RetroPieInstaller-168"><a href="#RetroPieInstaller-168"><span class="linenos">168</span></a>                <span class="n">capture_output</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> 
</span><span id="RetroPieInstaller-169"><a href="#RetroPieInstaller-169"><span class="linenos">169</span></a>                <span class="n">text</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> 
</span><span id="RetroPieInstaller-170"><a href="#RetroPieInstaller-170"><span class="linenos">170</span></a>                <span class="n">check</span><span class="o">=</span><span class="n">check</span>
</span><span id="RetroPieInstaller-171"><a href="#RetroPieInstaller-171"><span class="linenos">171</span></a>            <span class="p">)</span>
</span><span id="RetroPieInstaller-172"><a href="#RetroPieInstaller-172"><span class="linenos">172</span></a>            <span class="k">return</span> <span class="n">result</span><span class="o">.</span><span class="n">returncode</span><span class="p">,</span> <span class="n">result</span><span class="o">.</span><span class="n">stdout</span><span class="p">,</span> <span class="n">result</span><span class="o">.</span><span class="n">stderr</span>
</span><span id="RetroPieInstaller-173"><a href="#RetroPieInstaller-173"><span class="linenos">173</span></a>        <span class="k">except</span> <span class="n">subprocess</span><span class="o">.</span><span class="n">CalledProcessError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="RetroPieInstaller-174"><a href="#RetroPieInstaller-174"><span class="linenos">174</span></a>            <span class="k">return</span> <span class="n">e</span><span class="o">.</span><span class="n">returncode</span><span class="p">,</span> <span class="n">e</span><span class="o">.</span><span class="n">stdout</span><span class="p">,</span> <span class="n">e</span><span class="o">.</span><span class="n">stderr</span>
</span><span id="RetroPieInstaller-175"><a href="#RetroPieInstaller-175"><span class="linenos">175</span></a>    
</span><span id="RetroPieInstaller-176"><a href="#RetroPieInstaller-176"><span class="linenos">176</span></a>    <span class="k">def</span> <span class="nf">get_retropie_download_url</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="RetroPieInstaller-177"><a href="#RetroPieInstaller-177"><span class="linenos">177</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-178"><a href="#RetroPieInstaller-178"><span class="linenos">178</span></a><span class="sd">        获取RetroPie镜像下载链接</span>
</span><span id="RetroPieInstaller-179"><a href="#RetroPieInstaller-179"><span class="linenos">179</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-180"><a href="#RetroPieInstaller-180"><span class="linenos">180</span></a><span class="sd">        From RetroPie官网解析页面内容，查找适合树莓派4B的镜像下载链接。</span>
</span><span id="RetroPieInstaller-181"><a href="#RetroPieInstaller-181"><span class="linenos">181</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-182"><a href="#RetroPieInstaller-182"><span class="linenos">182</span></a><span class="sd">        Returns:</span>
</span><span id="RetroPieInstaller-183"><a href="#RetroPieInstaller-183"><span class="linenos">183</span></a><span class="sd">            Optional[str]: 下载链接URL，如果未找到则返回None</span>
</span><span id="RetroPieInstaller-184"><a href="#RetroPieInstaller-184"><span class="linenos">184</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-185"><a href="#RetroPieInstaller-185"><span class="linenos">185</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;获取RetroPie下载链接...&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-186"><a href="#RetroPieInstaller-186"><span class="linenos">186</span></a>        
</span><span id="RetroPieInstaller-187"><a href="#RetroPieInstaller-187"><span class="linenos">187</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller-188"><a href="#RetroPieInstaller-188"><span class="linenos">188</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">retropie_url</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="RetroPieInstaller-189"><a href="#RetroPieInstaller-189"><span class="linenos">189</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="RetroPieInstaller-190"><a href="#RetroPieInstaller-190"><span class="linenos">190</span></a>            
</span><span id="RetroPieInstaller-191"><a href="#RetroPieInstaller-191"><span class="linenos">191</span></a>            <span class="c1"># 解析页面内容，查找树莓派4B的镜像链接</span>
</span><span id="RetroPieInstaller-192"><a href="#RetroPieInstaller-192"><span class="linenos">192</span></a>            <span class="n">content</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">text</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span>
</span><span id="RetroPieInstaller-193"><a href="#RetroPieInstaller-193"><span class="linenos">193</span></a>            
</span><span id="RetroPieInstaller-194"><a href="#RetroPieInstaller-194"><span class="linenos">194</span></a>            <span class="c1"># 常见的RetroPie镜像文件名模式</span>
</span><span id="RetroPieInstaller-195"><a href="#RetroPieInstaller-195"><span class="linenos">195</span></a>            <span class="n">patterns</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="RetroPieInstaller-196"><a href="#RetroPieInstaller-196"><span class="linenos">196</span></a>                <span class="s2">&quot;retropie-buster-4.8-rpi4_400.img.gz&quot;</span><span class="p">,</span>
</span><span id="RetroPieInstaller-197"><a href="#RetroPieInstaller-197"><span class="linenos">197</span></a>                <span class="s2">&quot;retropie-buster-4.8-rpi4.img.gz&quot;</span><span class="p">,</span>
</span><span id="RetroPieInstaller-198"><a href="#RetroPieInstaller-198"><span class="linenos">198</span></a>                <span class="s2">&quot;retropie-4.8-rpi4.img.gz&quot;</span><span class="p">,</span>
</span><span id="RetroPieInstaller-199"><a href="#RetroPieInstaller-199"><span class="linenos">199</span></a>                <span class="s2">&quot;retropie-buster-rpi4.img.gz&quot;</span>
</span><span id="RetroPieInstaller-200"><a href="#RetroPieInstaller-200"><span class="linenos">200</span></a>            <span class="p">]</span>
</span><span id="RetroPieInstaller-201"><a href="#RetroPieInstaller-201"><span class="linenos">201</span></a>            
</span><span id="RetroPieInstaller-202"><a href="#RetroPieInstaller-202"><span class="linenos">202</span></a>            <span class="k">for</span> <span class="n">pattern</span> <span class="ow">in</span> <span class="n">patterns</span><span class="p">:</span>
</span><span id="RetroPieInstaller-203"><a href="#RetroPieInstaller-203"><span class="linenos">203</span></a>                <span class="k">if</span> <span class="n">pattern</span> <span class="ow">in</span> <span class="n">content</span><span class="p">:</span>
</span><span id="RetroPieInstaller-204"><a href="#RetroPieInstaller-204"><span class="linenos">204</span></a>                    <span class="c1"># 构建完整下载URL</span>
</span><span id="RetroPieInstaller-205"><a href="#RetroPieInstaller-205"><span class="linenos">205</span></a>                    <span class="n">download_url</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;https://github.com/RetroPie/RetroPie-Setup/releases/download/4.8/</span><span class="si">{</span><span class="n">pattern</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="RetroPieInstaller-206"><a href="#RetroPieInstaller-206"><span class="linenos">206</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到下载链接: </span><span class="si">{</span><span class="n">download_url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-207"><a href="#RetroPieInstaller-207"><span class="linenos">207</span></a>                    <span class="k">return</span> <span class="n">download_url</span>
</span><span id="RetroPieInstaller-208"><a href="#RetroPieInstaller-208"><span class="linenos">208</span></a>            
</span><span id="RetroPieInstaller-209"><a href="#RetroPieInstaller-209"><span class="linenos">209</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;未找到合适的镜像链接，请手动下载&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-210"><a href="#RetroPieInstaller-210"><span class="linenos">210</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="RetroPieInstaller-211"><a href="#RetroPieInstaller-211"><span class="linenos">211</span></a>            
</span><span id="RetroPieInstaller-212"><a href="#RetroPieInstaller-212"><span class="linenos">212</span></a>        <span class="k">except</span> <span class="n">requests</span><span class="o">.</span><span class="n">RequestException</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="RetroPieInstaller-213"><a href="#RetroPieInstaller-213"><span class="linenos">213</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;获取下载链接失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-214"><a href="#RetroPieInstaller-214"><span class="linenos">214</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="RetroPieInstaller-215"><a href="#RetroPieInstaller-215"><span class="linenos">215</span></a>    
</span><span id="RetroPieInstaller-216"><a href="#RetroPieInstaller-216"><span class="linenos">216</span></a>    <span class="k">def</span> <span class="nf">download_image</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">url</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Path</span><span class="p">]:</span>
</span><span id="RetroPieInstaller-217"><a href="#RetroPieInstaller-217"><span class="linenos">217</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-218"><a href="#RetroPieInstaller-218"><span class="linenos">218</span></a><span class="sd">        下载RetroPie镜像</span>
</span><span id="RetroPieInstaller-219"><a href="#RetroPieInstaller-219"><span class="linenos">219</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-220"><a href="#RetroPieInstaller-220"><span class="linenos">220</span></a><span class="sd">        Support断点续传的镜像下载，显示下载进度。</span>
</span><span id="RetroPieInstaller-221"><a href="#RetroPieInstaller-221"><span class="linenos">221</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller-222"><a href="#RetroPieInstaller-222"><span class="linenos">222</span></a><span class="sd">        Args:</span>
</span><span id="RetroPieInstaller-223"><a href="#RetroPieInstaller-223"><span class="linenos">223</span></a><span class="sd">            url (str): 镜像下载链接</span>
</span><span id="RetroPieInstaller-224"><a href="#RetroPieInstaller-224"><span class="linenos">224</span></a><span class="sd">            </span>
</span><span id="RetroPieInstaller-225"><a href="#RetroPieInstaller-225"><span class="linenos">225</span></a><span class="sd">        Returns:</span>
</span><span id="RetroPieInstaller-226"><a href="#RetroPieInstaller-226"><span class="linenos">226</span></a><span class="sd">            Optional[Path]: 下载文件的路径，如果下载失败则返回None</span>
</span><span id="RetroPieInstaller-227"><a href="#RetroPieInstaller-227"><span class="linenos">227</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-228"><a href="#RetroPieInstaller-228"><span class="linenos">228</span></a>        <span class="n">filename</span> <span class="o">=</span> <span class="n">url</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">)[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
</span><span id="RetroPieInstaller-229"><a href="#RetroPieInstaller-229"><span class="linenos">229</span></a>        <span class="n">file_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="n">filename</span>
</span><span id="RetroPieInstaller-230"><a href="#RetroPieInstaller-230"><span class="linenos">230</span></a>        
</span><span id="RetroPieInstaller-231"><a href="#RetroPieInstaller-231"><span class="linenos">231</span></a>        <span class="k">if</span> <span class="n">file_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="RetroPieInstaller-232"><a href="#RetroPieInstaller-232"><span class="linenos">232</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像文件已存在: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-233"><a href="#RetroPieInstaller-233"><span class="linenos">233</span></a>            <span class="k">return</span> <span class="n">file_path</span>
</span><span id="RetroPieInstaller-234"><a href="#RetroPieInstaller-234"><span class="linenos">234</span></a>        
</span><span id="RetroPieInstaller-235"><a href="#RetroPieInstaller-235"><span class="linenos">235</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;开始下载镜像: </span><span class="si">{</span><span class="n">url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-236"><a href="#RetroPieInstaller-236"><span class="linenos">236</span></a>        
</span><span id="RetroPieInstaller-237"><a href="#RetroPieInstaller-237"><span class="linenos">237</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller-238"><a href="#RetroPieInstaller-238"><span class="linenos">238</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">stream</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="RetroPieInstaller-239"><a href="#RetroPieInstaller-239"><span class="linenos">239</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="RetroPieInstaller-240"><a href="#RetroPieInstaller-240"><span class="linenos">240</span></a>            
</span><span id="RetroPieInstaller-241"><a href="#RetroPieInstaller-241"><span class="linenos">241</span></a>            <span class="n">total_size</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">headers</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;content-length&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
</span><span id="RetroPieInstaller-242"><a href="#RetroPieInstaller-242"><span class="linenos">242</span></a>            <span class="n">downloaded</span> <span class="o">=</span> <span class="mi">0</span>
</span><span id="RetroPieInstaller-243"><a href="#RetroPieInstaller-243"><span class="linenos">243</span></a>            
</span><span id="RetroPieInstaller-244"><a href="#RetroPieInstaller-244"><span class="linenos">244</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">file_path</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="RetroPieInstaller-245"><a href="#RetroPieInstaller-245"><span class="linenos">245</span></a>                <span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="n">response</span><span class="o">.</span><span class="n">iter_content</span><span class="p">(</span><span class="n">chunk_size</span><span class="o">=</span><span class="mi">8192</span><span class="p">):</span>
</span><span id="RetroPieInstaller-246"><a href="#RetroPieInstaller-246"><span class="linenos">246</span></a>                    <span class="k">if</span> <span class="n">chunk</span><span class="p">:</span>
</span><span id="RetroPieInstaller-247"><a href="#RetroPieInstaller-247"><span class="linenos">247</span></a>                        <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</span><span id="RetroPieInstaller-248"><a href="#RetroPieInstaller-248"><span class="linenos">248</span></a>                        <span class="n">downloaded</span> <span class="o">+=</span> <span class="nb">len</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</span><span id="RetroPieInstaller-249"><a href="#RetroPieInstaller-249"><span class="linenos">249</span></a>                        
</span><span id="RetroPieInstaller-250"><a href="#RetroPieInstaller-250"><span class="linenos">250</span></a>                        <span class="k">if</span> <span class="n">total_size</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="RetroPieInstaller-251"><a href="#RetroPieInstaller-251"><span class="linenos">251</span></a>                            <span class="n">progress</span> <span class="o">=</span> <span class="p">(</span><span class="n">downloaded</span> <span class="o">/</span> <span class="n">total_size</span><span class="p">)</span> <span class="o">*</span> <span class="mi">100</span>
</span><span id="RetroPieInstaller-252"><a href="#RetroPieInstaller-252"><span class="linenos">252</span></a>                            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="se">\r</span><span class="s2">下载进度: </span><span class="si">{</span><span class="n">progress</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">%&quot;</span><span class="p">,</span> <span class="n">end</span><span class="o">=</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">flush</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="RetroPieInstaller-253"><a href="#RetroPieInstaller-253"><span class="linenos">253</span></a>            
</span><span id="RetroPieInstaller-254"><a href="#RetroPieInstaller-254"><span class="linenos">254</span></a>            <span class="nb">print</span><span class="p">()</span>  <span class="c1"># 换行</span>
</span><span id="RetroPieInstaller-255"><a href="#RetroPieInstaller-255"><span class="linenos">255</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载完成: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-256"><a href="#RetroPieInstaller-256"><span class="linenos">256</span></a>            <span class="k">return</span> <span class="n">file_path</span>
</span><span id="RetroPieInstaller-257"><a href="#RetroPieInstaller-257"><span class="linenos">257</span></a>            
</span><span id="RetroPieInstaller-258"><a href="#RetroPieInstaller-258"><span class="linenos">258</span></a>        <span class="k">except</span> <span class="n">requests</span><span class="o">.</span><span class="n">RequestException</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="RetroPieInstaller-259"><a href="#RetroPieInstaller-259"><span class="linenos">259</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-260"><a href="#RetroPieInstaller-260"><span class="linenos">260</span></a>            <span class="k">if</span> <span class="n">file_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="RetroPieInstaller-261"><a href="#RetroPieInstaller-261"><span class="linenos">261</span></a>                <span class="n">file_path</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
</span><span id="RetroPieInstaller-262"><a href="#RetroPieInstaller-262"><span class="linenos">262</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="RetroPieInstaller-263"><a href="#RetroPieInstaller-263"><span class="linenos">263</span></a>    
</span><span id="RetroPieInstaller-264"><a href="#RetroPieInstaller-264"><span class="linenos">264</span></a>    <span class="k">def</span> <span class="nf">extract_image</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">archive_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Path</span><span class="p">]:</span>
</span><span id="RetroPieInstaller-265"><a href="#RetroPieInstaller-265"><span class="linenos">265</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;解压镜像文件&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-266"><a href="#RetroPieInstaller-266"><span class="linenos">266</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压镜像文件: </span><span class="si">{</span><span class="n">archive_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-267"><a href="#RetroPieInstaller-267"><span class="linenos">267</span></a>        
</span><span id="RetroPieInstaller-268"><a href="#RetroPieInstaller-268"><span class="linenos">268</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller-269"><a href="#RetroPieInstaller-269"><span class="linenos">269</span></a>            <span class="k">if</span> <span class="n">archive_path</span><span class="o">.</span><span class="n">suffix</span> <span class="o">==</span> <span class="s1">&#39;.gz&#39;</span><span class="p">:</span>
</span><span id="RetroPieInstaller-270"><a href="#RetroPieInstaller-270"><span class="linenos">270</span></a>                <span class="c1"># 处理 .img.gz 文件</span>
</span><span id="RetroPieInstaller-271"><a href="#RetroPieInstaller-271"><span class="linenos">271</span></a>                <span class="n">img_path</span> <span class="o">=</span> <span class="n">archive_path</span><span class="o">.</span><span class="n">with_suffix</span><span class="p">(</span><span class="s1">&#39;&#39;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-272"><a href="#RetroPieInstaller-272"><span class="linenos">272</span></a>                <span class="k">if</span> <span class="n">img_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="RetroPieInstaller-273"><a href="#RetroPieInstaller-273"><span class="linenos">273</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像文件已存在: </span><span class="si">{</span><span class="n">img_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-274"><a href="#RetroPieInstaller-274"><span class="linenos">274</span></a>                    <span class="k">return</span> <span class="n">img_path</span>
</span><span id="RetroPieInstaller-275"><a href="#RetroPieInstaller-275"><span class="linenos">275</span></a>                
</span><span id="RetroPieInstaller-276"><a href="#RetroPieInstaller-276"><span class="linenos">276</span></a>                <span class="kn">import</span> <span class="nn">gzip</span>
</span><span id="RetroPieInstaller-277"><a href="#RetroPieInstaller-277"><span class="linenos">277</span></a>                <span class="k">with</span> <span class="n">gzip</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="n">archive_path</span><span class="p">,</span> <span class="s1">&#39;rb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f_in</span><span class="p">:</span>
</span><span id="RetroPieInstaller-278"><a href="#RetroPieInstaller-278"><span class="linenos">278</span></a>                    <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">img_path</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f_out</span><span class="p">:</span>
</span><span id="RetroPieInstaller-279"><a href="#RetroPieInstaller-279"><span class="linenos">279</span></a>                        <span class="n">f_out</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">f_in</span><span class="o">.</span><span class="n">read</span><span class="p">())</span>
</span><span id="RetroPieInstaller-280"><a href="#RetroPieInstaller-280"><span class="linenos">280</span></a>                
</span><span id="RetroPieInstaller-281"><a href="#RetroPieInstaller-281"><span class="linenos">281</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压完成: </span><span class="si">{</span><span class="n">img_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-282"><a href="#RetroPieInstaller-282"><span class="linenos">282</span></a>                <span class="k">return</span> <span class="n">img_path</span>
</span><span id="RetroPieInstaller-283"><a href="#RetroPieInstaller-283"><span class="linenos">283</span></a>                
</span><span id="RetroPieInstaller-284"><a href="#RetroPieInstaller-284"><span class="linenos">284</span></a>            <span class="k">elif</span> <span class="n">archive_path</span><span class="o">.</span><span class="n">suffix</span> <span class="o">==</span> <span class="s1">&#39;.zip&#39;</span><span class="p">:</span>
</span><span id="RetroPieInstaller-285"><a href="#RetroPieInstaller-285"><span class="linenos">285</span></a>                <span class="c1"># 处理 .zip 文件</span>
</span><span id="RetroPieInstaller-286"><a href="#RetroPieInstaller-286"><span class="linenos">286</span></a>                <span class="k">with</span> <span class="n">zipfile</span><span class="o">.</span><span class="n">ZipFile</span><span class="p">(</span><span class="n">archive_path</span><span class="p">,</span> <span class="s1">&#39;r&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">zip_ref</span><span class="p">:</span>
</span><span id="RetroPieInstaller-287"><a href="#RetroPieInstaller-287"><span class="linenos">287</span></a>                    <span class="c1"># 查找 .img 文件</span>
</span><span id="RetroPieInstaller-288"><a href="#RetroPieInstaller-288"><span class="linenos">288</span></a>                    <span class="n">img_files</span> <span class="o">=</span> <span class="p">[</span><span class="n">f</span> <span class="k">for</span> <span class="n">f</span> <span class="ow">in</span> <span class="n">zip_ref</span><span class="o">.</span><span class="n">namelist</span><span class="p">()</span> <span class="k">if</span> <span class="n">f</span><span class="o">.</span><span class="n">endswith</span><span class="p">(</span><span class="s1">&#39;.img&#39;</span><span class="p">)]</span>
</span><span id="RetroPieInstaller-289"><a href="#RetroPieInstaller-289"><span class="linenos">289</span></a>                    <span class="k">if</span> <span class="ow">not</span> <span class="n">img_files</span><span class="p">:</span>
</span><span id="RetroPieInstaller-290"><a href="#RetroPieInstaller-290"><span class="linenos">290</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;ZIP文件中未找到.img文件&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-291"><a href="#RetroPieInstaller-291"><span class="linenos">291</span></a>                        <span class="k">return</span> <span class="kc">None</span>
</span><span id="RetroPieInstaller-292"><a href="#RetroPieInstaller-292"><span class="linenos">292</span></a>                    
</span><span id="RetroPieInstaller-293"><a href="#RetroPieInstaller-293"><span class="linenos">293</span></a>                    <span class="n">img_filename</span> <span class="o">=</span> <span class="n">img_files</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
</span><span id="RetroPieInstaller-294"><a href="#RetroPieInstaller-294"><span class="linenos">294</span></a>                    <span class="n">img_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="n">Path</span><span class="p">(</span><span class="n">img_filename</span><span class="p">)</span><span class="o">.</span><span class="n">name</span>
</span><span id="RetroPieInstaller-295"><a href="#RetroPieInstaller-295"><span class="linenos">295</span></a>                    
</span><span id="RetroPieInstaller-296"><a href="#RetroPieInstaller-296"><span class="linenos">296</span></a>                    <span class="k">if</span> <span class="n">img_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="RetroPieInstaller-297"><a href="#RetroPieInstaller-297"><span class="linenos">297</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像文件已存在: </span><span class="si">{</span><span class="n">img_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-298"><a href="#RetroPieInstaller-298"><span class="linenos">298</span></a>                        <span class="k">return</span> <span class="n">img_path</span>
</span><span id="RetroPieInstaller-299"><a href="#RetroPieInstaller-299"><span class="linenos">299</span></a>                    
</span><span id="RetroPieInstaller-300"><a href="#RetroPieInstaller-300"><span class="linenos">300</span></a>                    <span class="k">with</span> <span class="n">zip_ref</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="n">img_filename</span><span class="p">)</span> <span class="k">as</span> <span class="n">source</span><span class="p">,</span> <span class="nb">open</span><span class="p">(</span><span class="n">img_path</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">target</span><span class="p">:</span>
</span><span id="RetroPieInstaller-301"><a href="#RetroPieInstaller-301"><span class="linenos">301</span></a>                        <span class="n">target</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">source</span><span class="o">.</span><span class="n">read</span><span class="p">())</span>
</span><span id="RetroPieInstaller-302"><a href="#RetroPieInstaller-302"><span class="linenos">302</span></a>                    
</span><span id="RetroPieInstaller-303"><a href="#RetroPieInstaller-303"><span class="linenos">303</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压完成: </span><span class="si">{</span><span class="n">img_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-304"><a href="#RetroPieInstaller-304"><span class="linenos">304</span></a>                    <span class="k">return</span> <span class="n">img_path</span>
</span><span id="RetroPieInstaller-305"><a href="#RetroPieInstaller-305"><span class="linenos">305</span></a>                    
</span><span id="RetroPieInstaller-306"><a href="#RetroPieInstaller-306"><span class="linenos">306</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="RetroPieInstaller-307"><a href="#RetroPieInstaller-307"><span class="linenos">307</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;不支持的文件格式: </span><span class="si">{</span><span class="n">archive_path</span><span class="o">.</span><span class="n">suffix</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-308"><a href="#RetroPieInstaller-308"><span class="linenos">308</span></a>                <span class="k">return</span> <span class="kc">None</span>
</span><span id="RetroPieInstaller-309"><a href="#RetroPieInstaller-309"><span class="linenos">309</span></a>                
</span><span id="RetroPieInstaller-310"><a href="#RetroPieInstaller-310"><span class="linenos">310</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="RetroPieInstaller-311"><a href="#RetroPieInstaller-311"><span class="linenos">311</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-312"><a href="#RetroPieInstaller-312"><span class="linenos">312</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="RetroPieInstaller-313"><a href="#RetroPieInstaller-313"><span class="linenos">313</span></a>    
</span><span id="RetroPieInstaller-314"><a href="#RetroPieInstaller-314"><span class="linenos">314</span></a>    <span class="k">def</span> <span class="nf">list_available_disks</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]:</span>
</span><span id="RetroPieInstaller-315"><a href="#RetroPieInstaller-315"><span class="linenos">315</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;列出可用的磁盘&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-316"><a href="#RetroPieInstaller-316"><span class="linenos">316</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;扫描可用磁盘...&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-317"><a href="#RetroPieInstaller-317"><span class="linenos">317</span></a>        <span class="n">disks</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="RetroPieInstaller-318"><a href="#RetroPieInstaller-318"><span class="linenos">318</span></a>        
</span><span id="RetroPieInstaller-319"><a href="#RetroPieInstaller-319"><span class="linenos">319</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Windows&quot;</span><span class="p">:</span>
</span><span id="RetroPieInstaller-320"><a href="#RetroPieInstaller-320"><span class="linenos">320</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_list_windows_disks</span><span class="p">()</span>
</span><span id="RetroPieInstaller-321"><a href="#RetroPieInstaller-321"><span class="linenos">321</span></a>        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;Linux&quot;</span><span class="p">,</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">]:</span>
</span><span id="RetroPieInstaller-322"><a href="#RetroPieInstaller-322"><span class="linenos">322</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_list_unix_disks</span><span class="p">()</span>
</span><span id="RetroPieInstaller-323"><a href="#RetroPieInstaller-323"><span class="linenos">323</span></a>        
</span><span id="RetroPieInstaller-324"><a href="#RetroPieInstaller-324"><span class="linenos">324</span></a>        <span class="k">return</span> <span class="n">disks</span>
</span><span id="RetroPieInstaller-325"><a href="#RetroPieInstaller-325"><span class="linenos">325</span></a>    
</span><span id="RetroPieInstaller-326"><a href="#RetroPieInstaller-326"><span class="linenos">326</span></a>    <span class="k">def</span> <span class="nf">_list_windows_disks</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]:</span>
</span><span id="RetroPieInstaller-327"><a href="#RetroPieInstaller-327"><span class="linenos">327</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;列出Windows磁盘&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-328"><a href="#RetroPieInstaller-328"><span class="linenos">328</span></a>        <span class="n">disks</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="RetroPieInstaller-329"><a href="#RetroPieInstaller-329"><span class="linenos">329</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller-330"><a href="#RetroPieInstaller-330"><span class="linenos">330</span></a>            <span class="c1"># 使用 PowerShell 获取磁盘信息</span>
</span><span id="RetroPieInstaller-331"><a href="#RetroPieInstaller-331"><span class="linenos">331</span></a>            <span class="n">cmd</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="RetroPieInstaller-332"><a href="#RetroPieInstaller-332"><span class="linenos">332</span></a>                <span class="s2">&quot;powershell&quot;</span><span class="p">,</span> 
</span><span id="RetroPieInstaller-333"><a href="#RetroPieInstaller-333"><span class="linenos">333</span></a>                <span class="s2">&quot;Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID,Size,FreeSpace,VolumeName | ConvertTo-Json&quot;</span>
</span><span id="RetroPieInstaller-334"><a href="#RetroPieInstaller-334"><span class="linenos">334</span></a>            <span class="p">]</span>
</span><span id="RetroPieInstaller-335"><a href="#RetroPieInstaller-335"><span class="linenos">335</span></a>            
</span><span id="RetroPieInstaller-336"><a href="#RetroPieInstaller-336"><span class="linenos">336</span></a>            <span class="n">returncode</span><span class="p">,</span> <span class="n">stdout</span><span class="p">,</span> <span class="n">stderr</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_run_command</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="n">check</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</span><span id="RetroPieInstaller-337"><a href="#RetroPieInstaller-337"><span class="linenos">337</span></a>            
</span><span id="RetroPieInstaller-338"><a href="#RetroPieInstaller-338"><span class="linenos">338</span></a>            <span class="k">if</span> <span class="n">returncode</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="RetroPieInstaller-339"><a href="#RetroPieInstaller-339"><span class="linenos">339</span></a>                <span class="kn">import</span> <span class="nn">json</span>
</span><span id="RetroPieInstaller-340"><a href="#RetroPieInstaller-340"><span class="linenos">340</span></a>                <span class="n">disks_data</span> <span class="o">=</span> <span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="n">stdout</span><span class="p">)</span>
</span><span id="RetroPieInstaller-341"><a href="#RetroPieInstaller-341"><span class="linenos">341</span></a>                <span class="k">if</span> <span class="ow">not</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">disks_data</span><span class="p">,</span> <span class="nb">list</span><span class="p">):</span>
</span><span id="RetroPieInstaller-342"><a href="#RetroPieInstaller-342"><span class="linenos">342</span></a>                    <span class="n">disks_data</span> <span class="o">=</span> <span class="p">[</span><span class="n">disks_data</span><span class="p">]</span>
</span><span id="RetroPieInstaller-343"><a href="#RetroPieInstaller-343"><span class="linenos">343</span></a>                
</span><span id="RetroPieInstaller-344"><a href="#RetroPieInstaller-344"><span class="linenos">344</span></a>                <span class="k">for</span> <span class="n">disk</span> <span class="ow">in</span> <span class="n">disks_data</span><span class="p">:</span>
</span><span id="RetroPieInstaller-345"><a href="#RetroPieInstaller-345"><span class="linenos">345</span></a>                    <span class="n">device_id</span> <span class="o">=</span> <span class="n">disk</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;DeviceID&#39;</span><span class="p">,</span> <span class="s1">&#39;&#39;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-346"><a href="#RetroPieInstaller-346"><span class="linenos">346</span></a>                    <span class="n">size</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">disk</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;Size&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
</span><span id="RetroPieInstaller-347"><a href="#RetroPieInstaller-347"><span class="linenos">347</span></a>                    <span class="n">free_space</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">disk</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;FreeSpace&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
</span><span id="RetroPieInstaller-348"><a href="#RetroPieInstaller-348"><span class="linenos">348</span></a>                    <span class="n">volume_name</span> <span class="o">=</span> <span class="n">disk</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;VolumeName&#39;</span><span class="p">,</span> <span class="s1">&#39;Unknown&#39;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-349"><a href="#RetroPieInstaller-349"><span class="linenos">349</span></a>                    
</span><span id="RetroPieInstaller-350"><a href="#RetroPieInstaller-350"><span class="linenos">350</span></a>                    <span class="c1"># 只显示可移动磁盘或大容量磁盘</span>
</span><span id="RetroPieInstaller-351"><a href="#RetroPieInstaller-351"><span class="linenos">351</span></a>                    <span class="k">if</span> <span class="n">size</span> <span class="o">&gt;</span> <span class="mi">1024</span><span class="o">**</span><span class="mi">3</span><span class="p">:</span>  <span class="c1"># 大于1GB</span>
</span><span id="RetroPieInstaller-352"><a href="#RetroPieInstaller-352"><span class="linenos">352</span></a>                        <span class="n">size_gb</span> <span class="o">=</span> <span class="n">size</span> <span class="o">/</span> <span class="p">(</span><span class="mi">1024</span><span class="o">**</span><span class="mi">3</span><span class="p">)</span>
</span><span id="RetroPieInstaller-353"><a href="#RetroPieInstaller-353"><span class="linenos">353</span></a>                        <span class="n">free_gb</span> <span class="o">=</span> <span class="n">free_space</span> <span class="o">/</span> <span class="p">(</span><span class="mi">1024</span><span class="o">**</span><span class="mi">3</span><span class="p">)</span>
</span><span id="RetroPieInstaller-354"><a href="#RetroPieInstaller-354"><span class="linenos">354</span></a>                        <span class="n">disks</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">device_id</span><span class="p">,</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">size_gb</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">GB&quot;</span><span class="p">,</span> <span class="n">volume_name</span><span class="p">))</span>
</span><span id="RetroPieInstaller-355"><a href="#RetroPieInstaller-355"><span class="linenos">355</span></a>                
</span><span id="RetroPieInstaller-356"><a href="#RetroPieInstaller-356"><span class="linenos">356</span></a>                <span class="k">return</span> <span class="n">disks</span>
</span><span id="RetroPieInstaller-357"><a href="#RetroPieInstaller-357"><span class="linenos">357</span></a>                
</span><span id="RetroPieInstaller-358"><a href="#RetroPieInstaller-358"><span class="linenos">358</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="RetroPieInstaller-359"><a href="#RetroPieInstaller-359"><span class="linenos">359</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;获取Windows磁盘信息失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-360"><a href="#RetroPieInstaller-360"><span class="linenos">360</span></a>        
</span><span id="RetroPieInstaller-361"><a href="#RetroPieInstaller-361"><span class="linenos">361</span></a>        <span class="k">return</span> <span class="n">disks</span>
</span><span id="RetroPieInstaller-362"><a href="#RetroPieInstaller-362"><span class="linenos">362</span></a>    
</span><span id="RetroPieInstaller-363"><a href="#RetroPieInstaller-363"><span class="linenos">363</span></a>    <span class="k">def</span> <span class="nf">_list_unix_disks</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]:</span>
</span><span id="RetroPieInstaller-364"><a href="#RetroPieInstaller-364"><span class="linenos">364</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;列出Unix磁盘&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-365"><a href="#RetroPieInstaller-365"><span class="linenos">365</span></a>        <span class="n">disks</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="RetroPieInstaller-366"><a href="#RetroPieInstaller-366"><span class="linenos">366</span></a>        
</span><span id="RetroPieInstaller-367"><a href="#RetroPieInstaller-367"><span class="linenos">367</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller-368"><a href="#RetroPieInstaller-368"><span class="linenos">368</span></a>            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">:</span>  <span class="c1"># macOS</span>
</span><span id="RetroPieInstaller-369"><a href="#RetroPieInstaller-369"><span class="linenos">369</span></a>                <span class="n">cmd</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;diskutil&quot;</span><span class="p">,</span> <span class="s2">&quot;list&quot;</span><span class="p">]</span>
</span><span id="RetroPieInstaller-370"><a href="#RetroPieInstaller-370"><span class="linenos">370</span></a>            <span class="k">else</span><span class="p">:</span>  <span class="c1"># Linux</span>
</span><span id="RetroPieInstaller-371"><a href="#RetroPieInstaller-371"><span class="linenos">371</span></a>                <span class="n">cmd</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;lsblk&quot;</span><span class="p">,</span> <span class="s2">&quot;-o&quot;</span><span class="p">,</span> <span class="s2">&quot;NAME,SIZE,TYPE,MOUNTPOINT&quot;</span><span class="p">,</span> <span class="s2">&quot;-J&quot;</span><span class="p">]</span>
</span><span id="RetroPieInstaller-372"><a href="#RetroPieInstaller-372"><span class="linenos">372</span></a>            
</span><span id="RetroPieInstaller-373"><a href="#RetroPieInstaller-373"><span class="linenos">373</span></a>            <span class="n">returncode</span><span class="p">,</span> <span class="n">stdout</span><span class="p">,</span> <span class="n">stderr</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_run_command</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="n">check</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</span><span id="RetroPieInstaller-374"><a href="#RetroPieInstaller-374"><span class="linenos">374</span></a>            
</span><span id="RetroPieInstaller-375"><a href="#RetroPieInstaller-375"><span class="linenos">375</span></a>            <span class="k">if</span> <span class="n">returncode</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="RetroPieInstaller-376"><a href="#RetroPieInstaller-376"><span class="linenos">376</span></a>                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">:</span>
</span><span id="RetroPieInstaller-377"><a href="#RetroPieInstaller-377"><span class="linenos">377</span></a>                    <span class="c1"># 解析 macOS diskutil 输出</span>
</span><span id="RetroPieInstaller-378"><a href="#RetroPieInstaller-378"><span class="linenos">378</span></a>                    <span class="n">lines</span> <span class="o">=</span> <span class="n">stdout</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;</span><span class="se">\n</span><span class="s1">&#39;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-379"><a href="#RetroPieInstaller-379"><span class="linenos">379</span></a>                    <span class="k">for</span> <span class="n">line</span> <span class="ow">in</span> <span class="n">lines</span><span class="p">:</span>
</span><span id="RetroPieInstaller-380"><a href="#RetroPieInstaller-380"><span class="linenos">380</span></a>                        <span class="k">if</span> <span class="s1">&#39;/dev/disk&#39;</span> <span class="ow">in</span> <span class="n">line</span> <span class="ow">and</span> <span class="s1">&#39;external&#39;</span> <span class="ow">in</span> <span class="n">line</span><span class="o">.</span><span class="n">lower</span><span class="p">():</span>
</span><span id="RetroPieInstaller-381"><a href="#RetroPieInstaller-381"><span class="linenos">381</span></a>                            <span class="n">parts</span> <span class="o">=</span> <span class="n">line</span><span class="o">.</span><span class="n">split</span><span class="p">()</span>
</span><span id="RetroPieInstaller-382"><a href="#RetroPieInstaller-382"><span class="linenos">382</span></a>                            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">parts</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="mi">3</span><span class="p">:</span>
</span><span id="RetroPieInstaller-383"><a href="#RetroPieInstaller-383"><span class="linenos">383</span></a>                                <span class="n">disk_name</span> <span class="o">=</span> <span class="n">parts</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
</span><span id="RetroPieInstaller-384"><a href="#RetroPieInstaller-384"><span class="linenos">384</span></a>                                <span class="n">size</span> <span class="o">=</span> <span class="n">parts</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">parts</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">1</span> <span class="k">else</span> <span class="s2">&quot;Unknown&quot;</span>
</span><span id="RetroPieInstaller-385"><a href="#RetroPieInstaller-385"><span class="linenos">385</span></a>                                <span class="n">disks</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">disk_name</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="s2">&quot;External Disk&quot;</span><span class="p">))</span>
</span><span id="RetroPieInstaller-386"><a href="#RetroPieInstaller-386"><span class="linenos">386</span></a>                <span class="k">else</span><span class="p">:</span>
</span><span id="RetroPieInstaller-387"><a href="#RetroPieInstaller-387"><span class="linenos">387</span></a>                    <span class="c1"># 解析 Linux lsblk 输出</span>
</span><span id="RetroPieInstaller-388"><a href="#RetroPieInstaller-388"><span class="linenos">388</span></a>                    <span class="kn">import</span> <span class="nn">json</span>
</span><span id="RetroPieInstaller-389"><a href="#RetroPieInstaller-389"><span class="linenos">389</span></a>                    <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller-390"><a href="#RetroPieInstaller-390"><span class="linenos">390</span></a>                        <span class="n">data</span> <span class="o">=</span> <span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="n">stdout</span><span class="p">)</span>
</span><span id="RetroPieInstaller-391"><a href="#RetroPieInstaller-391"><span class="linenos">391</span></a>                        <span class="k">for</span> <span class="n">device</span> <span class="ow">in</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;blockdevices&#39;</span><span class="p">,</span> <span class="p">[]):</span>
</span><span id="RetroPieInstaller-392"><a href="#RetroPieInstaller-392"><span class="linenos">392</span></a>                            <span class="k">if</span> <span class="n">device</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;type&#39;</span><span class="p">)</span> <span class="o">==</span> <span class="s1">&#39;disk&#39;</span><span class="p">:</span>
</span><span id="RetroPieInstaller-393"><a href="#RetroPieInstaller-393"><span class="linenos">393</span></a>                                <span class="n">name</span> <span class="o">=</span> <span class="n">device</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;name&#39;</span><span class="p">,</span> <span class="s1">&#39;&#39;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-394"><a href="#RetroPieInstaller-394"><span class="linenos">394</span></a>                                <span class="n">size</span> <span class="o">=</span> <span class="n">device</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;size&#39;</span><span class="p">,</span> <span class="s1">&#39;Unknown&#39;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-395"><a href="#RetroPieInstaller-395"><span class="linenos">395</span></a>                                <span class="n">mountpoint</span> <span class="o">=</span> <span class="n">device</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;mountpoint&#39;</span><span class="p">,</span> <span class="s1">&#39;&#39;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-396"><a href="#RetroPieInstaller-396"><span class="linenos">396</span></a>                                <span class="n">disks</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">name</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="n">mountpoint</span> <span class="ow">or</span> <span class="s2">&quot;Unmounted&quot;</span><span class="p">))</span>
</span><span id="RetroPieInstaller-397"><a href="#RetroPieInstaller-397"><span class="linenos">397</span></a>                    <span class="k">except</span> <span class="n">json</span><span class="o">.</span><span class="n">JSONDecodeError</span><span class="p">:</span>
</span><span id="RetroPieInstaller-398"><a href="#RetroPieInstaller-398"><span class="linenos">398</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;无法解析lsblk输出&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-399"><a href="#RetroPieInstaller-399"><span class="linenos">399</span></a>            
</span><span id="RetroPieInstaller-400"><a href="#RetroPieInstaller-400"><span class="linenos">400</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="RetroPieInstaller-401"><a href="#RetroPieInstaller-401"><span class="linenos">401</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;获取Unix磁盘信息失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-402"><a href="#RetroPieInstaller-402"><span class="linenos">402</span></a>        
</span><span id="RetroPieInstaller-403"><a href="#RetroPieInstaller-403"><span class="linenos">403</span></a>        <span class="k">return</span> <span class="n">disks</span>
</span><span id="RetroPieInstaller-404"><a href="#RetroPieInstaller-404"><span class="linenos">404</span></a>    
</span><span id="RetroPieInstaller-405"><a href="#RetroPieInstaller-405"><span class="linenos">405</span></a>    <span class="k">def</span> <span class="nf">burn_image</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">image_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="RetroPieInstaller-406"><a href="#RetroPieInstaller-406"><span class="linenos">406</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;烧录镜像到指定磁盘&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-407"><a href="#RetroPieInstaller-407"><span class="linenos">407</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;开始烧录镜像到 </span><span class="si">{</span><span class="n">target_disk</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-408"><a href="#RetroPieInstaller-408"><span class="linenos">408</span></a>        
</span><span id="RetroPieInstaller-409"><a href="#RetroPieInstaller-409"><span class="linenos">409</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Windows&quot;</span><span class="p">:</span>
</span><span id="RetroPieInstaller-410"><a href="#RetroPieInstaller-410"><span class="linenos">410</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_burn_windows</span><span class="p">(</span><span class="n">image_path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">)</span>
</span><span id="RetroPieInstaller-411"><a href="#RetroPieInstaller-411"><span class="linenos">411</span></a>        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;Linux&quot;</span><span class="p">,</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">]:</span>
</span><span id="RetroPieInstaller-412"><a href="#RetroPieInstaller-412"><span class="linenos">412</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_burn_unix</span><span class="p">(</span><span class="n">image_path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">)</span>
</span><span id="RetroPieInstaller-413"><a href="#RetroPieInstaller-413"><span class="linenos">413</span></a>        
</span><span id="RetroPieInstaller-414"><a href="#RetroPieInstaller-414"><span class="linenos">414</span></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="RetroPieInstaller-415"><a href="#RetroPieInstaller-415"><span class="linenos">415</span></a>    
</span><span id="RetroPieInstaller-416"><a href="#RetroPieInstaller-416"><span class="linenos">416</span></a>    <span class="k">def</span> <span class="nf">_burn_windows</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">image_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="RetroPieInstaller-417"><a href="#RetroPieInstaller-417"><span class="linenos">417</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;Windows系统烧录&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-418"><a href="#RetroPieInstaller-418"><span class="linenos">418</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;Windows系统需要手动烧录&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-419"><a href="#RetroPieInstaller-419"><span class="linenos">419</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;请使用 Win32DiskImager 或 balenaEtcher 将镜像烧录到 </span><span class="si">{</span><span class="n">target_disk</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-420"><a href="#RetroPieInstaller-420"><span class="linenos">420</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像文件路径: </span><span class="si">{</span><span class="n">image_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-421"><a href="#RetroPieInstaller-421"><span class="linenos">421</span></a>        <span class="k">return</span> <span class="kc">False</span>
</span><span id="RetroPieInstaller-422"><a href="#RetroPieInstaller-422"><span class="linenos">422</span></a>    
</span><span id="RetroPieInstaller-423"><a href="#RetroPieInstaller-423"><span class="linenos">423</span></a>    <span class="k">def</span> <span class="nf">_burn_unix</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">image_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="RetroPieInstaller-424"><a href="#RetroPieInstaller-424"><span class="linenos">424</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;Unix系统烧录&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-425"><a href="#RetroPieInstaller-425"><span class="linenos">425</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;即将烧录镜像到 </span><span class="si">{</span><span class="n">target_disk</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-426"><a href="#RetroPieInstaller-426"><span class="linenos">426</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;此操作将擦除目标磁盘的所有数据！&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-427"><a href="#RetroPieInstaller-427"><span class="linenos">427</span></a>        
</span><span id="RetroPieInstaller-428"><a href="#RetroPieInstaller-428"><span class="linenos">428</span></a>        <span class="c1"># 确认操作</span>
</span><span id="RetroPieInstaller-429"><a href="#RetroPieInstaller-429"><span class="linenos">429</span></a>        <span class="n">confirm</span> <span class="o">=</span> <span class="nb">input</span><span class="p">(</span><span class="s2">&quot;确认继续？(输入 &#39;yes&#39; 确认): &quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-430"><a href="#RetroPieInstaller-430"><span class="linenos">430</span></a>        <span class="k">if</span> <span class="n">confirm</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="o">!=</span> <span class="s1">&#39;yes&#39;</span><span class="p">:</span>
</span><span id="RetroPieInstaller-431"><a href="#RetroPieInstaller-431"><span class="linenos">431</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;操作已取消&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-432"><a href="#RetroPieInstaller-432"><span class="linenos">432</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="RetroPieInstaller-433"><a href="#RetroPieInstaller-433"><span class="linenos">433</span></a>        
</span><span id="RetroPieInstaller-434"><a href="#RetroPieInstaller-434"><span class="linenos">434</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller-435"><a href="#RetroPieInstaller-435"><span class="linenos">435</span></a>            <span class="c1"># 卸载磁盘（如果已挂载）</span>
</span><span id="RetroPieInstaller-436"><a href="#RetroPieInstaller-436"><span class="linenos">436</span></a>            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">:</span>
</span><span id="RetroPieInstaller-437"><a href="#RetroPieInstaller-437"><span class="linenos">437</span></a>                <span class="n">unmount_cmd</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;diskutil&quot;</span><span class="p">,</span> <span class="s2">&quot;unmountDisk&quot;</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">]</span>
</span><span id="RetroPieInstaller-438"><a href="#RetroPieInstaller-438"><span class="linenos">438</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="RetroPieInstaller-439"><a href="#RetroPieInstaller-439"><span class="linenos">439</span></a>                <span class="n">unmount_cmd</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;sudo&quot;</span><span class="p">,</span> <span class="s2">&quot;umount&quot;</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">]</span>
</span><span id="RetroPieInstaller-440"><a href="#RetroPieInstaller-440"><span class="linenos">440</span></a>            
</span><span id="RetroPieInstaller-441"><a href="#RetroPieInstaller-441"><span class="linenos">441</span></a>            <span class="bp">self</span><span class="o">.</span><span class="n">_run_command</span><span class="p">(</span><span class="n">unmount_cmd</span><span class="p">,</span> <span class="n">check</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</span><span id="RetroPieInstaller-442"><a href="#RetroPieInstaller-442"><span class="linenos">442</span></a>            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
</span><span id="RetroPieInstaller-443"><a href="#RetroPieInstaller-443"><span class="linenos">443</span></a>            
</span><span id="RetroPieInstaller-444"><a href="#RetroPieInstaller-444"><span class="linenos">444</span></a>            <span class="c1"># 使用dd命令烧录</span>
</span><span id="RetroPieInstaller-445"><a href="#RetroPieInstaller-445"><span class="linenos">445</span></a>            <span class="n">dd_cmd</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="RetroPieInstaller-446"><a href="#RetroPieInstaller-446"><span class="linenos">446</span></a>                <span class="s2">&quot;sudo&quot;</span><span class="p">,</span> <span class="s2">&quot;dd&quot;</span><span class="p">,</span> 
</span><span id="RetroPieInstaller-447"><a href="#RetroPieInstaller-447"><span class="linenos">447</span></a>                <span class="sa">f</span><span class="s2">&quot;if=</span><span class="si">{</span><span class="n">image_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> 
</span><span id="RetroPieInstaller-448"><a href="#RetroPieInstaller-448"><span class="linenos">448</span></a>                <span class="sa">f</span><span class="s2">&quot;of=</span><span class="si">{</span><span class="n">target_disk</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> 
</span><span id="RetroPieInstaller-449"><a href="#RetroPieInstaller-449"><span class="linenos">449</span></a>                <span class="s2">&quot;bs=4M&quot;</span><span class="p">,</span> 
</span><span id="RetroPieInstaller-450"><a href="#RetroPieInstaller-450"><span class="linenos">450</span></a>                <span class="s2">&quot;status=progress&quot;</span>
</span><span id="RetroPieInstaller-451"><a href="#RetroPieInstaller-451"><span class="linenos">451</span></a>            <span class="p">]</span>
</span><span id="RetroPieInstaller-452"><a href="#RetroPieInstaller-452"><span class="linenos">452</span></a>            
</span><span id="RetroPieInstaller-453"><a href="#RetroPieInstaller-453"><span class="linenos">453</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;开始烧录，这可能需要几分钟...&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-454"><a href="#RetroPieInstaller-454"><span class="linenos">454</span></a>            <span class="n">returncode</span><span class="p">,</span> <span class="n">stdout</span><span class="p">,</span> <span class="n">stderr</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_run_command</span><span class="p">(</span><span class="n">dd_cmd</span><span class="p">)</span>
</span><span id="RetroPieInstaller-455"><a href="#RetroPieInstaller-455"><span class="linenos">455</span></a>            
</span><span id="RetroPieInstaller-456"><a href="#RetroPieInstaller-456"><span class="linenos">456</span></a>            <span class="k">if</span> <span class="n">returncode</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="RetroPieInstaller-457"><a href="#RetroPieInstaller-457"><span class="linenos">457</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;烧录完成！&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-458"><a href="#RetroPieInstaller-458"><span class="linenos">458</span></a>                <span class="k">return</span> <span class="kc">True</span>
</span><span id="RetroPieInstaller-459"><a href="#RetroPieInstaller-459"><span class="linenos">459</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="RetroPieInstaller-460"><a href="#RetroPieInstaller-460"><span class="linenos">460</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;烧录失败: </span><span class="si">{</span><span class="n">stderr</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-461"><a href="#RetroPieInstaller-461"><span class="linenos">461</span></a>                <span class="k">return</span> <span class="kc">False</span>
</span><span id="RetroPieInstaller-462"><a href="#RetroPieInstaller-462"><span class="linenos">462</span></a>                
</span><span id="RetroPieInstaller-463"><a href="#RetroPieInstaller-463"><span class="linenos">463</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="RetroPieInstaller-464"><a href="#RetroPieInstaller-464"><span class="linenos">464</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;烧录过程中出错: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-465"><a href="#RetroPieInstaller-465"><span class="linenos">465</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span><span id="RetroPieInstaller-466"><a href="#RetroPieInstaller-466"><span class="linenos">466</span></a>    
</span><span id="RetroPieInstaller-467"><a href="#RetroPieInstaller-467"><span class="linenos">467</span></a>    <span class="k">def</span> <span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
</span><span id="RetroPieInstaller-468"><a href="#RetroPieInstaller-468"><span class="linenos">468</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;运行主程序&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller-469"><a href="#RetroPieInstaller-469"><span class="linenos">469</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;=== RetroPie 镜像下载和烧录工具 ===&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-470"><a href="#RetroPieInstaller-470"><span class="linenos">470</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;操作系统: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">system</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-471"><a href="#RetroPieInstaller-471"><span class="linenos">471</span></a>        
</span><span id="RetroPieInstaller-472"><a href="#RetroPieInstaller-472"><span class="linenos">472</span></a>        <span class="c1"># 检查依赖</span>
</span><span id="RetroPieInstaller-473"><a href="#RetroPieInstaller-473"><span class="linenos">473</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">check_dependencies</span><span class="p">():</span>
</span><span id="RetroPieInstaller-474"><a href="#RetroPieInstaller-474"><span class="linenos">474</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;系统依赖检查失败&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-475"><a href="#RetroPieInstaller-475"><span class="linenos">475</span></a>            <span class="k">return</span>
</span><span id="RetroPieInstaller-476"><a href="#RetroPieInstaller-476"><span class="linenos">476</span></a>        
</span><span id="RetroPieInstaller-477"><a href="#RetroPieInstaller-477"><span class="linenos">477</span></a>        <span class="c1"># 获取下载链接</span>
</span><span id="RetroPieInstaller-478"><a href="#RetroPieInstaller-478"><span class="linenos">478</span></a>        <span class="n">download_url</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_retropie_download_url</span><span class="p">()</span>
</span><span id="RetroPieInstaller-479"><a href="#RetroPieInstaller-479"><span class="linenos">479</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">download_url</span><span class="p">:</span>
</span><span id="RetroPieInstaller-480"><a href="#RetroPieInstaller-480"><span class="linenos">480</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;无法获取下载链接&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-481"><a href="#RetroPieInstaller-481"><span class="linenos">481</span></a>            <span class="k">return</span>
</span><span id="RetroPieInstaller-482"><a href="#RetroPieInstaller-482"><span class="linenos">482</span></a>        
</span><span id="RetroPieInstaller-483"><a href="#RetroPieInstaller-483"><span class="linenos">483</span></a>        <span class="c1"># 下载镜像</span>
</span><span id="RetroPieInstaller-484"><a href="#RetroPieInstaller-484"><span class="linenos">484</span></a>        <span class="n">archive_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_image</span><span class="p">(</span><span class="n">download_url</span><span class="p">)</span>
</span><span id="RetroPieInstaller-485"><a href="#RetroPieInstaller-485"><span class="linenos">485</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">archive_path</span><span class="p">:</span>
</span><span id="RetroPieInstaller-486"><a href="#RetroPieInstaller-486"><span class="linenos">486</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;镜像下载失败&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-487"><a href="#RetroPieInstaller-487"><span class="linenos">487</span></a>            <span class="k">return</span>
</span><span id="RetroPieInstaller-488"><a href="#RetroPieInstaller-488"><span class="linenos">488</span></a>        
</span><span id="RetroPieInstaller-489"><a href="#RetroPieInstaller-489"><span class="linenos">489</span></a>        <span class="c1"># 解压镜像</span>
</span><span id="RetroPieInstaller-490"><a href="#RetroPieInstaller-490"><span class="linenos">490</span></a>        <span class="n">image_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">extract_image</span><span class="p">(</span><span class="n">archive_path</span><span class="p">)</span>
</span><span id="RetroPieInstaller-491"><a href="#RetroPieInstaller-491"><span class="linenos">491</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">image_path</span><span class="p">:</span>
</span><span id="RetroPieInstaller-492"><a href="#RetroPieInstaller-492"><span class="linenos">492</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;镜像解压失败&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-493"><a href="#RetroPieInstaller-493"><span class="linenos">493</span></a>            <span class="k">return</span>
</span><span id="RetroPieInstaller-494"><a href="#RetroPieInstaller-494"><span class="linenos">494</span></a>        
</span><span id="RetroPieInstaller-495"><a href="#RetroPieInstaller-495"><span class="linenos">495</span></a>        <span class="c1"># 列出可用磁盘</span>
</span><span id="RetroPieInstaller-496"><a href="#RetroPieInstaller-496"><span class="linenos">496</span></a>        <span class="n">disks</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">list_available_disks</span><span class="p">()</span>
</span><span id="RetroPieInstaller-497"><a href="#RetroPieInstaller-497"><span class="linenos">497</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">disks</span><span class="p">:</span>
</span><span id="RetroPieInstaller-498"><a href="#RetroPieInstaller-498"><span class="linenos">498</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;未找到可用磁盘&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-499"><a href="#RetroPieInstaller-499"><span class="linenos">499</span></a>            <span class="k">return</span>
</span><span id="RetroPieInstaller-500"><a href="#RetroPieInstaller-500"><span class="linenos">500</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;可用磁盘:&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-501"><a href="#RetroPieInstaller-501"><span class="linenos">501</span></a>        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="p">(</span><span class="n">device</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="n">name</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">disks</span><span class="p">):</span>
</span><span id="RetroPieInstaller-502"><a href="#RetroPieInstaller-502"><span class="linenos">502</span></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">. </span><span class="si">{</span><span class="n">device</span><span class="si">}</span><span class="s2"> (</span><span class="si">{</span><span class="n">size</span><span class="si">}</span><span class="s2">) - </span><span class="si">{</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-503"><a href="#RetroPieInstaller-503"><span class="linenos">503</span></a>        
</span><span id="RetroPieInstaller-504"><a href="#RetroPieInstaller-504"><span class="linenos">504</span></a>        <span class="c1"># 选择目标磁盘</span>
</span><span id="RetroPieInstaller-505"><a href="#RetroPieInstaller-505"><span class="linenos">505</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller-506"><a href="#RetroPieInstaller-506"><span class="linenos">506</span></a>            <span class="n">choice</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="nb">input</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;请选择目标磁盘 (1-</span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">disks</span><span class="p">)</span><span class="si">}</span><span class="s2">): &quot;</span><span class="p">))</span> <span class="o">-</span> <span class="mi">1</span>
</span><span id="RetroPieInstaller-507"><a href="#RetroPieInstaller-507"><span class="linenos">507</span></a>            <span class="k">if</span> <span class="mi">0</span> <span class="o">&lt;=</span> <span class="n">choice</span> <span class="o">&lt;</span> <span class="nb">len</span><span class="p">(</span><span class="n">disks</span><span class="p">):</span>
</span><span id="RetroPieInstaller-508"><a href="#RetroPieInstaller-508"><span class="linenos">508</span></a>                <span class="n">target_disk</span> <span class="o">=</span> <span class="n">disks</span><span class="p">[</span><span class="n">choice</span><span class="p">][</span><span class="mi">0</span><span class="p">]</span>
</span><span id="RetroPieInstaller-509"><a href="#RetroPieInstaller-509"><span class="linenos">509</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="RetroPieInstaller-510"><a href="#RetroPieInstaller-510"><span class="linenos">510</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;无效的选择&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-511"><a href="#RetroPieInstaller-511"><span class="linenos">511</span></a>                <span class="k">return</span>
</span><span id="RetroPieInstaller-512"><a href="#RetroPieInstaller-512"><span class="linenos">512</span></a>        <span class="k">except</span> <span class="ne">ValueError</span><span class="p">:</span>
</span><span id="RetroPieInstaller-513"><a href="#RetroPieInstaller-513"><span class="linenos">513</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;请输入有效的数字&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-514"><a href="#RetroPieInstaller-514"><span class="linenos">514</span></a>            <span class="k">return</span>
</span><span id="RetroPieInstaller-515"><a href="#RetroPieInstaller-515"><span class="linenos">515</span></a>        
</span><span id="RetroPieInstaller-516"><a href="#RetroPieInstaller-516"><span class="linenos">516</span></a>        <span class="c1"># 烧录镜像</span>
</span><span id="RetroPieInstaller-517"><a href="#RetroPieInstaller-517"><span class="linenos">517</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">burn_image</span><span class="p">(</span><span class="n">image_path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">):</span>
</span><span id="RetroPieInstaller-518"><a href="#RetroPieInstaller-518"><span class="linenos">518</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;RetroPie 安装完成！&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller-519"><a href="#RetroPieInstaller-519"><span class="linenos">519</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="RetroPieInstaller-520"><a href="#RetroPieInstaller-520"><span class="linenos">520</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;烧录失败&quot;</span><span class="p">)</span>
</span></pre></div>


            <div class="docstring"><p>RetroPie 镜像下载和烧录工具类</p>

<p>提供完整的RetroPie镜像自动化安装流程，包括：</p>

<ul>
<li>系统依赖检查</li>
<li>镜像下载和解压</li>
<li>磁盘设备管理</li>
<li>镜像烧录操作</li>
</ul>

<p>属性:
    system (str): 当前操作系统名称
    retropie_url (str): RetroPie官网URL
    download_dir (Path): 下载目录路径</p>
</div>


                            <div id="RetroPieInstaller.__init__" class="classattr">
                                        <input id="RetroPieInstaller.__init__-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="name">RetroPieInstaller</span><span class="signature pdoc-code condensed">()</span>

                <label class="view-source-button" for="RetroPieInstaller.__init__-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#RetroPieInstaller.__init__"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="RetroPieInstaller.__init__-67"><a href="#RetroPieInstaller.__init__-67"><span class="linenos">67</span></a>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
</span><span id="RetroPieInstaller.__init__-68"><a href="#RetroPieInstaller.__init__-68"><span class="linenos">68</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller.__init__-69"><a href="#RetroPieInstaller.__init__-69"><span class="linenos">69</span></a><span class="sd">        初始化RetroPie安装器</span>
</span><span id="RetroPieInstaller.__init__-70"><a href="#RetroPieInstaller.__init__-70"><span class="linenos">70</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller.__init__-71"><a href="#RetroPieInstaller.__init__-71"><span class="linenos">71</span></a><span class="sd">        设置基本配置和创建必要的目录结构。</span>
</span><span id="RetroPieInstaller.__init__-72"><a href="#RetroPieInstaller.__init__-72"><span class="linenos">72</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller.__init__-73"><a href="#RetroPieInstaller.__init__-73"><span class="linenos">73</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">=</span> <span class="n">platform</span><span class="o">.</span><span class="n">system</span><span class="p">()</span>
</span><span id="RetroPieInstaller.__init__-74"><a href="#RetroPieInstaller.__init__-74"><span class="linenos">74</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">retropie_url</span> <span class="o">=</span> <span class="s2">&quot;https://retropie.org.uk/download/&quot;</span>
</span><span id="RetroPieInstaller.__init__-75"><a href="#RetroPieInstaller.__init__-75"><span class="linenos">75</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s2">&quot;downloads&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.__init__-76"><a href="#RetroPieInstaller.__init__-76"><span class="linenos">76</span></a>        <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span></pre></div>


            <div class="docstring"><p>初始化RetroPie安装器</p>

<p>设置基本配置和创建必要的目录结构。</p>
</div>


                            </div>
                            <div id="RetroPieInstaller.system" class="classattr">
                                <div class="attr variable">
            <span class="name">system</span>

        
    </div>
    <a class="headerlink" href="#RetroPieInstaller.system"></a>
    
    

                            </div>
                            <div id="RetroPieInstaller.retropie_url" class="classattr">
                                <div class="attr variable">
            <span class="name">retropie_url</span>

        
    </div>
    <a class="headerlink" href="#RetroPieInstaller.retropie_url"></a>
    
    

                            </div>
                            <div id="RetroPieInstaller.download_dir" class="classattr">
                                <div class="attr variable">
            <span class="name">download_dir</span>

        
    </div>
    <a class="headerlink" href="#RetroPieInstaller.download_dir"></a>
    
    

                            </div>
                            <div id="RetroPieInstaller.check_dependencies" class="classattr">
                                        <input id="RetroPieInstaller.check_dependencies-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">check_dependencies</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">) -> <span class="nb">bool</span>:</span></span>

                <label class="view-source-button" for="RetroPieInstaller.check_dependencies-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#RetroPieInstaller.check_dependencies"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="RetroPieInstaller.check_dependencies-78"><a href="#RetroPieInstaller.check_dependencies-78"><span class="linenos">78</span></a>    <span class="k">def</span> <span class="nf">check_dependencies</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="RetroPieInstaller.check_dependencies-79"><a href="#RetroPieInstaller.check_dependencies-79"><span class="linenos">79</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller.check_dependencies-80"><a href="#RetroPieInstaller.check_dependencies-80"><span class="linenos">80</span></a><span class="sd">        检查系统依赖是否满足</span>
</span><span id="RetroPieInstaller.check_dependencies-81"><a href="#RetroPieInstaller.check_dependencies-81"><span class="linenos">81</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller.check_dependencies-82"><a href="#RetroPieInstaller.check_dependencies-82"><span class="linenos">82</span></a><span class="sd">        根据操作系统类型检查必要的工具和权限。</span>
</span><span id="RetroPieInstaller.check_dependencies-83"><a href="#RetroPieInstaller.check_dependencies-83"><span class="linenos">83</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller.check_dependencies-84"><a href="#RetroPieInstaller.check_dependencies-84"><span class="linenos">84</span></a><span class="sd">        Returns:</span>
</span><span id="RetroPieInstaller.check_dependencies-85"><a href="#RetroPieInstaller.check_dependencies-85"><span class="linenos">85</span></a><span class="sd">            bool: 依赖检查是否通过</span>
</span><span id="RetroPieInstaller.check_dependencies-86"><a href="#RetroPieInstaller.check_dependencies-86"><span class="linenos">86</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller.check_dependencies-87"><a href="#RetroPieInstaller.check_dependencies-87"><span class="linenos">87</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;检查系统依赖...&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.check_dependencies-88"><a href="#RetroPieInstaller.check_dependencies-88"><span class="linenos">88</span></a>        
</span><span id="RetroPieInstaller.check_dependencies-89"><a href="#RetroPieInstaller.check_dependencies-89"><span class="linenos">89</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Windows&quot;</span><span class="p">:</span>
</span><span id="RetroPieInstaller.check_dependencies-90"><a href="#RetroPieInstaller.check_dependencies-90"><span class="linenos">90</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_windows_dependencies</span><span class="p">()</span>
</span><span id="RetroPieInstaller.check_dependencies-91"><a href="#RetroPieInstaller.check_dependencies-91"><span class="linenos">91</span></a>        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;Linux&quot;</span><span class="p">,</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">]:</span>  <span class="c1"># Darwin = macOS</span>
</span><span id="RetroPieInstaller.check_dependencies-92"><a href="#RetroPieInstaller.check_dependencies-92"><span class="linenos">92</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_unix_dependencies</span><span class="p">()</span>
</span><span id="RetroPieInstaller.check_dependencies-93"><a href="#RetroPieInstaller.check_dependencies-93"><span class="linenos">93</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="RetroPieInstaller.check_dependencies-94"><a href="#RetroPieInstaller.check_dependencies-94"><span class="linenos">94</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;不支持的操作系统: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">system</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.check_dependencies-95"><a href="#RetroPieInstaller.check_dependencies-95"><span class="linenos">95</span></a>            <span class="k">return</span> <span class="kc">False</span>
</span></pre></div>


            <div class="docstring"><p>检查系统依赖是否满足</p>

<p>根据操作系统类型检查必要的工具和权限。</p>

<p>Returns:
    bool: 依赖检查是否通过</p>
</div>


                            </div>
                            <div id="RetroPieInstaller.get_retropie_download_url" class="classattr">
                                        <input id="RetroPieInstaller.get_retropie_download_url-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">get_retropie_download_url</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">) -> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>:</span></span>

                <label class="view-source-button" for="RetroPieInstaller.get_retropie_download_url-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#RetroPieInstaller.get_retropie_download_url"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="RetroPieInstaller.get_retropie_download_url-176"><a href="#RetroPieInstaller.get_retropie_download_url-176"><span class="linenos">176</span></a>    <span class="k">def</span> <span class="nf">get_retropie_download_url</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]:</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-177"><a href="#RetroPieInstaller.get_retropie_download_url-177"><span class="linenos">177</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-178"><a href="#RetroPieInstaller.get_retropie_download_url-178"><span class="linenos">178</span></a><span class="sd">        获取RetroPie镜像下载链接</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-179"><a href="#RetroPieInstaller.get_retropie_download_url-179"><span class="linenos">179</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller.get_retropie_download_url-180"><a href="#RetroPieInstaller.get_retropie_download_url-180"><span class="linenos">180</span></a><span class="sd">        From RetroPie官网解析页面内容，查找适合树莓派4B的镜像下载链接。</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-181"><a href="#RetroPieInstaller.get_retropie_download_url-181"><span class="linenos">181</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller.get_retropie_download_url-182"><a href="#RetroPieInstaller.get_retropie_download_url-182"><span class="linenos">182</span></a><span class="sd">        Returns:</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-183"><a href="#RetroPieInstaller.get_retropie_download_url-183"><span class="linenos">183</span></a><span class="sd">            Optional[str]: 下载链接URL，如果未找到则返回None</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-184"><a href="#RetroPieInstaller.get_retropie_download_url-184"><span class="linenos">184</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-185"><a href="#RetroPieInstaller.get_retropie_download_url-185"><span class="linenos">185</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;获取RetroPie下载链接...&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-186"><a href="#RetroPieInstaller.get_retropie_download_url-186"><span class="linenos">186</span></a>        
</span><span id="RetroPieInstaller.get_retropie_download_url-187"><a href="#RetroPieInstaller.get_retropie_download_url-187"><span class="linenos">187</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-188"><a href="#RetroPieInstaller.get_retropie_download_url-188"><span class="linenos">188</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">retropie_url</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-189"><a href="#RetroPieInstaller.get_retropie_download_url-189"><span class="linenos">189</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-190"><a href="#RetroPieInstaller.get_retropie_download_url-190"><span class="linenos">190</span></a>            
</span><span id="RetroPieInstaller.get_retropie_download_url-191"><a href="#RetroPieInstaller.get_retropie_download_url-191"><span class="linenos">191</span></a>            <span class="c1"># 解析页面内容，查找树莓派4B的镜像链接</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-192"><a href="#RetroPieInstaller.get_retropie_download_url-192"><span class="linenos">192</span></a>            <span class="n">content</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">text</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-193"><a href="#RetroPieInstaller.get_retropie_download_url-193"><span class="linenos">193</span></a>            
</span><span id="RetroPieInstaller.get_retropie_download_url-194"><a href="#RetroPieInstaller.get_retropie_download_url-194"><span class="linenos">194</span></a>            <span class="c1"># 常见的RetroPie镜像文件名模式</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-195"><a href="#RetroPieInstaller.get_retropie_download_url-195"><span class="linenos">195</span></a>            <span class="n">patterns</span> <span class="o">=</span> <span class="p">[</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-196"><a href="#RetroPieInstaller.get_retropie_download_url-196"><span class="linenos">196</span></a>                <span class="s2">&quot;retropie-buster-4.8-rpi4_400.img.gz&quot;</span><span class="p">,</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-197"><a href="#RetroPieInstaller.get_retropie_download_url-197"><span class="linenos">197</span></a>                <span class="s2">&quot;retropie-buster-4.8-rpi4.img.gz&quot;</span><span class="p">,</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-198"><a href="#RetroPieInstaller.get_retropie_download_url-198"><span class="linenos">198</span></a>                <span class="s2">&quot;retropie-4.8-rpi4.img.gz&quot;</span><span class="p">,</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-199"><a href="#RetroPieInstaller.get_retropie_download_url-199"><span class="linenos">199</span></a>                <span class="s2">&quot;retropie-buster-rpi4.img.gz&quot;</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-200"><a href="#RetroPieInstaller.get_retropie_download_url-200"><span class="linenos">200</span></a>            <span class="p">]</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-201"><a href="#RetroPieInstaller.get_retropie_download_url-201"><span class="linenos">201</span></a>            
</span><span id="RetroPieInstaller.get_retropie_download_url-202"><a href="#RetroPieInstaller.get_retropie_download_url-202"><span class="linenos">202</span></a>            <span class="k">for</span> <span class="n">pattern</span> <span class="ow">in</span> <span class="n">patterns</span><span class="p">:</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-203"><a href="#RetroPieInstaller.get_retropie_download_url-203"><span class="linenos">203</span></a>                <span class="k">if</span> <span class="n">pattern</span> <span class="ow">in</span> <span class="n">content</span><span class="p">:</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-204"><a href="#RetroPieInstaller.get_retropie_download_url-204"><span class="linenos">204</span></a>                    <span class="c1"># 构建完整下载URL</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-205"><a href="#RetroPieInstaller.get_retropie_download_url-205"><span class="linenos">205</span></a>                    <span class="n">download_url</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;https://github.com/RetroPie/RetroPie-Setup/releases/download/4.8/</span><span class="si">{</span><span class="n">pattern</span><span class="si">}</span><span class="s2">&quot;</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-206"><a href="#RetroPieInstaller.get_retropie_download_url-206"><span class="linenos">206</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;找到下载链接: </span><span class="si">{</span><span class="n">download_url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-207"><a href="#RetroPieInstaller.get_retropie_download_url-207"><span class="linenos">207</span></a>                    <span class="k">return</span> <span class="n">download_url</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-208"><a href="#RetroPieInstaller.get_retropie_download_url-208"><span class="linenos">208</span></a>            
</span><span id="RetroPieInstaller.get_retropie_download_url-209"><a href="#RetroPieInstaller.get_retropie_download_url-209"><span class="linenos">209</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;未找到合适的镜像链接，请手动下载&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-210"><a href="#RetroPieInstaller.get_retropie_download_url-210"><span class="linenos">210</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-211"><a href="#RetroPieInstaller.get_retropie_download_url-211"><span class="linenos">211</span></a>            
</span><span id="RetroPieInstaller.get_retropie_download_url-212"><a href="#RetroPieInstaller.get_retropie_download_url-212"><span class="linenos">212</span></a>        <span class="k">except</span> <span class="n">requests</span><span class="o">.</span><span class="n">RequestException</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-213"><a href="#RetroPieInstaller.get_retropie_download_url-213"><span class="linenos">213</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;获取下载链接失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.get_retropie_download_url-214"><a href="#RetroPieInstaller.get_retropie_download_url-214"><span class="linenos">214</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span></pre></div>


            <div class="docstring"><p>获取RetroPie镜像下载链接</p>

<p>From RetroPie官网解析页面内容，查找适合树莓派4B的镜像下载链接。</p>

<p>Returns:
    Optional[str]: 下载链接URL，如果未找到则返回None</p>
</div>


                            </div>
                            <div id="RetroPieInstaller.download_image" class="classattr">
                                        <input id="RetroPieInstaller.download_image-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">download_image</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">url</span><span class="p">:</span> <span class="nb">str</span></span><span class="return-annotation">) -> <span class="n">Optional</span><span class="p">[</span><span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">]</span>:</span></span>

                <label class="view-source-button" for="RetroPieInstaller.download_image-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#RetroPieInstaller.download_image"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="RetroPieInstaller.download_image-216"><a href="#RetroPieInstaller.download_image-216"><span class="linenos">216</span></a>    <span class="k">def</span> <span class="nf">download_image</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">url</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Path</span><span class="p">]:</span>
</span><span id="RetroPieInstaller.download_image-217"><a href="#RetroPieInstaller.download_image-217"><span class="linenos">217</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller.download_image-218"><a href="#RetroPieInstaller.download_image-218"><span class="linenos">218</span></a><span class="sd">        下载RetroPie镜像</span>
</span><span id="RetroPieInstaller.download_image-219"><a href="#RetroPieInstaller.download_image-219"><span class="linenos">219</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller.download_image-220"><a href="#RetroPieInstaller.download_image-220"><span class="linenos">220</span></a><span class="sd">        Support断点续传的镜像下载，显示下载进度。</span>
</span><span id="RetroPieInstaller.download_image-221"><a href="#RetroPieInstaller.download_image-221"><span class="linenos">221</span></a><span class="sd">        </span>
</span><span id="RetroPieInstaller.download_image-222"><a href="#RetroPieInstaller.download_image-222"><span class="linenos">222</span></a><span class="sd">        Args:</span>
</span><span id="RetroPieInstaller.download_image-223"><a href="#RetroPieInstaller.download_image-223"><span class="linenos">223</span></a><span class="sd">            url (str): 镜像下载链接</span>
</span><span id="RetroPieInstaller.download_image-224"><a href="#RetroPieInstaller.download_image-224"><span class="linenos">224</span></a><span class="sd">            </span>
</span><span id="RetroPieInstaller.download_image-225"><a href="#RetroPieInstaller.download_image-225"><span class="linenos">225</span></a><span class="sd">        Returns:</span>
</span><span id="RetroPieInstaller.download_image-226"><a href="#RetroPieInstaller.download_image-226"><span class="linenos">226</span></a><span class="sd">            Optional[Path]: 下载文件的路径，如果下载失败则返回None</span>
</span><span id="RetroPieInstaller.download_image-227"><a href="#RetroPieInstaller.download_image-227"><span class="linenos">227</span></a><span class="sd">        &quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller.download_image-228"><a href="#RetroPieInstaller.download_image-228"><span class="linenos">228</span></a>        <span class="n">filename</span> <span class="o">=</span> <span class="n">url</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">)[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
</span><span id="RetroPieInstaller.download_image-229"><a href="#RetroPieInstaller.download_image-229"><span class="linenos">229</span></a>        <span class="n">file_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="n">filename</span>
</span><span id="RetroPieInstaller.download_image-230"><a href="#RetroPieInstaller.download_image-230"><span class="linenos">230</span></a>        
</span><span id="RetroPieInstaller.download_image-231"><a href="#RetroPieInstaller.download_image-231"><span class="linenos">231</span></a>        <span class="k">if</span> <span class="n">file_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="RetroPieInstaller.download_image-232"><a href="#RetroPieInstaller.download_image-232"><span class="linenos">232</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像文件已存在: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.download_image-233"><a href="#RetroPieInstaller.download_image-233"><span class="linenos">233</span></a>            <span class="k">return</span> <span class="n">file_path</span>
</span><span id="RetroPieInstaller.download_image-234"><a href="#RetroPieInstaller.download_image-234"><span class="linenos">234</span></a>        
</span><span id="RetroPieInstaller.download_image-235"><a href="#RetroPieInstaller.download_image-235"><span class="linenos">235</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;开始下载镜像: </span><span class="si">{</span><span class="n">url</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.download_image-236"><a href="#RetroPieInstaller.download_image-236"><span class="linenos">236</span></a>        
</span><span id="RetroPieInstaller.download_image-237"><a href="#RetroPieInstaller.download_image-237"><span class="linenos">237</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller.download_image-238"><a href="#RetroPieInstaller.download_image-238"><span class="linenos">238</span></a>            <span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">stream</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
</span><span id="RetroPieInstaller.download_image-239"><a href="#RetroPieInstaller.download_image-239"><span class="linenos">239</span></a>            <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
</span><span id="RetroPieInstaller.download_image-240"><a href="#RetroPieInstaller.download_image-240"><span class="linenos">240</span></a>            
</span><span id="RetroPieInstaller.download_image-241"><a href="#RetroPieInstaller.download_image-241"><span class="linenos">241</span></a>            <span class="n">total_size</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">headers</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;content-length&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
</span><span id="RetroPieInstaller.download_image-242"><a href="#RetroPieInstaller.download_image-242"><span class="linenos">242</span></a>            <span class="n">downloaded</span> <span class="o">=</span> <span class="mi">0</span>
</span><span id="RetroPieInstaller.download_image-243"><a href="#RetroPieInstaller.download_image-243"><span class="linenos">243</span></a>            
</span><span id="RetroPieInstaller.download_image-244"><a href="#RetroPieInstaller.download_image-244"><span class="linenos">244</span></a>            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">file_path</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
</span><span id="RetroPieInstaller.download_image-245"><a href="#RetroPieInstaller.download_image-245"><span class="linenos">245</span></a>                <span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="n">response</span><span class="o">.</span><span class="n">iter_content</span><span class="p">(</span><span class="n">chunk_size</span><span class="o">=</span><span class="mi">8192</span><span class="p">):</span>
</span><span id="RetroPieInstaller.download_image-246"><a href="#RetroPieInstaller.download_image-246"><span class="linenos">246</span></a>                    <span class="k">if</span> <span class="n">chunk</span><span class="p">:</span>
</span><span id="RetroPieInstaller.download_image-247"><a href="#RetroPieInstaller.download_image-247"><span class="linenos">247</span></a>                        <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</span><span id="RetroPieInstaller.download_image-248"><a href="#RetroPieInstaller.download_image-248"><span class="linenos">248</span></a>                        <span class="n">downloaded</span> <span class="o">+=</span> <span class="nb">len</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</span><span id="RetroPieInstaller.download_image-249"><a href="#RetroPieInstaller.download_image-249"><span class="linenos">249</span></a>                        
</span><span id="RetroPieInstaller.download_image-250"><a href="#RetroPieInstaller.download_image-250"><span class="linenos">250</span></a>                        <span class="k">if</span> <span class="n">total_size</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
</span><span id="RetroPieInstaller.download_image-251"><a href="#RetroPieInstaller.download_image-251"><span class="linenos">251</span></a>                            <span class="n">progress</span> <span class="o">=</span> <span class="p">(</span><span class="n">downloaded</span> <span class="o">/</span> <span class="n">total_size</span><span class="p">)</span> <span class="o">*</span> <span class="mi">100</span>
</span><span id="RetroPieInstaller.download_image-252"><a href="#RetroPieInstaller.download_image-252"><span class="linenos">252</span></a>                            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="se">\r</span><span class="s2">下载进度: </span><span class="si">{</span><span class="n">progress</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">%&quot;</span><span class="p">,</span> <span class="n">end</span><span class="o">=</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">flush</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</span><span id="RetroPieInstaller.download_image-253"><a href="#RetroPieInstaller.download_image-253"><span class="linenos">253</span></a>            
</span><span id="RetroPieInstaller.download_image-254"><a href="#RetroPieInstaller.download_image-254"><span class="linenos">254</span></a>            <span class="nb">print</span><span class="p">()</span>  <span class="c1"># 换行</span>
</span><span id="RetroPieInstaller.download_image-255"><a href="#RetroPieInstaller.download_image-255"><span class="linenos">255</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载完成: </span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.download_image-256"><a href="#RetroPieInstaller.download_image-256"><span class="linenos">256</span></a>            <span class="k">return</span> <span class="n">file_path</span>
</span><span id="RetroPieInstaller.download_image-257"><a href="#RetroPieInstaller.download_image-257"><span class="linenos">257</span></a>            
</span><span id="RetroPieInstaller.download_image-258"><a href="#RetroPieInstaller.download_image-258"><span class="linenos">258</span></a>        <span class="k">except</span> <span class="n">requests</span><span class="o">.</span><span class="n">RequestException</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="RetroPieInstaller.download_image-259"><a href="#RetroPieInstaller.download_image-259"><span class="linenos">259</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;下载失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.download_image-260"><a href="#RetroPieInstaller.download_image-260"><span class="linenos">260</span></a>            <span class="k">if</span> <span class="n">file_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="RetroPieInstaller.download_image-261"><a href="#RetroPieInstaller.download_image-261"><span class="linenos">261</span></a>                <span class="n">file_path</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
</span><span id="RetroPieInstaller.download_image-262"><a href="#RetroPieInstaller.download_image-262"><span class="linenos">262</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span></pre></div>


            <div class="docstring"><p>下载RetroPie镜像</p>

<p>Support断点续传的镜像下载，显示下载进度。</p>

<p>Args:
    url (str): 镜像下载链接</p>

<p>Returns:
    Optional[Path]: 下载文件的路径，如果下载失败则返回None</p>
</div>


                            </div>
                            <div id="RetroPieInstaller.extract_image" class="classattr">
                                        <input id="RetroPieInstaller.extract_image-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">extract_image</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">archive_path</span><span class="p">:</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span></span><span class="return-annotation">) -> <span class="n">Optional</span><span class="p">[</span><span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span><span class="p">]</span>:</span></span>

                <label class="view-source-button" for="RetroPieInstaller.extract_image-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#RetroPieInstaller.extract_image"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="RetroPieInstaller.extract_image-264"><a href="#RetroPieInstaller.extract_image-264"><span class="linenos">264</span></a>    <span class="k">def</span> <span class="nf">extract_image</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">archive_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Path</span><span class="p">]:</span>
</span><span id="RetroPieInstaller.extract_image-265"><a href="#RetroPieInstaller.extract_image-265"><span class="linenos">265</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;解压镜像文件&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller.extract_image-266"><a href="#RetroPieInstaller.extract_image-266"><span class="linenos">266</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压镜像文件: </span><span class="si">{</span><span class="n">archive_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.extract_image-267"><a href="#RetroPieInstaller.extract_image-267"><span class="linenos">267</span></a>        
</span><span id="RetroPieInstaller.extract_image-268"><a href="#RetroPieInstaller.extract_image-268"><span class="linenos">268</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller.extract_image-269"><a href="#RetroPieInstaller.extract_image-269"><span class="linenos">269</span></a>            <span class="k">if</span> <span class="n">archive_path</span><span class="o">.</span><span class="n">suffix</span> <span class="o">==</span> <span class="s1">&#39;.gz&#39;</span><span class="p">:</span>
</span><span id="RetroPieInstaller.extract_image-270"><a href="#RetroPieInstaller.extract_image-270"><span class="linenos">270</span></a>                <span class="c1"># 处理 .img.gz 文件</span>
</span><span id="RetroPieInstaller.extract_image-271"><a href="#RetroPieInstaller.extract_image-271"><span class="linenos">271</span></a>                <span class="n">img_path</span> <span class="o">=</span> <span class="n">archive_path</span><span class="o">.</span><span class="n">with_suffix</span><span class="p">(</span><span class="s1">&#39;&#39;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.extract_image-272"><a href="#RetroPieInstaller.extract_image-272"><span class="linenos">272</span></a>                <span class="k">if</span> <span class="n">img_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="RetroPieInstaller.extract_image-273"><a href="#RetroPieInstaller.extract_image-273"><span class="linenos">273</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像文件已存在: </span><span class="si">{</span><span class="n">img_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.extract_image-274"><a href="#RetroPieInstaller.extract_image-274"><span class="linenos">274</span></a>                    <span class="k">return</span> <span class="n">img_path</span>
</span><span id="RetroPieInstaller.extract_image-275"><a href="#RetroPieInstaller.extract_image-275"><span class="linenos">275</span></a>                
</span><span id="RetroPieInstaller.extract_image-276"><a href="#RetroPieInstaller.extract_image-276"><span class="linenos">276</span></a>                <span class="kn">import</span> <span class="nn">gzip</span>
</span><span id="RetroPieInstaller.extract_image-277"><a href="#RetroPieInstaller.extract_image-277"><span class="linenos">277</span></a>                <span class="k">with</span> <span class="n">gzip</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="n">archive_path</span><span class="p">,</span> <span class="s1">&#39;rb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f_in</span><span class="p">:</span>
</span><span id="RetroPieInstaller.extract_image-278"><a href="#RetroPieInstaller.extract_image-278"><span class="linenos">278</span></a>                    <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">img_path</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f_out</span><span class="p">:</span>
</span><span id="RetroPieInstaller.extract_image-279"><a href="#RetroPieInstaller.extract_image-279"><span class="linenos">279</span></a>                        <span class="n">f_out</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">f_in</span><span class="o">.</span><span class="n">read</span><span class="p">())</span>
</span><span id="RetroPieInstaller.extract_image-280"><a href="#RetroPieInstaller.extract_image-280"><span class="linenos">280</span></a>                
</span><span id="RetroPieInstaller.extract_image-281"><a href="#RetroPieInstaller.extract_image-281"><span class="linenos">281</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压完成: </span><span class="si">{</span><span class="n">img_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.extract_image-282"><a href="#RetroPieInstaller.extract_image-282"><span class="linenos">282</span></a>                <span class="k">return</span> <span class="n">img_path</span>
</span><span id="RetroPieInstaller.extract_image-283"><a href="#RetroPieInstaller.extract_image-283"><span class="linenos">283</span></a>                
</span><span id="RetroPieInstaller.extract_image-284"><a href="#RetroPieInstaller.extract_image-284"><span class="linenos">284</span></a>            <span class="k">elif</span> <span class="n">archive_path</span><span class="o">.</span><span class="n">suffix</span> <span class="o">==</span> <span class="s1">&#39;.zip&#39;</span><span class="p">:</span>
</span><span id="RetroPieInstaller.extract_image-285"><a href="#RetroPieInstaller.extract_image-285"><span class="linenos">285</span></a>                <span class="c1"># 处理 .zip 文件</span>
</span><span id="RetroPieInstaller.extract_image-286"><a href="#RetroPieInstaller.extract_image-286"><span class="linenos">286</span></a>                <span class="k">with</span> <span class="n">zipfile</span><span class="o">.</span><span class="n">ZipFile</span><span class="p">(</span><span class="n">archive_path</span><span class="p">,</span> <span class="s1">&#39;r&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">zip_ref</span><span class="p">:</span>
</span><span id="RetroPieInstaller.extract_image-287"><a href="#RetroPieInstaller.extract_image-287"><span class="linenos">287</span></a>                    <span class="c1"># 查找 .img 文件</span>
</span><span id="RetroPieInstaller.extract_image-288"><a href="#RetroPieInstaller.extract_image-288"><span class="linenos">288</span></a>                    <span class="n">img_files</span> <span class="o">=</span> <span class="p">[</span><span class="n">f</span> <span class="k">for</span> <span class="n">f</span> <span class="ow">in</span> <span class="n">zip_ref</span><span class="o">.</span><span class="n">namelist</span><span class="p">()</span> <span class="k">if</span> <span class="n">f</span><span class="o">.</span><span class="n">endswith</span><span class="p">(</span><span class="s1">&#39;.img&#39;</span><span class="p">)]</span>
</span><span id="RetroPieInstaller.extract_image-289"><a href="#RetroPieInstaller.extract_image-289"><span class="linenos">289</span></a>                    <span class="k">if</span> <span class="ow">not</span> <span class="n">img_files</span><span class="p">:</span>
</span><span id="RetroPieInstaller.extract_image-290"><a href="#RetroPieInstaller.extract_image-290"><span class="linenos">290</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;ZIP文件中未找到.img文件&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.extract_image-291"><a href="#RetroPieInstaller.extract_image-291"><span class="linenos">291</span></a>                        <span class="k">return</span> <span class="kc">None</span>
</span><span id="RetroPieInstaller.extract_image-292"><a href="#RetroPieInstaller.extract_image-292"><span class="linenos">292</span></a>                    
</span><span id="RetroPieInstaller.extract_image-293"><a href="#RetroPieInstaller.extract_image-293"><span class="linenos">293</span></a>                    <span class="n">img_filename</span> <span class="o">=</span> <span class="n">img_files</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
</span><span id="RetroPieInstaller.extract_image-294"><a href="#RetroPieInstaller.extract_image-294"><span class="linenos">294</span></a>                    <span class="n">img_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_dir</span> <span class="o">/</span> <span class="n">Path</span><span class="p">(</span><span class="n">img_filename</span><span class="p">)</span><span class="o">.</span><span class="n">name</span>
</span><span id="RetroPieInstaller.extract_image-295"><a href="#RetroPieInstaller.extract_image-295"><span class="linenos">295</span></a>                    
</span><span id="RetroPieInstaller.extract_image-296"><a href="#RetroPieInstaller.extract_image-296"><span class="linenos">296</span></a>                    <span class="k">if</span> <span class="n">img_path</span><span class="o">.</span><span class="n">exists</span><span class="p">():</span>
</span><span id="RetroPieInstaller.extract_image-297"><a href="#RetroPieInstaller.extract_image-297"><span class="linenos">297</span></a>                        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像文件已存在: </span><span class="si">{</span><span class="n">img_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.extract_image-298"><a href="#RetroPieInstaller.extract_image-298"><span class="linenos">298</span></a>                        <span class="k">return</span> <span class="n">img_path</span>
</span><span id="RetroPieInstaller.extract_image-299"><a href="#RetroPieInstaller.extract_image-299"><span class="linenos">299</span></a>                    
</span><span id="RetroPieInstaller.extract_image-300"><a href="#RetroPieInstaller.extract_image-300"><span class="linenos">300</span></a>                    <span class="k">with</span> <span class="n">zip_ref</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="n">img_filename</span><span class="p">)</span> <span class="k">as</span> <span class="n">source</span><span class="p">,</span> <span class="nb">open</span><span class="p">(</span><span class="n">img_path</span><span class="p">,</span> <span class="s1">&#39;wb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">target</span><span class="p">:</span>
</span><span id="RetroPieInstaller.extract_image-301"><a href="#RetroPieInstaller.extract_image-301"><span class="linenos">301</span></a>                        <span class="n">target</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">source</span><span class="o">.</span><span class="n">read</span><span class="p">())</span>
</span><span id="RetroPieInstaller.extract_image-302"><a href="#RetroPieInstaller.extract_image-302"><span class="linenos">302</span></a>                    
</span><span id="RetroPieInstaller.extract_image-303"><a href="#RetroPieInstaller.extract_image-303"><span class="linenos">303</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压完成: </span><span class="si">{</span><span class="n">img_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.extract_image-304"><a href="#RetroPieInstaller.extract_image-304"><span class="linenos">304</span></a>                    <span class="k">return</span> <span class="n">img_path</span>
</span><span id="RetroPieInstaller.extract_image-305"><a href="#RetroPieInstaller.extract_image-305"><span class="linenos">305</span></a>                    
</span><span id="RetroPieInstaller.extract_image-306"><a href="#RetroPieInstaller.extract_image-306"><span class="linenos">306</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="RetroPieInstaller.extract_image-307"><a href="#RetroPieInstaller.extract_image-307"><span class="linenos">307</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;不支持的文件格式: </span><span class="si">{</span><span class="n">archive_path</span><span class="o">.</span><span class="n">suffix</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.extract_image-308"><a href="#RetroPieInstaller.extract_image-308"><span class="linenos">308</span></a>                <span class="k">return</span> <span class="kc">None</span>
</span><span id="RetroPieInstaller.extract_image-309"><a href="#RetroPieInstaller.extract_image-309"><span class="linenos">309</span></a>                
</span><span id="RetroPieInstaller.extract_image-310"><a href="#RetroPieInstaller.extract_image-310"><span class="linenos">310</span></a>        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><span id="RetroPieInstaller.extract_image-311"><a href="#RetroPieInstaller.extract_image-311"><span class="linenos">311</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解压失败: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.extract_image-312"><a href="#RetroPieInstaller.extract_image-312"><span class="linenos">312</span></a>            <span class="k">return</span> <span class="kc">None</span>
</span></pre></div>


            <div class="docstring"><p>解压镜像文件</p>
</div>


                            </div>
                            <div id="RetroPieInstaller.list_available_disks" class="classattr">
                                        <input id="RetroPieInstaller.list_available_disks-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">list_available_disks</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">) -> <span class="n">List</span><span class="p">[</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]</span>:</span></span>

                <label class="view-source-button" for="RetroPieInstaller.list_available_disks-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#RetroPieInstaller.list_available_disks"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="RetroPieInstaller.list_available_disks-314"><a href="#RetroPieInstaller.list_available_disks-314"><span class="linenos">314</span></a>    <span class="k">def</span> <span class="nf">list_available_disks</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]:</span>
</span><span id="RetroPieInstaller.list_available_disks-315"><a href="#RetroPieInstaller.list_available_disks-315"><span class="linenos">315</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;列出可用的磁盘&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller.list_available_disks-316"><a href="#RetroPieInstaller.list_available_disks-316"><span class="linenos">316</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;扫描可用磁盘...&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.list_available_disks-317"><a href="#RetroPieInstaller.list_available_disks-317"><span class="linenos">317</span></a>        <span class="n">disks</span> <span class="o">=</span> <span class="p">[]</span>
</span><span id="RetroPieInstaller.list_available_disks-318"><a href="#RetroPieInstaller.list_available_disks-318"><span class="linenos">318</span></a>        
</span><span id="RetroPieInstaller.list_available_disks-319"><a href="#RetroPieInstaller.list_available_disks-319"><span class="linenos">319</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Windows&quot;</span><span class="p">:</span>
</span><span id="RetroPieInstaller.list_available_disks-320"><a href="#RetroPieInstaller.list_available_disks-320"><span class="linenos">320</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_list_windows_disks</span><span class="p">()</span>
</span><span id="RetroPieInstaller.list_available_disks-321"><a href="#RetroPieInstaller.list_available_disks-321"><span class="linenos">321</span></a>        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;Linux&quot;</span><span class="p">,</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">]:</span>
</span><span id="RetroPieInstaller.list_available_disks-322"><a href="#RetroPieInstaller.list_available_disks-322"><span class="linenos">322</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_list_unix_disks</span><span class="p">()</span>
</span><span id="RetroPieInstaller.list_available_disks-323"><a href="#RetroPieInstaller.list_available_disks-323"><span class="linenos">323</span></a>        
</span><span id="RetroPieInstaller.list_available_disks-324"><a href="#RetroPieInstaller.list_available_disks-324"><span class="linenos">324</span></a>        <span class="k">return</span> <span class="n">disks</span>
</span></pre></div>


            <div class="docstring"><p>列出可用的磁盘</p>
</div>


                            </div>
                            <div id="RetroPieInstaller.burn_image" class="classattr">
                                        <input id="RetroPieInstaller.burn_image-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">burn_image</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span>, </span><span class="param"><span class="n">image_path</span><span class="p">:</span> <span class="n">pathlib</span><span class="o">.</span><span class="n">Path</span>, </span><span class="param"><span class="n">target_disk</span><span class="p">:</span> <span class="nb">str</span></span><span class="return-annotation">) -> <span class="nb">bool</span>:</span></span>

                <label class="view-source-button" for="RetroPieInstaller.burn_image-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#RetroPieInstaller.burn_image"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="RetroPieInstaller.burn_image-405"><a href="#RetroPieInstaller.burn_image-405"><span class="linenos">405</span></a>    <span class="k">def</span> <span class="nf">burn_image</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">image_path</span><span class="p">:</span> <span class="n">Path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
</span><span id="RetroPieInstaller.burn_image-406"><a href="#RetroPieInstaller.burn_image-406"><span class="linenos">406</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;烧录镜像到指定磁盘&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller.burn_image-407"><a href="#RetroPieInstaller.burn_image-407"><span class="linenos">407</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;开始烧录镜像到 </span><span class="si">{</span><span class="n">target_disk</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.burn_image-408"><a href="#RetroPieInstaller.burn_image-408"><span class="linenos">408</span></a>        
</span><span id="RetroPieInstaller.burn_image-409"><a href="#RetroPieInstaller.burn_image-409"><span class="linenos">409</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="o">==</span> <span class="s2">&quot;Windows&quot;</span><span class="p">:</span>
</span><span id="RetroPieInstaller.burn_image-410"><a href="#RetroPieInstaller.burn_image-410"><span class="linenos">410</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_burn_windows</span><span class="p">(</span><span class="n">image_path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">)</span>
</span><span id="RetroPieInstaller.burn_image-411"><a href="#RetroPieInstaller.burn_image-411"><span class="linenos">411</span></a>        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">system</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;Linux&quot;</span><span class="p">,</span> <span class="s2">&quot;Darwin&quot;</span><span class="p">]:</span>
</span><span id="RetroPieInstaller.burn_image-412"><a href="#RetroPieInstaller.burn_image-412"><span class="linenos">412</span></a>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_burn_unix</span><span class="p">(</span><span class="n">image_path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">)</span>
</span><span id="RetroPieInstaller.burn_image-413"><a href="#RetroPieInstaller.burn_image-413"><span class="linenos">413</span></a>        
</span><span id="RetroPieInstaller.burn_image-414"><a href="#RetroPieInstaller.burn_image-414"><span class="linenos">414</span></a>        <span class="k">return</span> <span class="kc">False</span>
</span></pre></div>


            <div class="docstring"><p>烧录镜像到指定磁盘</p>
</div>


                            </div>
                            <div id="RetroPieInstaller.run" class="classattr">
                                        <input id="RetroPieInstaller.run-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">run</span><span class="signature pdoc-code condensed">(<span class="param"><span class="bp">self</span></span><span class="return-annotation">):</span></span>

                <label class="view-source-button" for="RetroPieInstaller.run-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#RetroPieInstaller.run"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="RetroPieInstaller.run-467"><a href="#RetroPieInstaller.run-467"><span class="linenos">467</span></a>    <span class="k">def</span> <span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
</span><span id="RetroPieInstaller.run-468"><a href="#RetroPieInstaller.run-468"><span class="linenos">468</span></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;运行主程序&quot;&quot;&quot;</span>
</span><span id="RetroPieInstaller.run-469"><a href="#RetroPieInstaller.run-469"><span class="linenos">469</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;=== RetroPie 镜像下载和烧录工具 ===&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-470"><a href="#RetroPieInstaller.run-470"><span class="linenos">470</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;操作系统: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">system</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-471"><a href="#RetroPieInstaller.run-471"><span class="linenos">471</span></a>        
</span><span id="RetroPieInstaller.run-472"><a href="#RetroPieInstaller.run-472"><span class="linenos">472</span></a>        <span class="c1"># 检查依赖</span>
</span><span id="RetroPieInstaller.run-473"><a href="#RetroPieInstaller.run-473"><span class="linenos">473</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">check_dependencies</span><span class="p">():</span>
</span><span id="RetroPieInstaller.run-474"><a href="#RetroPieInstaller.run-474"><span class="linenos">474</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;系统依赖检查失败&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-475"><a href="#RetroPieInstaller.run-475"><span class="linenos">475</span></a>            <span class="k">return</span>
</span><span id="RetroPieInstaller.run-476"><a href="#RetroPieInstaller.run-476"><span class="linenos">476</span></a>        
</span><span id="RetroPieInstaller.run-477"><a href="#RetroPieInstaller.run-477"><span class="linenos">477</span></a>        <span class="c1"># 获取下载链接</span>
</span><span id="RetroPieInstaller.run-478"><a href="#RetroPieInstaller.run-478"><span class="linenos">478</span></a>        <span class="n">download_url</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_retropie_download_url</span><span class="p">()</span>
</span><span id="RetroPieInstaller.run-479"><a href="#RetroPieInstaller.run-479"><span class="linenos">479</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">download_url</span><span class="p">:</span>
</span><span id="RetroPieInstaller.run-480"><a href="#RetroPieInstaller.run-480"><span class="linenos">480</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;无法获取下载链接&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-481"><a href="#RetroPieInstaller.run-481"><span class="linenos">481</span></a>            <span class="k">return</span>
</span><span id="RetroPieInstaller.run-482"><a href="#RetroPieInstaller.run-482"><span class="linenos">482</span></a>        
</span><span id="RetroPieInstaller.run-483"><a href="#RetroPieInstaller.run-483"><span class="linenos">483</span></a>        <span class="c1"># 下载镜像</span>
</span><span id="RetroPieInstaller.run-484"><a href="#RetroPieInstaller.run-484"><span class="linenos">484</span></a>        <span class="n">archive_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">download_image</span><span class="p">(</span><span class="n">download_url</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-485"><a href="#RetroPieInstaller.run-485"><span class="linenos">485</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">archive_path</span><span class="p">:</span>
</span><span id="RetroPieInstaller.run-486"><a href="#RetroPieInstaller.run-486"><span class="linenos">486</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;镜像下载失败&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-487"><a href="#RetroPieInstaller.run-487"><span class="linenos">487</span></a>            <span class="k">return</span>
</span><span id="RetroPieInstaller.run-488"><a href="#RetroPieInstaller.run-488"><span class="linenos">488</span></a>        
</span><span id="RetroPieInstaller.run-489"><a href="#RetroPieInstaller.run-489"><span class="linenos">489</span></a>        <span class="c1"># 解压镜像</span>
</span><span id="RetroPieInstaller.run-490"><a href="#RetroPieInstaller.run-490"><span class="linenos">490</span></a>        <span class="n">image_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">extract_image</span><span class="p">(</span><span class="n">archive_path</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-491"><a href="#RetroPieInstaller.run-491"><span class="linenos">491</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">image_path</span><span class="p">:</span>
</span><span id="RetroPieInstaller.run-492"><a href="#RetroPieInstaller.run-492"><span class="linenos">492</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;镜像解压失败&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-493"><a href="#RetroPieInstaller.run-493"><span class="linenos">493</span></a>            <span class="k">return</span>
</span><span id="RetroPieInstaller.run-494"><a href="#RetroPieInstaller.run-494"><span class="linenos">494</span></a>        
</span><span id="RetroPieInstaller.run-495"><a href="#RetroPieInstaller.run-495"><span class="linenos">495</span></a>        <span class="c1"># 列出可用磁盘</span>
</span><span id="RetroPieInstaller.run-496"><a href="#RetroPieInstaller.run-496"><span class="linenos">496</span></a>        <span class="n">disks</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">list_available_disks</span><span class="p">()</span>
</span><span id="RetroPieInstaller.run-497"><a href="#RetroPieInstaller.run-497"><span class="linenos">497</span></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">disks</span><span class="p">:</span>
</span><span id="RetroPieInstaller.run-498"><a href="#RetroPieInstaller.run-498"><span class="linenos">498</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;未找到可用磁盘&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-499"><a href="#RetroPieInstaller.run-499"><span class="linenos">499</span></a>            <span class="k">return</span>
</span><span id="RetroPieInstaller.run-500"><a href="#RetroPieInstaller.run-500"><span class="linenos">500</span></a>        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;可用磁盘:&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-501"><a href="#RetroPieInstaller.run-501"><span class="linenos">501</span></a>        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="p">(</span><span class="n">device</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="n">name</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">disks</span><span class="p">):</span>
</span><span id="RetroPieInstaller.run-502"><a href="#RetroPieInstaller.run-502"><span class="linenos">502</span></a>            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">. </span><span class="si">{</span><span class="n">device</span><span class="si">}</span><span class="s2"> (</span><span class="si">{</span><span class="n">size</span><span class="si">}</span><span class="s2">) - </span><span class="si">{</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-503"><a href="#RetroPieInstaller.run-503"><span class="linenos">503</span></a>        
</span><span id="RetroPieInstaller.run-504"><a href="#RetroPieInstaller.run-504"><span class="linenos">504</span></a>        <span class="c1"># 选择目标磁盘</span>
</span><span id="RetroPieInstaller.run-505"><a href="#RetroPieInstaller.run-505"><span class="linenos">505</span></a>        <span class="k">try</span><span class="p">:</span>
</span><span id="RetroPieInstaller.run-506"><a href="#RetroPieInstaller.run-506"><span class="linenos">506</span></a>            <span class="n">choice</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="nb">input</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;请选择目标磁盘 (1-</span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">disks</span><span class="p">)</span><span class="si">}</span><span class="s2">): &quot;</span><span class="p">))</span> <span class="o">-</span> <span class="mi">1</span>
</span><span id="RetroPieInstaller.run-507"><a href="#RetroPieInstaller.run-507"><span class="linenos">507</span></a>            <span class="k">if</span> <span class="mi">0</span> <span class="o">&lt;=</span> <span class="n">choice</span> <span class="o">&lt;</span> <span class="nb">len</span><span class="p">(</span><span class="n">disks</span><span class="p">):</span>
</span><span id="RetroPieInstaller.run-508"><a href="#RetroPieInstaller.run-508"><span class="linenos">508</span></a>                <span class="n">target_disk</span> <span class="o">=</span> <span class="n">disks</span><span class="p">[</span><span class="n">choice</span><span class="p">][</span><span class="mi">0</span><span class="p">]</span>
</span><span id="RetroPieInstaller.run-509"><a href="#RetroPieInstaller.run-509"><span class="linenos">509</span></a>            <span class="k">else</span><span class="p">:</span>
</span><span id="RetroPieInstaller.run-510"><a href="#RetroPieInstaller.run-510"><span class="linenos">510</span></a>                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;无效的选择&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-511"><a href="#RetroPieInstaller.run-511"><span class="linenos">511</span></a>                <span class="k">return</span>
</span><span id="RetroPieInstaller.run-512"><a href="#RetroPieInstaller.run-512"><span class="linenos">512</span></a>        <span class="k">except</span> <span class="ne">ValueError</span><span class="p">:</span>
</span><span id="RetroPieInstaller.run-513"><a href="#RetroPieInstaller.run-513"><span class="linenos">513</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;请输入有效的数字&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-514"><a href="#RetroPieInstaller.run-514"><span class="linenos">514</span></a>            <span class="k">return</span>
</span><span id="RetroPieInstaller.run-515"><a href="#RetroPieInstaller.run-515"><span class="linenos">515</span></a>        
</span><span id="RetroPieInstaller.run-516"><a href="#RetroPieInstaller.run-516"><span class="linenos">516</span></a>        <span class="c1"># 烧录镜像</span>
</span><span id="RetroPieInstaller.run-517"><a href="#RetroPieInstaller.run-517"><span class="linenos">517</span></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">burn_image</span><span class="p">(</span><span class="n">image_path</span><span class="p">,</span> <span class="n">target_disk</span><span class="p">):</span>
</span><span id="RetroPieInstaller.run-518"><a href="#RetroPieInstaller.run-518"><span class="linenos">518</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;RetroPie 安装完成！&quot;</span><span class="p">)</span>
</span><span id="RetroPieInstaller.run-519"><a href="#RetroPieInstaller.run-519"><span class="linenos">519</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="RetroPieInstaller.run-520"><a href="#RetroPieInstaller.run-520"><span class="linenos">520</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;烧录失败&quot;</span><span class="p">)</span>
</span></pre></div>


            <div class="docstring"><p>运行主程序</p>
</div>


                            </div>
                </section>
                <section id="main">
                            <input id="main-view-source" class="view-source-toggle-state" type="checkbox" aria-hidden="true" tabindex="-1">
<div class="attr function">
            
        <span class="def">def</span>
        <span class="name">main</span><span class="signature pdoc-code condensed">(<span class="return-annotation">):</span></span>

                <label class="view-source-button" for="main-view-source"><span>View Source</span></label>

    </div>
    <a class="headerlink" href="#main"></a>
            <div class="pdoc-code codehilite"><pre><span></span><span id="main-522"><a href="#main-522"><span class="linenos">522</span></a><span class="k">def</span> <span class="nf">main</span><span class="p">():</span>
</span><span id="main-523"><a href="#main-523"><span class="linenos">523</span></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;主函数&quot;&quot;&quot;</span>
</span><span id="main-524"><a href="#main-524"><span class="linenos">524</span></a>    <span class="n">parser</span> <span class="o">=</span> <span class="n">argparse</span><span class="o">.</span><span class="n">ArgumentParser</span><span class="p">(</span><span class="n">description</span><span class="o">=</span><span class="s2">&quot;RetroPie 镜像下载和烧录工具&quot;</span><span class="p">)</span>
</span><span id="main-525"><a href="#main-525"><span class="linenos">525</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--check-only&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;仅检查系统依赖&quot;</span><span class="p">)</span>
</span><span id="main-526"><a href="#main-526"><span class="linenos">526</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--download-only&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;仅下载镜像&quot;</span><span class="p">)</span>
</span><span id="main-527"><a href="#main-527"><span class="linenos">527</span></a>    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--list-disks&quot;</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s2">&quot;store_true&quot;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s2">&quot;列出可用磁盘&quot;</span><span class="p">)</span>
</span><span id="main-528"><a href="#main-528"><span class="linenos">528</span></a>    
</span><span id="main-529"><a href="#main-529"><span class="linenos">529</span></a>    <span class="n">args</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">()</span>
</span><span id="main-530"><a href="#main-530"><span class="linenos">530</span></a>    
</span><span id="main-531"><a href="#main-531"><span class="linenos">531</span></a>    <span class="n">installer</span> <span class="o">=</span> <span class="n">RetroPieInstaller</span><span class="p">()</span>
</span><span id="main-532"><a href="#main-532"><span class="linenos">532</span></a>    
</span><span id="main-533"><a href="#main-533"><span class="linenos">533</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">check_only</span><span class="p">:</span>
</span><span id="main-534"><a href="#main-534"><span class="linenos">534</span></a>        <span class="k">if</span> <span class="n">installer</span><span class="o">.</span><span class="n">check_dependencies</span><span class="p">():</span>
</span><span id="main-535"><a href="#main-535"><span class="linenos">535</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;系统依赖检查通过&quot;</span><span class="p">)</span>
</span><span id="main-536"><a href="#main-536"><span class="linenos">536</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="main-537"><a href="#main-537"><span class="linenos">537</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;系统依赖检查失败&quot;</span><span class="p">)</span>
</span><span id="main-538"><a href="#main-538"><span class="linenos">538</span></a>        <span class="k">return</span>
</span><span id="main-539"><a href="#main-539"><span class="linenos">539</span></a>    
</span><span id="main-540"><a href="#main-540"><span class="linenos">540</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">list_disks</span><span class="p">:</span>
</span><span id="main-541"><a href="#main-541"><span class="linenos">541</span></a>        <span class="n">disks</span> <span class="o">=</span> <span class="n">installer</span><span class="o">.</span><span class="n">list_available_disks</span><span class="p">()</span>
</span><span id="main-542"><a href="#main-542"><span class="linenos">542</span></a>        <span class="k">if</span> <span class="n">disks</span><span class="p">:</span>
</span><span id="main-543"><a href="#main-543"><span class="linenos">543</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;可用磁盘:&quot;</span><span class="p">)</span>
</span><span id="main-544"><a href="#main-544"><span class="linenos">544</span></a>            <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="p">(</span><span class="n">device</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="n">name</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">disks</span><span class="p">):</span>
</span><span id="main-545"><a href="#main-545"><span class="linenos">545</span></a>                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">. </span><span class="si">{</span><span class="n">device</span><span class="si">}</span><span class="s2"> (</span><span class="si">{</span><span class="n">size</span><span class="si">}</span><span class="s2">) - </span><span class="si">{</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="main-546"><a href="#main-546"><span class="linenos">546</span></a>        <span class="k">else</span><span class="p">:</span>
</span><span id="main-547"><a href="#main-547"><span class="linenos">547</span></a>            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;未找到可用磁盘&quot;</span><span class="p">)</span>
</span><span id="main-548"><a href="#main-548"><span class="linenos">548</span></a>        <span class="k">return</span>
</span><span id="main-549"><a href="#main-549"><span class="linenos">549</span></a>    
</span><span id="main-550"><a href="#main-550"><span class="linenos">550</span></a>    <span class="k">if</span> <span class="n">args</span><span class="o">.</span><span class="n">download_only</span><span class="p">:</span>
</span><span id="main-551"><a href="#main-551"><span class="linenos">551</span></a>        <span class="n">download_url</span> <span class="o">=</span> <span class="n">installer</span><span class="o">.</span><span class="n">get_retropie_download_url</span><span class="p">()</span>
</span><span id="main-552"><a href="#main-552"><span class="linenos">552</span></a>        <span class="k">if</span> <span class="n">download_url</span><span class="p">:</span>
</span><span id="main-553"><a href="#main-553"><span class="linenos">553</span></a>            <span class="n">archive_path</span> <span class="o">=</span> <span class="n">installer</span><span class="o">.</span><span class="n">download_image</span><span class="p">(</span><span class="n">download_url</span><span class="p">)</span>
</span><span id="main-554"><a href="#main-554"><span class="linenos">554</span></a>            <span class="k">if</span> <span class="n">archive_path</span><span class="p">:</span>
</span><span id="main-555"><a href="#main-555"><span class="linenos">555</span></a>                <span class="n">image_path</span> <span class="o">=</span> <span class="n">installer</span><span class="o">.</span><span class="n">extract_image</span><span class="p">(</span><span class="n">archive_path</span><span class="p">)</span>
</span><span id="main-556"><a href="#main-556"><span class="linenos">556</span></a>                <span class="k">if</span> <span class="n">image_path</span><span class="p">:</span>
</span><span id="main-557"><a href="#main-557"><span class="linenos">557</span></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;镜像下载完成: </span><span class="si">{</span><span class="n">image_path</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><span id="main-558"><a href="#main-558"><span class="linenos">558</span></a>        <span class="k">return</span>
</span><span id="main-559"><a href="#main-559"><span class="linenos">559</span></a>    
</span><span id="main-560"><a href="#main-560"><span class="linenos">560</span></a>    <span class="c1"># 运行完整流程</span>
</span><span id="main-561"><a href="#main-561"><span class="linenos">561</span></a>    <span class="n">installer</span><span class="o">.</span><span class="n">run</span><span class="p">()</span>
</span></pre></div>


            <div class="docstring"><p>主函数</p>
</div>


                </section>
    </main>
<script>
    function escapeHTML(html) {
        return document.createElement('div').appendChild(document.createTextNode(html)).parentNode.innerHTML;
    }

    const originalContent = document.querySelector("main.pdoc");
    let currentContent = originalContent;

    function setContent(innerHTML) {
        let elem;
        if (innerHTML) {
            elem = document.createElement("main");
            elem.classList.add("pdoc");
            elem.innerHTML = innerHTML;
        } else {
            elem = originalContent;
        }
        if (currentContent !== elem) {
            currentContent.replaceWith(elem);
            currentContent = elem;
        }
    }

    function getSearchTerm() {
        return (new URL(window.location)).searchParams.get("search");
    }

    const searchBox = document.querySelector(".pdoc input[type=search]");
    searchBox.addEventListener("input", function () {
        let url = new URL(window.location);
        if (searchBox.value.trim()) {
            url.hash = "";
            url.searchParams.set("search", searchBox.value);
        } else {
            url.searchParams.delete("search");
        }
        history.replaceState("", "", url.toString());
        onInput();
    });
    window.addEventListener("popstate", onInput);


    let search, searchErr;

    async function initialize() {
        try {
            search = await new Promise((resolve, reject) => {
                const script = document.createElement("script");
                script.type = "text/javascript";
                script.async = true;
                script.onload = () => resolve(window.pdocSearch);
                script.onerror = (e) => reject(e);
                script.src = "../search.js";
                document.getElementsByTagName("head")[0].appendChild(script);
            });
        } catch (e) {
            console.error("Cannot fetch pdoc search index");
            searchErr = "Cannot fetch search index.";
        }
        onInput();

        document.querySelector("nav.pdoc").addEventListener("click", e => {
            if (e.target.hash) {
                searchBox.value = "";
                searchBox.dispatchEvent(new Event("input"));
            }
        });
    }

    function onInput() {
        setContent((() => {
            const term = getSearchTerm();
            if (!term) {
                return null
            }
            if (searchErr) {
                return `<h3>Error: ${searchErr}</h3>`
            }
            if (!search) {
                return "<h3>Searching...</h3>"
            }

            window.scrollTo({top: 0, left: 0, behavior: 'auto'});

            const results = search(term);

            let html;
            if (results.length === 0) {
                html = `No search results for '${escapeHTML(term)}'.`
            } else {
                html = `<h4>${results.length} search result${results.length > 1 ? "s" : ""} for '${escapeHTML(term)}'.</h4>`;
            }
            for (let result of results.slice(0, 10)) {
                let doc = result.doc;
                let url = `../${doc.modulename.replaceAll(".", "/")}.html`;
                if (doc.qualname) {
                    url += `#${doc.qualname}`;
                }

                let heading;
                switch (result.doc.kind) {
                    case "function":
                        if (doc.fullname.endsWith(".__init__")) {
                            heading = `<span class="name">${doc.fullname.replace(/\.__init__$/, "")}</span>${doc.signature}`;
                        } else {
                            heading = `<span class="def">${doc.funcdef}</span> <span class="name">${doc.fullname}</span>${doc.signature}`;
                        }
                        break;
                    case "class":
                        heading = `<span class="def">class</span> <span class="name">${doc.fullname}</span>`;
                        if (doc.bases)
                            heading += `<wbr>(<span class="base">${doc.bases}</span>)`;
                        heading += `:`;
                        break;
                    case "variable":
                        heading = `<span class="name">${doc.fullname}</span>`;
                        if (doc.annotation)
                            heading += `<span class="annotation">${doc.annotation}</span>`;
                        if (doc.default_value)
                            heading += `<span class="default_value"> = ${doc.default_value}</span>`;
                        break;
                    default:
                        heading = `<span class="name">${doc.fullname}</span>`;
                        break;
                }
                html += `
                        <section class="search-result">
                        <a href="${url}" class="attr ${doc.kind}">${heading}</a>
                        <div class="docstring">${doc.doc}</div>
                        </section>
                    `;

            }
            return html;
        })());
    }

    if (getSearchTerm()) {
        initialize();
        searchBox.value = getSearchTerm();
        onInput();
    } else {
        searchBox.addEventListener("focus", initialize, {once: true});
    }

    searchBox.addEventListener("keydown", e => {
        if (["ArrowDown", "ArrowUp", "Enter"].includes(e.key)) {
            let focused = currentContent.querySelector(".search-result.focused");
            if (!focused) {
                currentContent.querySelector(".search-result").classList.add("focused");
            } else if (
                e.key === "ArrowDown"
                && focused.nextElementSibling
                && focused.nextElementSibling.classList.contains("search-result")
            ) {
                focused.classList.remove("focused");
                focused.nextElementSibling.classList.add("focused");
                focused.nextElementSibling.scrollIntoView({
                    behavior: "smooth",
                    block: "nearest",
                    inline: "nearest"
                });
            } else if (
                e.key === "ArrowUp"
                && focused.previousElementSibling
                && focused.previousElementSibling.classList.contains("search-result")
            ) {
                focused.classList.remove("focused");
                focused.previousElementSibling.classList.add("focused");
                focused.previousElementSibling.scrollIntoView({
                    behavior: "smooth",
                    block: "nearest",
                    inline: "nearest"
                });
            } else if (
                e.key === "Enter"
            ) {
                focused.querySelector("a").click();
            }
        }
    });
</script></body>
</html>