["tests/test_rom_integration.py::TestROMConfiguration::test_packages_config_has_rom_section", "tests/test_rom_integration.py::TestROMIntegration::test_download_category_with_fallback", "tests/test_rom_integration.py::TestROMIntegration::test_fallback_rom_creation", "tests/test_rom_integration.py::TestROMIntegration::test_playlist_creation", "tests/test_rom_integration.py::TestROMIntegration::test_rom_catalog_creation", "tests/test_rom_integration.py::TestROMIntegration::test_rom_downloader_initialization", "tests/test_rom_integration.py::TestROMIntegration::test_rom_manager_backup_restore", "tests/test_rom_integration.py::TestROMIntegration::test_rom_manager_clean_roms", "tests/test_rom_integration.py::TestROMIntegration::test_rom_manager_create_playlist", "tests/test_rom_integration.py::TestROMIntegration::test_rom_manager_list_roms", "tests/test_rom_integration.py::TestROMIntegration::test_rom_manager_verify_roms", "tests/test_rom_integration.py::TestROMIntegration::test_sample_rom_generation"]