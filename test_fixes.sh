#!/bin/bash

# 测试修复后的脚本
echo "🧪 测试ROM下载器修复..."

# 测试1: 列出可用分类
echo "📋 测试1: 列出可用分类"
python3 src/scripts/rom_downloader.py --list

# 测试2: 下载homebrew_games分类
echo ""
echo "📥 测试2: 下载homebrew_games分类"
mkdir -p test_roms/
python3 src/scripts/rom_downloader.py --category homebrew_games --output test_roms/

# 测试3: ROM管理器
echo ""
echo "📁 测试3: ROM管理器"
python3 src/scripts/rom_manager.py --roms-dir test_roms/ list

echo ""
echo "✅ 测试完成！"
