#!/bin/bash

# GamePlayer-Raspberry Docker图形化启动脚本
# 支持VNC远程桌面和Web界面

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示标题
echo -e "${PURPLE}"
echo "🎮 GamePlayer-Raspberry Docker图形化启动器"
echo "=============================================="
echo -e "${NC}"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
    echo "安装命令:"
    echo "  macOS: brew install docker"
    echo "  Ubuntu: sudo apt install docker.io"
    echo "  CentOS: sudo yum install docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo -e "${RED}❌ Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker服务未运行，请启动Docker服务${NC}"
    exit 1
fi

# 显示系统信息
echo -e "${CYAN}📊 系统信息:${NC}"
echo "  操作系统: $(uname -s)"
echo "  架构: $(uname -m)"
echo "  Docker版本: $(docker --version)"
echo "  当前目录: $(pwd)"
echo ""

# 创建必要目录
echo -e "${BLUE}📁 创建必要目录...${NC}"
mkdir -p data/roms/{nes,snes,gameboy,gba,genesis}
mkdir -p data/saves/{nes,snes,gameboy,gba,genesis}
mkdir -p data/web/images/covers
mkdir -p logs
mkdir -p reports
mkdir -p config
echo "✅ 目录创建完成"

# 检查配置文件
echo -e "${BLUE}🔧 检查配置文件...${NC}"
if [ ! -f "docker-compose.gui.yml" ]; then
    echo -e "${RED}❌ docker-compose.gui.yml文件不存在${NC}"
    exit 1
fi

if [ ! -f "Dockerfile.gui" ]; then
    echo -e "${RED}❌ Dockerfile.gui文件不存在${NC}"
    exit 1
fi
echo "✅ 配置文件检查完成"

# 停止现有容器
echo -e "${YELLOW}🛑 停止现有容器...${NC}"
docker-compose -f docker-compose.gui.yml down 2>/dev/null || true
echo "✅ 现有容器已停止"

# 构建镜像
echo -e "${BLUE}🔨 构建Docker镜像...${NC}"
echo "这可能需要几分钟时间，请耐心等待..."
if docker-compose -f docker-compose.gui.yml build --no-cache; then
    echo "✅ 镜像构建完成"
else
    echo -e "${RED}❌ 镜像构建失败${NC}"
    exit 1
fi

# 启动服务
echo -e "${GREEN}🚀 启动GamePlayer-Raspberry服务...${NC}"
if docker-compose -f docker-compose.gui.yml up -d; then
    echo "✅ 服务启动成功"
else
    echo -e "${RED}❌ 服务启动失败${NC}"
    exit 1
fi

# 等待服务就绪
echo -e "${BLUE}⏳ 等待服务就绪...${NC}"
sleep 10

# 检查服务状态
echo -e "${CYAN}📊 服务状态检查:${NC}"
docker-compose -f docker-compose.gui.yml ps

# 显示访问信息
echo ""
echo -e "${GREEN}🎉 GamePlayer-Raspberry Docker图形化环境启动成功！${NC}"
echo "=============================================="
echo ""
echo -e "${CYAN}🌐 Web访问地址:${NC}"
echo "  游戏中心: http://localhost:3020"
echo "  文件管理: http://localhost:8080"
echo "  系统监控: http://localhost:9000"
echo "  VNC Web版: http://localhost:6080"
echo ""
echo -e "${CYAN}🖥️ VNC远程桌面:${NC}"
echo "  地址: localhost:5900"
echo "  密码: gamer123"
echo "  分辨率: 1920x1080"
echo ""
echo -e "${CYAN}🎮 支持的模拟器:${NC}"
echo "  • mednafen - 多系统模拟器"
echo "  • fceux - NES模拟器"
echo "  • snes9x - SNES模拟器"
echo "  • visualboyadvance-m - GBA模拟器"
echo ""
echo -e "${CYAN}📁 重要目录:${NC}"
echo "  ROM文件: ./data/roms/"
echo "  存档文件: ./data/saves/"
echo "  配置文件: ./config/"
echo "  日志文件: ./logs/"
echo ""
echo -e "${YELLOW}💡 使用提示:${NC}"
echo "  1. 打开 http://localhost:3020 访问游戏中心"
echo "  2. 使用VNC客户端连接 localhost:5900 进行图形化操作"
echo "  3. 将ROM文件放入 ./data/roms/ 对应目录"
echo "  4. 游戏存档会自动保存到 ./data/saves/"
echo ""
echo -e "${CYAN}🔧 管理命令:${NC}"
echo "  查看日志: docker-compose -f docker-compose.gui.yml logs -f"
echo "  停止服务: docker-compose -f docker-compose.gui.yml down"
echo "  重启服务: docker-compose -f docker-compose.gui.yml restart"
echo "  进入容器: docker exec -it gameplayer-raspberry-gui bash"
echo ""

# 检查服务健康状态
echo -e "${BLUE}🏥 检查服务健康状态...${NC}"
sleep 5

# 测试Web服务
if curl -s http://localhost:3020/api/status >/dev/null 2>&1; then
    echo "✅ Web服务正常"
else
    echo "⚠️ Web服务可能还在启动中，请稍等片刻"
fi

# 测试VNC服务
if nc -z localhost 5900 2>/dev/null; then
    echo "✅ VNC服务正常"
else
    echo "⚠️ VNC服务可能还在启动中，请稍等片刻"
fi

echo ""
echo -e "${GREEN}🎮 享受游戏时光！${NC}"
echo "=============================================="

# 可选：自动打开浏览器
if command -v open &> /dev/null; then
    echo "🌐 正在打开浏览器..."
    sleep 2
    open http://localhost:3020
elif command -v xdg-open &> /dev/null; then
    echo "🌐 正在打开浏览器..."
    sleep 2
    xdg-open http://localhost:3020
fi
