# 树莓派模拟环境 Dockerfile
# 基于ARM64架构模拟树莓派环境

FROM arm64v8/ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV DISPLAY=:1
ENV HOME=/home/<USER>
ENV USER=pi

# 安装基础包
RUN apt-get update && apt-get install -y \
    # 系统工具
    sudo curl wget git vim nano htop \
    # Python环境
    python3 python3-pip python3-dev \
    # 图形界面
    xvfb fluxbox x11vnc novnc websockify \
    # 音频支持
    pulseaudio alsa-utils \
    # 游戏相关
    libsdl2-dev libsdl2-image-dev libsdl2-mixer-dev \
    # 编译工具
    build-essential cmake pkg-config \
    # 网络工具
    net-tools iputils-ping \
    # 文件工具
    unzip p7zip-full \
    && rm -rf /var/lib/apt/lists/*

# 创建pi用户（模拟树莓派默认用户）
RUN useradd -m -s /bin/bash pi && \
    echo "pi:raspberry" | chpasswd && \
    usermod -aG sudo pi && \
    echo "pi ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# 切换到pi用户
USER pi
WORKDIR /home/<USER>

# 创建树莓派目录结构
RUN mkdir -p /home/<USER>/RetroPie/roms/nes && \
    mkdir -p /home/<USER>/RetroPie/configs && \
    mkdir -p /home/<USER>/RetroPie/emulators && \
    mkdir -p /home/<USER>/GamePlayer-Raspberry && \
    mkdir -p /home/<USER>/.config

# 安装Python包
RUN pip3 install --user \
    pygame \
    requests \
    flask \
    pillow \
    pyyaml \
    psutil \
    tqdm

# 复制项目文件
COPY --chown=pi:pi . /home/<USER>/GamePlayer-Raspberry/

# 设置工作目录
WORKDIR /home/<USER>/GamePlayer-Raspberry

# 安装项目依赖
RUN pip3 install --user -r requirements.txt

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "🚀 启动树莓派模拟环境..."\n\
\n\
# 启动虚拟显示\n\
Xvfb :1 -screen 0 1024x768x24 &\n\
sleep 3\n\
\n\
# 启动窗口管理器\n\
export DISPLAY=:1\n\
fluxbox &\n\
sleep 2\n\
\n\
# 启动VNC服务器\n\
x11vnc -display :1 -nopw -listen 0.0.0.0 -xkb -ncache 10 -ncache_cr -forever -rfbport 5901 &\n\
sleep 3\n\
\n\
# 启动noVNC\n\
cd /usr/share/novnc\n\
./utils/launch.sh --vnc localhost:5901 --listen 6080 &\n\
sleep 3\n\
\n\
# 启动HTTP服务器\n\
cd /home/<USER>/GamePlayer-Raspberry\n\
python3 -m http.server 8080 &\n\
\n\
echo "✅ 所有服务已启动"\n\
echo "📱 VNC访问: http://localhost:6080/vnc.html"\n\
echo "📁 文件访问: http://localhost:8080"\n\
echo "🎮 开始下载ROM文件..."\n\
\n\
# 下载ROM文件\n\
python3 scripts/rom_downloader.py --output /home/<USER>/RetroPie/roms/nes\n\
\n\
echo "🎉 树莓派模拟环境就绪！"\n\
\n\
# 保持容器运行\n\
tail -f /dev/null\n\
' > /home/<USER>/start-raspberry-sim.sh && chmod +x /home/<USER>/start-raspberry-sim.sh

# 创建NES模拟器启动脚本
RUN echo '#!/bin/bash\n\
echo "🎮 启动NES游戏模拟器..."\n\
\n\
export DISPLAY=:1\n\
cd /home/<USER>/GamePlayer-Raspberry\n\
\n\
# 检查ROM文件\n\
ROM_DIR="/home/<USER>/RetroPie/roms/nes"\n\
if [ ! -d "$ROM_DIR" ] || [ -z "$(ls -A $ROM_DIR/*.nes 2>/dev/null)" ]; then\n\
    echo "📥 下载ROM文件..."\n\
    python3 scripts/rom_downloader.py --output "$ROM_DIR"\n\
fi\n\
\n\
# 列出可用ROM\n\
echo "📋 可用游戏:"\n\
python3 scripts/rom_manager.py --roms-dir "$ROM_DIR" list\n\
\n\
# 启动游戏选择界面\n\
python3 scripts/nes_game_launcher.py\n\
' > /home/<USER>/start-nes-games.sh && chmod +x /home/<USER>/start-nes-games.sh

# 创建RetroPie配置文件
RUN echo '# RetroPie 配置文件\n\
[nes]\n\
emulator = "nesticle"\n\
rom_path = "/home/<USER>/RetroPie/roms/nes"\n\
save_path = "/home/<USER>/RetroPie/saves/nes"\n\
\n\
[nesticle]\n\
executable = "/home/<USER>/GamePlayer-Raspberry/core/nesticle_installer.py"\n\
config_file = "/home/<USER>/RetroPie/configs/nes/nesticle.cfg"\n\
\n\
[virtuanes]\n\
executable = "/home/<USER>/GamePlayer-Raspberry/core/virtuanes_installer.py"\n\
config_file = "/home/<USER>/RetroPie/configs/nes/virtuanes.cfg"\n\
' > /home/<USER>/RetroPie/configs/retropie.conf

# 暴露端口
EXPOSE 5901 6080 8080

# 设置启动命令
CMD ["/home/<USER>/start-raspberry-sim.sh"]
