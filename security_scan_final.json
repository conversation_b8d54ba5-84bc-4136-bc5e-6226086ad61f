{"errors": [{"filename": "src/core/device_manager.py", "reason": "syntax error while parsing AST from file"}, {"filename": "src/core/game_launcher.py", "reason": "syntax error while parsing AST from file"}, {"filename": "src/core/save_manager.py", "reason": "syntax error while parsing AST from file"}, {"filename": "src/scripts/raspberry_image_builder.py", "reason": "syntax error while parsing AST from file"}, {"filename": "src/scripts/smart_installer.py", "reason": "syntax error while parsing AST from file"}], "generated_at": "2025-07-01T00:41:18Z", "metrics": {"_totals": {"CONFIDENCE.HIGH": 236, "CONFIDENCE.LOW": 3, "CONFIDENCE.MEDIUM": 2, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 5, "SEVERITY.LOW": 223, "SEVERITY.MEDIUM": 13, "SEVERITY.UNDEFINED": 0, "loc": 19287, "nosec": 0, "skipped_tests": 0}, "src/core/audio_manager.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 321, "nosec": 0, "skipped_tests": 0}, "src/core/base_installer.py": {"CONFIDENCE.HIGH": 5, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 4, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 74, "nosec": 0, "skipped_tests": 0}, "src/core/bing_cover_downloader.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 262, "nosec": 0, "skipped_tests": 0}, "src/core/cheat_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 519, "nosec": 0, "skipped_tests": 0}, "src/core/config_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 217, "nosec": 0, "skipped_tests": 0}, "src/core/cover_downloader.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 212, "nosec": 0, "skipped_tests": 0}, "src/core/device_manager.py": {"loc": 315, "nosec": 0, "skipped_tests": 0}, "src/core/enhanced_cover_downloader.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 258, "nosec": 0, "skipped_tests": 0}, "src/core/game_health_checker.py": {"CONFIDENCE.HIGH": 12, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 11, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 461, "nosec": 0, "skipped_tests": 0}, "src/core/game_launcher.py": {"loc": 271, "nosec": 0, "skipped_tests": 0}, "src/core/hdmi_config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 273, "nosec": 0, "skipped_tests": 0}, "src/core/nes_emulator.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 493, "nosec": 0, "skipped_tests": 0}, "src/core/nesticle_installer.py": {"CONFIDENCE.HIGH": 12, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 8, "SEVERITY.MEDIUM": 4, "SEVERITY.UNDEFINED": 0, "loc": 502, "nosec": 0, "skipped_tests": 0}, "src/core/retropie_installer.py": {"CONFIDENCE.HIGH": 27, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 26, "SEVERITY.MEDIUM": 2, "SEVERITY.UNDEFINED": 0, "loc": 587, "nosec": 0, "skipped_tests": 0}, "src/core/rom_downloader.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 417, "nosec": 0, "skipped_tests": 0}, "src/core/rom_manager.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 329, "nosec": 0, "skipped_tests": 0}, "src/core/save_manager.py": {"loc": 249, "nosec": 0, "skipped_tests": 0}, "src/core/settings_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 270, "nosec": 0, "skipped_tests": 0}, "src/core/system_checker.py": {"CONFIDENCE.HIGH": 38, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 37, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 533, "nosec": 0, "skipped_tests": 0}, "src/core/virtuanes_installer.py": {"CONFIDENCE.HIGH": 6, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 4, "SEVERITY.MEDIUM": 2, "SEVERITY.UNDEFINED": 0, "loc": 308, "nosec": 0, "skipped_tests": 0}, "src/scripts/auto_code_fix.py": {"CONFIDENCE.HIGH": 5, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 5, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 266, "nosec": 0, "skipped_tests": 0}, "src/scripts/auto_fix_all_games.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 234, "nosec": 0, "skipped_tests": 0}, "src/scripts/auto_full_pipeline.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 210, "nosec": 0, "skipped_tests": 0}, "src/scripts/auto_save_sync.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 2, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 2, "SEVERITY.UNDEFINED": 0, "loc": 163, "nosec": 0, "skipped_tests": 0}, "src/scripts/auto_security_fix.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 213, "nosec": 0, "skipped_tests": 0}, "src/scripts/auto_update_rom_sources.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 292, "nosec": 0, "skipped_tests": 0}, "src/scripts/check_core_dependencies.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 114, "nosec": 0, "skipped_tests": 0}, "src/scripts/check_dependencies.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 257, "nosec": 0, "skipped_tests": 0}, "src/scripts/demo_all_features.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 395, "nosec": 0, "skipped_tests": 0}, "src/scripts/demo_game.py": {"CONFIDENCE.HIGH": 12, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 12, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 202, "nosec": 0, "skipped_tests": 0}, "src/scripts/download_roms.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 145, "nosec": 0, "skipped_tests": 0}, "src/scripts/enhanced_game_launcher.py": {"CONFIDENCE.HIGH": 5, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 5, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 387, "nosec": 0, "skipped_tests": 0}, "src/scripts/final_nes_fix.py": {"CONFIDENCE.HIGH": 9, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 9, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 219, "nosec": 0, "skipped_tests": 0}, "src/scripts/fix_emulator_startup.py": {"CONFIDENCE.HIGH": 11, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 10, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 309, "nosec": 0, "skipped_tests": 0}, "src/scripts/fix_nes_emulator.py": {"CONFIDENCE.HIGH": 11, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 10, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 279, "nosec": 0, "skipped_tests": 0}, "src/scripts/fix_nes_runtime_failure.py": {"CONFIDENCE.HIGH": 8, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 8, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 318, "nosec": 0, "skipped_tests": 0}, "src/scripts/image_integration_checker.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 349, "nosec": 0, "skipped_tests": 0}, "src/scripts/manage_cover_sources.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 220, "nosec": 0, "skipped_tests": 0}, "src/scripts/nes_game_launcher.py": {"CONFIDENCE.HIGH": 5, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 5, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 440, "nosec": 0, "skipped_tests": 0}, "src/scripts/quick_fix_nes.py": {"CONFIDENCE.HIGH": 5, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 5, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 157, "nosec": 0, "skipped_tests": 0}, "src/scripts/quick_function_test.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 204, "nosec": 0, "skipped_tests": 0}, "src/scripts/raspberry_image_builder.py": {"loc": 501, "nosec": 0, "skipped_tests": 0}, "src/scripts/rom_downloader.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 423, "nosec": 0, "skipped_tests": 0}, "src/scripts/rom_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 238, "nosec": 0, "skipped_tests": 0}, "src/scripts/run_nes_game.py": {"CONFIDENCE.HIGH": 9, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 9, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 304, "nosec": 0, "skipped_tests": 0}, "src/scripts/simple_demo_server.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 2206, "nosec": 0, "skipped_tests": 0}, "src/scripts/simple_nes_player.py": {"CONFIDENCE.HIGH": 4, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 4, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 273, "nosec": 0, "skipped_tests": 0}, "src/scripts/smart_installer.py": {"loc": 455, "nosec": 0, "skipped_tests": 0}, "src/scripts/test_auto_cheats.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 134, "nosec": 0, "skipped_tests": 0}, "src/scripts/test_core_functions.py": {"CONFIDENCE.HIGH": 11, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 10, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 292, "nosec": 0, "skipped_tests": 0}, "src/scripts/test_enhanced_covers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 149, "nosec": 0, "skipped_tests": 0}, "src/scripts/test_settings.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 124, "nosec": 0, "skipped_tests": 0}, "src/scripts/universal_game_launcher.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 101, "nosec": 0, "skipped_tests": 0}, "src/scripts/verify_docker_integration.py": {"CONFIDENCE.HIGH": 9, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 9, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 190, "nosec": 0, "skipped_tests": 0}, "src/systems/systems/hdmi/core/hdmi_config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 260, "nosec": 0, "skipped_tests": 0}, "src/systems/systems/hdmi/tests/test_hdmi_config.py": {"CONFIDENCE.HIGH": 9, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 9, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 152, "nosec": 0, "skipped_tests": 0}, "src/systems/systems/retropie/core/log_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 195, "nosec": 0, "skipped_tests": 0}, "src/systems/systems/retropie/core/log_uploader.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 40, "nosec": 0, "skipped_tests": 0}, "src/systems/systems/retropie/core/logger_config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 43, "nosec": 0, "skipped_tests": 0}, "src/systems/systems/retropie/core/retropie_installer.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 436, "nosec": 0, "skipped_tests": 0}, "src/systems/systems/retropie/roms/rom_downloader.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 431, "nosec": 0, "skipped_tests": 0}, "src/systems/systems/retropie/tests/tests/conftest.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "src/systems/systems/retropie/tests/tests/run_all_tests.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 9, "nosec": 0, "skipped_tests": 0}, "src/web/web_config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 82, "nosec": 0, "skipped_tests": 0}}, "results": [{"code": "11 import threading\n12 import subprocess\n13 from pathlib import Path\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/core/audio_manager.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 12, "line_range": [12], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "4 import os\n5 import subprocess\n6 \n", "col_offset": 0, "end_col_offset": 17, "filename": "src/core/base_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 5, "line_range": [5], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "58         try:\n59             result = subprocess.run(\n60                 [\"dpkg\", \"-l\", package],\n61                 capture_output=True,\n62                 text=True,\n63                 check=False\n64             )\n65             return result.returncode == 0\n", "col_offset": 21, "end_col_offset": 13, "filename": "src/core/base_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 59, "line_range": [59, 60, 61, 62, 63, 64], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "58         try:\n59             result = subprocess.run(\n60                 [\"dpkg\", \"-l\", package],\n61                 capture_output=True,\n62                 text=True,\n63                 check=False\n64             )\n65             return result.returncode == 0\n", "col_offset": 21, "end_col_offset": 13, "filename": "src/core/base_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 59, "line_range": [59, 60, 61, 62, 63, 64], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "79         \"\"\"Helper method to run shell commands\"\"\"\n80         import subprocess\n81         try:\n", "col_offset": 8, "end_col_offset": 25, "filename": "src/core/base_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 80, "line_range": [80], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "81         try:\n82             result = subprocess.run(command, shell=True, check=True,\n83                                   stdout=subprocess.PIPE, stderr=subprocess.PIPE)\n84             return result.stdout.decode('utf-8')\n", "col_offset": 21, "end_col_offset": 81, "filename": "src/core/base_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "HIGH", "issue_text": "subprocess call with shell=True identified, security issue.", "line_number": 82, "line_range": [82, 83], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b602_subprocess_popen_with_shell_equals_true.html", "test_id": "B602", "test_name": "subprocess_popen_with_shell_equals_true"}, {"code": "10 import time\n11 import subprocess\n12 import shutil\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/core/game_health_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 11, "line_range": [11], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "161             # 检查命令是否存在\n162             result = subprocess.run(\n163                 [\"which\", command],\n164                 capture_output=True,\n165                 text=True,\n166                 timeout=10\n167             )\n168 \n", "col_offset": 21, "end_col_offset": 13, "filename": "src/core/game_health_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 162, "line_range": [162, 163, 164, 165, 166, 167], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "161             # 检查命令是否存在\n162             result = subprocess.run(\n163                 [\"which\", command],\n164                 capture_output=True,\n165                 text=True,\n166                 timeout=10\n167             )\n168 \n", "col_offset": 21, "end_col_offset": 13, "filename": "src/core/game_health_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 162, "line_range": [162, 163, 164, 165, 166, 167], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "171                 try:\n172                     help_result = subprocess.run(\n173                         [command] + emulator_config[\"test_args\"],\n174                         capture_output=True,\n175                         text=True,\n176                         timeout=10\n177                     )\n178                     return \"available\"\n", "col_offset": 34, "end_col_offset": 21, "filename": "src/core/game_health_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 172, "line_range": [172, 173, 174, 175, 176, 177], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "298             logger.info(\"🔄 更新Homebrew...\")\n299             subprocess.run([\"brew\", \"update\"], capture_output=True, timeout=120)\n300 \n", "col_offset": 12, "end_col_offset": 80, "filename": "src/core/game_health_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 299, "line_range": [299], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "298             logger.info(\"🔄 更新Homebrew...\")\n299             subprocess.run([\"brew\", \"update\"], capture_output=True, timeout=120)\n300 \n", "col_offset": 12, "end_col_offset": 80, "filename": "src/core/game_health_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 299, "line_range": [299], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "300 \n301             result = subprocess.run(\n302                 cmd_parts,\n303                 capture_output=True,\n304                 text=True,\n305                 timeout=600  # 10分钟超时\n306             )\n307 \n", "col_offset": 21, "end_col_offset": 13, "filename": "src/core/game_health_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 301, "line_range": [301, 302, 303, 304, 305, 306], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "325         try:\n326             result = subprocess.run([\"which\", \"brew\"], capture_output=True, timeout=10)\n327             return result.returncode == 0\n", "col_offset": 21, "end_col_offset": 87, "filename": "src/core/game_health_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 326, "line_range": [326], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "325         try:\n326             result = subprocess.run([\"which\", \"brew\"], capture_output=True, timeout=10)\n327             return result.returncode == 0\n", "col_offset": 21, "end_col_offset": 87, "filename": "src/core/game_health_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 326, "line_range": [326], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "336                 install_script,\n337                 shell=True,\n338                 capture_output=True,\n339                 text=True,\n340                 timeout=600\n341             )\n342             return result.returncode == 0\n343         except:\n344             return False\n", "col_offset": 21, "end_col_offset": 13, "filename": "src/core/game_health_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "HIGH", "issue_text": "subprocess call with shell=True identified, security issue.", "line_number": 337, "line_range": [335, 336, 337, 338, 339, 340, 341], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b602_subprocess_popen_with_shell_equals_true.html", "test_id": "B602", "test_name": "subprocess_popen_with_shell_equals_true"}, {"code": "377                 cmd_parts = alt_cmd.split()\n378                 result = subprocess.run(\n379                     cmd_parts,\n380                     capture_output=True,\n381                     text=True,\n382                     timeout=300\n383                 )\n384 \n", "col_offset": 25, "end_col_offset": 17, "filename": "src/core/game_health_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 378, "line_range": [378, 379, 380, 381, 382, 383], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "387                         return True\n388             except:\n389                 continue\n390 \n", "col_offset": 12, "end_col_offset": 24, "filename": "src/core/game_health_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Continue detected.", "line_number": 388, "line_range": [388, 389], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b112_try_except_continue.html", "test_id": "B112", "test_name": "try_except_continue"}, {"code": "46                     return font\n47             except:\n48                 continue\n49 \n", "col_offset": 12, "end_col_offset": 24, "filename": "src/core/nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Continue detected.", "line_number": 47, "line_range": [47, 48], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b112_try_except_continue.html", "test_id": "B112", "test_name": "try_except_continue"}, {"code": "549 \n550         except Exception as e:\n551             # 控制器输入失败时静默处理\n552             pass\n553 \n", "col_offset": 8, "end_col_offset": 16, "filename": "src/core/nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 550, "line_range": [550, 551, 552], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "32 import os\n33 import subprocess\n34 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/core/nesticle_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 33, "line_range": [33], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "149                     f.write(script_content)\n150                 os.chmod(script_path, 0o755)\n151                 logger.info(f\"✓ 测试环境安装脚本已创建: {script_path}\")\n", "col_offset": 16, "end_col_offset": 44, "filename": "src/core/nesticle_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 732, "link": "https://cwe.mitre.org/data/definitions/732.html"}, "issue_severity": "MEDIUM", "issue_text": "Chmod setting a permissive mask 0o755 on file (script_path).", "line_number": 150, "line_range": [150], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b103_set_bad_file_permissions.html", "test_id": "B103", "test_name": "set_bad_file_permissions"}, {"code": "172 \n173                 os.chmod(script_path, 0o755)\n174                 logger.info(f\"✓ 下载成功: {script_path}\")\n", "col_offset": 16, "end_col_offset": 44, "filename": "src/core/nesticle_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 732, "link": "https://cwe.mitre.org/data/definitions/732.html"}, "issue_severity": "MEDIUM", "issue_text": "Chmod setting a permissive mask 0o755 on file (script_path).", "line_number": 173, "line_range": [173], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b103_set_bad_file_permissions.html", "test_id": "B103", "test_name": "set_bad_file_permissions"}, {"code": "202             # 执行安装脚本\n203             result = subprocess.run(\n204                 [str(script_path)],\n205                 cwd=self.install_dir,\n206                 check=True,\n207                 capture_output=True,\n208                 text=True\n209             )\n210 \n", "col_offset": 21, "end_col_offset": 13, "filename": "src/core/nesticle_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 203, "line_range": [203, 204, 205, 206, 207, 208, 209], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "392                 f.write(script_content)\n393             os.chmod(auto_save_script, 0o755)\n394             logger.info(f\"✓ 自动保存脚本已创建: {auto_save_script}\")\n", "col_offset": 12, "end_col_offset": 45, "filename": "src/core/nesticle_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 732, "link": "https://cwe.mitre.org/data/definitions/732.html"}, "issue_severity": "MEDIUM", "issue_text": "Chmod setting a permissive mask 0o755 on file (auto_save_script).", "line_number": 393, "line_range": [393], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b103_set_bad_file_permissions.html", "test_id": "B103", "test_name": "set_bad_file_permissions"}, {"code": "463             # 更新 MIME 数据库\n464             subprocess.run([\"sudo\", \"update-desktop-database\"], check=True)\n465 \n", "col_offset": 12, "end_col_offset": 75, "filename": "src/core/nesticle_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 464, "line_range": [464], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "463             # 更新 MIME 数据库\n464             subprocess.run([\"sudo\", \"update-desktop-database\"], check=True)\n465 \n", "col_offset": 12, "end_col_offset": 75, "filename": "src/core/nesticle_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 464, "line_range": [464], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "506 \n507             os.chmod(script_path, 0o755)\n508             logger.info(f\"✓ 启动脚本已创建: {script_path}\")\n", "col_offset": 12, "end_col_offset": 40, "filename": "src/core/nesticle_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 732, "link": "https://cwe.mitre.org/data/definitions/732.html"}, "issue_severity": "MEDIUM", "issue_text": "Chmod setting a permissive mask 0o755 on file (script_path).", "line_number": 507, "line_range": [507], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b103_set_bad_file_permissions.html", "test_id": "B103", "test_name": "set_bad_file_permissions"}, {"code": "578             try:\n579                 if subprocess.run([\"systemctl\", \"is-enabled\", \"nesticle-autostart.service\"],\n580                                 capture_output=True, check=False).returncode == 0:\n581                     logger.info(\"✓ 自启动服务已启用\")\n", "col_offset": 19, "end_col_offset": 65, "filename": "src/core/nesticle_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 579, "line_range": [579, 580], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "578             try:\n579                 if subprocess.run([\"systemctl\", \"is-enabled\", \"nesticle-autostart.service\"],\n580                                 capture_output=True, check=False).returncode == 0:\n581                     logger.info(\"✓ 自启动服务已启用\")\n", "col_offset": 19, "end_col_offset": 65, "filename": "src/core/nesticle_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 579, "line_range": [579, 580], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "587         # 测试可执行文件\n588         if subprocess.run([\"which\", \"nesticle\"], capture_output=True).returncode == 0:\n589             logger.info(\"✓ Nesticle 可执行文件测试通过\")\n", "col_offset": 11, "end_col_offset": 69, "filename": "src/core/nesticle_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 588, "line_range": [588], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "587         # 测试可执行文件\n588         if subprocess.run([\"which\", \"nesticle\"], capture_output=True).returncode == 0:\n589             logger.info(\"✓ Nesticle 可执行文件测试通过\")\n", "col_offset": 11, "end_col_offset": 69, "filename": "src/core/nesticle_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 588, "line_range": [588], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "32 import platform\n33 import subprocess\n34 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 33, "line_range": [33], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "171         try:\n172             result = subprocess.run(\n173                 cmd, capture_output=True, text=True, check=check)\n174             return result.returncode, result.stdout, result.stderr\n", "col_offset": 21, "end_col_offset": 65, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 172, "line_range": [172, 173], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "460             # 挂载镜像\n461             subprocess.run([\n462                 \"sudo\", \"mount\", \"-o\", f\"loop,offset={offset}\",\n463                 str(image_path), str(mount_point)\n464             ], check=True)\n465 \n", "col_offset": 12, "end_col_offset": 26, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 461, "line_range": [461, 462, 463, 464], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "460             # 挂载镜像\n461             subprocess.run([\n462                 \"sudo\", \"mount\", \"-o\", f\"loop,offset={offset}\",\n463                 str(image_path), str(mount_point)\n464             ], check=True)\n465 \n", "col_offset": 12, "end_col_offset": 26, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 461, "line_range": [461, 462, 463, 464], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "471                     shutil.copy2(virtuanes_script, target_script)\n472                     os.chmod(target_script, 0o755)\n473                     logger.info(\"✓ VirtuaNES 集成脚本已复制到镜像\")\n", "col_offset": 20, "end_col_offset": 50, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 732, "link": "https://cwe.mitre.org/data/definitions/732.html"}, "issue_severity": "MEDIUM", "issue_text": "Chmod setting a permissive mask 0o755 on file (target_script).", "line_number": 472, "line_range": [472], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b103_set_bad_file_permissions.html", "test_id": "B103", "test_name": "set_bad_file_permissions"}, {"code": "499                 # 卸载镜像\n500                 subprocess.run([\"sudo\", \"umount\", str(mount_point)], check=True)\n501 \n", "col_offset": 16, "end_col_offset": 80, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 500, "line_range": [500], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "499                 # 卸载镜像\n500                 subprocess.run([\"sudo\", \"umount\", str(mount_point)], check=True)\n501 \n", "col_offset": 16, "end_col_offset": 80, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 500, "line_range": [500], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "517             # 使用 fdisk 获取分区信息\n518             result = subprocess.run([\n519                 \"fdisk\", \"-l\", str(image_path)\n520             ], capture_output=True, text=True, check=True)\n521 \n", "col_offset": 21, "end_col_offset": 58, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 518, "line_range": [518, 519, 520], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "517             # 使用 fdisk 获取分区信息\n518             result = subprocess.run([\n519                 \"fdisk\", \"-l\", str(image_path)\n520             ], capture_output=True, text=True, check=True)\n521 \n", "col_offset": 21, "end_col_offset": 58, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 518, "line_range": [518, 519, 520], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "587             # 创建挂载点\n588             mount_point = \"/tmp/nesticle_mount\"\n589             subprocess.run([\"sudo\", \"mkdir\", \"-p\", mount_point], check=True)\n", "col_offset": 26, "end_col_offset": 47, "filename": "src/core/retropie_installer.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 377, "link": "https://cwe.mitre.org/data/definitions/377.html"}, "issue_severity": "MEDIUM", "issue_text": "Probable insecure usage of temp file/directory.", "line_number": 588, "line_range": [588], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b108_hardcoded_tmp_directory.html", "test_id": "B108", "test_name": "hardcoded_tmp_directory"}, {"code": "588             mount_point = \"/tmp/nesticle_mount\"\n589             subprocess.run([\"sudo\", \"mkdir\", \"-p\", mount_point], check=True)\n590 \n", "col_offset": 12, "end_col_offset": 76, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 589, "line_range": [589], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "588             mount_point = \"/tmp/nesticle_mount\"\n589             subprocess.run([\"sudo\", \"mkdir\", \"-p\", mount_point], check=True)\n590 \n", "col_offset": 12, "end_col_offset": 76, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 589, "line_range": [589], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "591             # 挂载镜像\n592             loop_device = subprocess.check_output(\n593                 [\"sudo\", \"losetup\", \"--find\", \"--show\", image_path],\n594                 text=True\n595             ).strip()\n596 \n", "col_offset": 26, "end_col_offset": 13, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 592, "line_range": [592, 593, 594, 595], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "591             # 挂载镜像\n592             loop_device = subprocess.check_output(\n593                 [\"sudo\", \"losetup\", \"--find\", \"--show\", image_path],\n594                 text=True\n595             ).strip()\n596 \n", "col_offset": 26, "end_col_offset": 13, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 592, "line_range": [592, 593, 594, 595], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "596 \n597             subprocess.run([\"sudo\", \"mount\", f\"{loop_device}p2\", mount_point], check=True)\n598 \n", "col_offset": 12, "end_col_offset": 90, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 597, "line_range": [597], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "596 \n597             subprocess.run([\"sudo\", \"mount\", f\"{loop_device}p2\", mount_point], check=True)\n598 \n", "col_offset": 12, "end_col_offset": 90, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 597, "line_range": [597], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "600             target_script = f\"{mount_point}/opt/retropie/emulators/nesticle/integrate_nesticle.sh\"\n601             subprocess.run([\"sudo\", \"mkdir\", \"-p\", os.path.dirname(target_script)], check=True)\n602             subprocess.run([\n", "col_offset": 12, "end_col_offset": 95, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 601, "line_range": [601], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "600             target_script = f\"{mount_point}/opt/retropie/emulators/nesticle/integrate_nesticle.sh\"\n601             subprocess.run([\"sudo\", \"mkdir\", \"-p\", os.path.dirname(target_script)], check=True)\n602             subprocess.run([\n", "col_offset": 12, "end_col_offset": 95, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 601, "line_range": [601], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "601             subprocess.run([\"sudo\", \"mkdir\", \"-p\", os.path.dirname(target_script)], check=True)\n602             subprocess.run([\n603                 \"sudo\", \"cp\",\n604                 \"scripts/auto_nesticle_integration.sh\",\n605                 target_script\n606             ], check=True)\n607             subprocess.run([\"sudo\", \"chmod\", \"+x\", target_script], check=True)\n", "col_offset": 12, "end_col_offset": 26, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 602, "line_range": [602, 603, 604, 605, 606], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "601             subprocess.run([\"sudo\", \"mkdir\", \"-p\", os.path.dirname(target_script)], check=True)\n602             subprocess.run([\n603                 \"sudo\", \"cp\",\n604                 \"scripts/auto_nesticle_integration.sh\",\n605                 target_script\n606             ], check=True)\n607             subprocess.run([\"sudo\", \"chmod\", \"+x\", target_script], check=True)\n", "col_offset": 12, "end_col_offset": 26, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 602, "line_range": [602, 603, 604, 605, 606], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "606             ], check=True)\n607             subprocess.run([\"sudo\", \"chmod\", \"+x\", target_script], check=True)\n608 \n", "col_offset": 12, "end_col_offset": 78, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 607, "line_range": [607], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "606             ], check=True)\n607             subprocess.run([\"sudo\", \"chmod\", \"+x\", target_script], check=True)\n608 \n", "col_offset": 12, "end_col_offset": 78, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 607, "line_range": [607], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "632             # 卸载镜像\n633             subprocess.run([\"sudo\", \"umount\", mount_point], check=True)\n634             subprocess.run([\"sudo\", \"losetup\", \"-d\", loop_device], check=True)\n", "col_offset": 12, "end_col_offset": 71, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 633, "line_range": [633], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "632             # 卸载镜像\n633             subprocess.run([\"sudo\", \"umount\", mount_point], check=True)\n634             subprocess.run([\"sudo\", \"losetup\", \"-d\", loop_device], check=True)\n", "col_offset": 12, "end_col_offset": 71, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 633, "line_range": [633], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "633             subprocess.run([\"sudo\", \"umount\", mount_point], check=True)\n634             subprocess.run([\"sudo\", \"losetup\", \"-d\", loop_device], check=True)\n635             subprocess.run([\"sudo\", \"rmdir\", mount_point], check=True)\n", "col_offset": 12, "end_col_offset": 78, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 634, "line_range": [634], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "633             subprocess.run([\"sudo\", \"umount\", mount_point], check=True)\n634             subprocess.run([\"sudo\", \"losetup\", \"-d\", loop_device], check=True)\n635             subprocess.run([\"sudo\", \"rmdir\", mount_point], check=True)\n", "col_offset": 12, "end_col_offset": 78, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 634, "line_range": [634], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "634             subprocess.run([\"sudo\", \"losetup\", \"-d\", loop_device], check=True)\n635             subprocess.run([\"sudo\", \"rmdir\", mount_point], check=True)\n636 \n", "col_offset": 12, "end_col_offset": 70, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 635, "line_range": [635], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "634             subprocess.run([\"sudo\", \"losetup\", \"-d\", loop_device], check=True)\n635             subprocess.run([\"sudo\", \"rmdir\", mount_point], check=True)\n636 \n", "col_offset": 12, "end_col_offset": 70, "filename": "src/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 635, "line_range": [635], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "76         try:\n77             response = requests.get(url, stream=True)\n78             response.raise_for_status()\n", "col_offset": 23, "end_col_offset": 53, "filename": "src/core/rom_downloader.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 77, "line_range": [77], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "350                                 rom_info.update(metadata)\n351                         except:\n352                             pass\n353 \n", "col_offset": 24, "end_col_offset": 32, "filename": "src/core/rom_manager.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 351, "line_range": [351, 352], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "10 import time\n11 import subprocess\n12 import threading\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 11, "line_range": [11], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "156             try:\n157                 result = subprocess.run([\"ls\", \"/dev/input/event*\"],\n158                                       capture_output=True, text=True, shell=True)\n159                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 81, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 157, "line_range": [157, 158], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "157                 result = subprocess.run([\"ls\", \"/dev/input/event*\"],\n158                                       capture_output=True, text=True, shell=True)\n159                 if result.returncode == 0:\n160                     event_devices = result.stdout.strip().split('\\n')\n", "col_offset": 25, "end_col_offset": 81, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "HIGH", "issue_text": "subprocess call with shell=True identified, security issue.", "line_number": 158, "line_range": [157, 158], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b602_subprocess_popen_with_shell_equals_true.html", "test_id": "B602", "test_name": "subprocess_popen_with_shell_equals_true"}, {"code": "160                     event_devices = result.stdout.strip().split('\\n')\n161             except:\n162                 pass\n163 \n", "col_offset": 12, "end_col_offset": 20, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 161, "line_range": [161, 162], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "166             try:\n167                 result = subprocess.run([\"python3\", \"-c\",\n168                     \"import pygame; pygame.init(); print(pygame.joystick.get_count())\"],\n169                     capture_output=True, text=True)\n170                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 51, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 167, "line_range": [167, 168, 169], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "166             try:\n167                 result = subprocess.run([\"python3\", \"-c\",\n168                     \"import pygame; pygame.init(); print(pygame.joystick.get_count())\"],\n169                     capture_output=True, text=True)\n170                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 51, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 167, "line_range": [167, 168, 169], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "172                     sdl_gamepads = [f\"gamepad_{i}\" for i in range(count)]\n173             except:\n174                 pass\n175 \n", "col_offset": 12, "end_col_offset": 20, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 173, "line_range": [173, 174], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "211             # 检查蓝牙服务状态\n212             result = subprocess.run([\"systemctl\", \"is-active\", \"bluetooth\"],\n213                                   capture_output=True, text=True)\n214 \n", "col_offset": 21, "end_col_offset": 65, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 212, "line_range": [212, 213], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "211             # 检查蓝牙服务状态\n212             result = subprocess.run([\"systemctl\", \"is-active\", \"bluetooth\"],\n213                                   capture_output=True, text=True)\n214 \n", "col_offset": 21, "end_col_offset": 65, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 212, "line_range": [212, 213], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "219             try:\n220                 result = subprocess.run([\"bluetoothctl\", \"devices\", \"Connected\"],\n221                                       capture_output=True, text=True, timeout=5)\n222                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 80, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 220, "line_range": [220, 221], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "219             try:\n220                 result = subprocess.run([\"bluetoothctl\", \"devices\", \"Connected\"],\n221                                       capture_output=True, text=True, timeout=5)\n222                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 80, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 220, "line_range": [220, 221], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "223                     connected_devices = [line.strip() for line in result.stdout.split('\\n') if line.strip()]\n224             except:\n225                 pass\n226 \n", "col_offset": 12, "end_col_offset": 20, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 224, "line_range": [224, 225], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "261             try:\n262                 result = subprocess.run([\"aplay\", \"-l\"], capture_output=True, text=True)\n263                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 88, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 262, "line_range": [262], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "261             try:\n262                 result = subprocess.run([\"aplay\", \"-l\"], capture_output=True, text=True)\n263                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 88, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 262, "line_range": [262], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "264                     alsa_devices = [line for line in result.stdout.split('\\n') if 'card' in line]\n265             except:\n266                 pass\n267 \n", "col_offset": 12, "end_col_offset": 20, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 265, "line_range": [265, 266], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "270             try:\n271                 result = subprocess.run([\"pulseaudio\", \"--check\"], capture_output=True, text=True)\n272                 pulse_running = result.returncode == 0\n", "col_offset": 25, "end_col_offset": 98, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 271, "line_range": [271], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "270             try:\n271                 result = subprocess.run([\"pulseaudio\", \"--check\"], capture_output=True, text=True)\n272                 pulse_running = result.returncode == 0\n", "col_offset": 25, "end_col_offset": 98, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 271, "line_range": [271], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "272                 pulse_running = result.returncode == 0\n273             except:\n274                 pass\n275 \n", "col_offset": 12, "end_col_offset": 20, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 273, "line_range": [273, 274], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "278             try:\n279                 result = subprocess.run([\"pactl\", \"list\", \"short\", \"sinks\"],\n280                                       capture_output=True, text=True)\n281                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 69, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 279, "line_range": [279, 280], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "278             try:\n279                 result = subprocess.run([\"pactl\", \"list\", \"short\", \"sinks\"],\n280                                       capture_output=True, text=True)\n281                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 69, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 279, "line_range": [279, 280], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "282                     audio_sinks = [line.strip() for line in result.stdout.split('\\n') if line.strip()]\n283             except:\n284                 pass\n285 \n", "col_offset": 12, "end_col_offset": 20, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 283, "line_range": [283, 284], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "322             try:\n323                 result = subprocess.run([\"xrandr\", \"--listmonitors\"],\n324                                       capture_output=True, text=True)\n325                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 69, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 323, "line_range": [323, 324], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "322             try:\n323                 result = subprocess.run([\"xrandr\", \"--listmonitors\"],\n324                                       capture_output=True, text=True)\n325                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 69, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 323, "line_range": [323, 324], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "327                                if 'Monitor' in line or 'x' in line]\n328             except:\n329                 pass\n330 \n", "col_offset": 12, "end_col_offset": 20, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 328, "line_range": [328, 329], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "333             try:\n334                 result = subprocess.run([\"lspci\", \"-k\"], capture_output=True, text=True)\n335                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 88, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 334, "line_range": [334], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "333             try:\n334                 result = subprocess.run([\"lspci\", \"-k\"], capture_output=True, text=True)\n335                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 88, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 334, "line_range": [334], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "338                     gpu_info = gpu_lines\n339             except:\n340                 pass\n341 \n", "col_offset": 12, "end_col_offset": 20, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 339, "line_range": [339, 340], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "344             try:\n345                 result = subprocess.run([\"glxinfo\", \"-B\"], capture_output=True, text=True)\n346                 opengl_support = result.returncode == 0 and \"OpenGL\" in result.stdout\n", "col_offset": 25, "end_col_offset": 90, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 345, "line_range": [345], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "344             try:\n345                 result = subprocess.run([\"glxinfo\", \"-B\"], capture_output=True, text=True)\n346                 opengl_support = result.returncode == 0 and \"OpenGL\" in result.stdout\n", "col_offset": 25, "end_col_offset": 90, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 345, "line_range": [345], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "346                 opengl_support = result.returncode == 0 and \"OpenGL\" in result.stdout\n347             except:\n348                 pass\n349 \n", "col_offset": 12, "end_col_offset": 20, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 347, "line_range": [347, 348], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "395             try:\n396                 result = subprocess.run([\"which\", command], capture_output=True, text=True)\n397                 installed[system] = result.returncode == 0\n", "col_offset": 25, "end_col_offset": 91, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 396, "line_range": [396], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "395             try:\n396                 result = subprocess.run([\"which\", command], capture_output=True, text=True)\n397                 installed[system] = result.returncode == 0\n", "col_offset": 25, "end_col_offset": 91, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 396, "line_range": [396], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "571         try:\n572             result = subprocess.run([\"sudo\", \"systemctl\", \"start\", \"bluetooth\"],\n573                                   capture_output=True, text=True)\n574             if result.returncode == 0:\n", "col_offset": 21, "end_col_offset": 65, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 572, "line_range": [572, 573], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "571         try:\n572             result = subprocess.run([\"sudo\", \"systemctl\", \"start\", \"bluetooth\"],\n573                                   capture_output=True, text=True)\n574             if result.returncode == 0:\n", "col_offset": 21, "end_col_offset": 65, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 572, "line_range": [572, 573], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "584             # 启动 PulseAudio\n585             subprocess.run([\"pulseaudio\", \"--start\"], capture_output=True)\n586             return {\"success\": True, \"message\": \"音频系统设置完成\"}\n", "col_offset": 12, "end_col_offset": 74, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 585, "line_range": [585], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "584             # 启动 PulseAudio\n585             subprocess.run([\"pulseaudio\", \"--start\"], capture_output=True)\n586             return {\"success\": True, \"message\": \"音频系统设置完成\"}\n", "col_offset": 12, "end_col_offset": 74, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 585, "line_range": [585], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "605                     package = install_commands[system]\n606                     result = subprocess.run([\"sudo\", \"apt-get\", \"install\", \"-y\", package],\n607                                           capture_output=True, text=True)\n608                     if result.returncode == 0:\n", "col_offset": 29, "end_col_offset": 73, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 606, "line_range": [606, 607], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "605                     package = install_commands[system]\n606                     result = subprocess.run([\"sudo\", \"apt-get\", \"install\", \"-y\", package],\n607                                           capture_output=True, text=True)\n608                     if result.returncode == 0:\n", "col_offset": 29, "end_col_offset": 73, "filename": "src/core/system_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 606, "line_range": [606, 607], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "30 import os\n31 import subprocess\n32 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/core/virtuanes_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 31, "line_range": [31], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "130                 # 设置执行权限\n131                 os.chmod(script_path, 0o755)\n132 \n", "col_offset": 16, "end_col_offset": 44, "filename": "src/core/virtuanes_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 732, "link": "https://cwe.mitre.org/data/definitions/732.html"}, "issue_severity": "MEDIUM", "issue_text": "Chmod setting a permissive mask 0o755 on file (script_path).", "line_number": 131, "line_range": [131], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b103_set_bad_file_permissions.html", "test_id": "B103", "test_name": "set_bad_file_permissions"}, {"code": "153             # 执行安装脚本\n154             result = subprocess.run(\n155                 [str(script_path)],\n156                 cwd=self.install_dir,\n157                 check=True,\n158                 capture_output=True,\n159                 text=True\n160             )\n161 \n", "col_offset": 21, "end_col_offset": 13, "filename": "src/core/virtuanes_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 154, "line_range": [154, 155, 156, 157, 158, 159, 160], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "296             # 更新 MIME 数据库\n297             subprocess.run([\"sudo\", \"update-desktop-database\"], check=True)\n298 \n", "col_offset": 12, "end_col_offset": 75, "filename": "src/core/virtuanes_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 297, "line_range": [297], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "296             # 更新 MIME 数据库\n297             subprocess.run([\"sudo\", \"update-desktop-database\"], check=True)\n298 \n", "col_offset": 12, "end_col_offset": 75, "filename": "src/core/virtuanes_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 297, "line_range": [297], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "332 \n333             os.chmod(script_path, 0o755)\n334             logger.info(f\"✓ 启动脚本已创建: {script_path}\")\n", "col_offset": 12, "end_col_offset": 40, "filename": "src/core/virtuanes_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 732, "link": "https://cwe.mitre.org/data/definitions/732.html"}, "issue_severity": "MEDIUM", "issue_text": "Chmod setting a permissive mask 0o755 on file (script_path).", "line_number": 333, "line_range": [333], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b103_set_bad_file_permissions.html", "test_id": "B103", "test_name": "set_bad_file_permissions"}, {"code": "9 import json\n10 import subprocess\n11 from pathlib import Path\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/auto_code_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 10, "line_range": [10], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "36             try:\n37                 result = subprocess.run(\n38                     [\"python3\", \"-m\", \"py_compile\", str(py_file)],\n39                     capture_output=True,\n40                     text=True,\n41                     timeout=10\n42                 )\n43 \n", "col_offset": 25, "end_col_offset": 17, "filename": "src/scripts/auto_code_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 37, "line_range": [37, 38, 39, 40, 41, 42], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "36             try:\n37                 result = subprocess.run(\n38                     [\"python3\", \"-m\", \"py_compile\", str(py_file)],\n39                     capture_output=True,\n40                     text=True,\n41                     timeout=10\n42                 )\n43 \n", "col_offset": 25, "end_col_offset": 17, "filename": "src/scripts/auto_code_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 37, "line_range": [37, 38, 39, 40, 41, 42], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "209                     try:\n210                         subprocess.run([\"rm\", \"-rf\", str(root_roms)], check=True)\n211                         print(f\"  ✅ 删除重复目录: {root_roms}\")\n", "col_offset": 24, "end_col_offset": 81, "filename": "src/scripts/auto_code_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 210, "line_range": [210], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "209                     try:\n210                         subprocess.run([\"rm\", \"-rf\", str(root_roms)], check=True)\n211                         print(f\"  ✅ 删除重复目录: {root_roms}\")\n", "col_offset": 24, "end_col_offset": 81, "filename": "src/scripts/auto_code_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 210, "line_range": [210], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "8 import sys\n9 import subprocess\n10 import time\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/auto_full_pipeline.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 9, "line_range": [9], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "41             self.log_step(description, \"RUNNING\")\n42             result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)\n43 \n", "col_offset": 21, "end_col_offset": 95, "filename": "src/scripts/auto_full_pipeline.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 42, "line_range": [42], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "12 import shutil\n13 import subprocess\n14 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/auto_save_sync.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 13, "line_range": [13], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "84     data = {\"key\": remote_key, \"api_key\": api_key}\n85     resp = requests.post(url, files=files, data=data)\n86     print(f\"API上传结果: {resp.status_code}\")\n", "col_offset": 11, "end_col_offset": 53, "filename": "src/scripts/auto_save_sync.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 85, "line_range": [85], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "93     params = {\"key\": remote_key, \"api_key\": api_key}\n94     resp = requests.get(url, params=params, stream=True)\n95     if resp.status_code == 200:\n", "col_offset": 11, "end_col_offset": 56, "filename": "src/scripts/auto_save_sync.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 94, "line_range": [94], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "191     print(f\"启动模拟器: {' '.join(cmd)}\")\n192     subprocess.run(cmd)\n193 \n", "col_offset": 4, "end_col_offset": 23, "filename": "src/scripts/auto_save_sync.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 192, "line_range": [192], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "7 import sys\n8 import subprocess\n9 from pathlib import Path\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/check_core_dependencies.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 8, "line_range": [8], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "23     try:\n24         result = subprocess.run([sys.executable, '-m', 'pip', 'show', package_name],\n25                               capture_output=True, text=True)\n26         return result.returncode == 0\n", "col_offset": 17, "end_col_offset": 61, "filename": "src/scripts/check_core_dependencies.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 24, "line_range": [24, 25], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "9 import json\n10 import subprocess\n11 from pathlib import Path\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/check_dependencies.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 10, "line_range": [10], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "143         try:\n144             result = subprocess.run([sys.executable, '-m', 'pip', 'list'],\n145                                   capture_output=True, text=True)\n146 \n", "col_offset": 21, "end_col_offset": 65, "filename": "src/scripts/check_dependencies.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 144, "line_range": [144, 145], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "9 import time\n10 import subprocess\n11 import json\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/demo_all_features.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 10, "line_range": [10], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "72             ball = {\n73                 'x': random.randint(50, self.width - 50),\n74                 'y': random.randint(50, self.height - 50),\n", "col_offset": 21, "end_col_offset": 56, "filename": "src/scripts/demo_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 73, "line_range": [73], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "73                 'x': random.randint(50, self.width - 50),\n74                 'y': random.randint(50, self.height - 50),\n75                 'vx': random.uniform(-3, 3),\n", "col_offset": 21, "end_col_offset": 57, "filename": "src/scripts/demo_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 74, "line_range": [74], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "74                 'y': random.randint(50, self.height - 50),\n75                 'vx': random.uniform(-3, 3),\n76                 'vy': random.uniform(-3, 3),\n", "col_offset": 22, "end_col_offset": 43, "filename": "src/scripts/demo_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 75, "line_range": [75], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "75                 'vx': random.uniform(-3, 3),\n76                 'vy': random.uniform(-3, 3),\n77                 'radius': random.randint(10, 25),\n", "col_offset": 22, "end_col_offset": 43, "filename": "src/scripts/demo_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 76, "line_range": [76], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "76                 'vy': random.uniform(-3, 3),\n77                 'radius': random.randint(10, 25),\n78                 'color': random.choice(colors),\n", "col_offset": 26, "end_col_offset": 48, "filename": "src/scripts/demo_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 77, "line_range": [77], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "77                 'radius': random.randint(10, 25),\n78                 'color': random.choice(colors),\n79                 'trail': []\n", "col_offset": 25, "end_col_offset": 46, "filename": "src/scripts/demo_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 78, "line_range": [78], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "99         ball = {\n100             'x': random.randint(50, self.width - 50),\n101             'y': random.randint(50, self.height - 50),\n", "col_offset": 17, "end_col_offset": 52, "filename": "src/scripts/demo_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 100, "line_range": [100], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "100             'x': random.randint(50, self.width - 50),\n101             'y': random.randint(50, self.height - 50),\n102             'vx': random.uniform(-4, 4),\n", "col_offset": 17, "end_col_offset": 53, "filename": "src/scripts/demo_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 101, "line_range": [101], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "101             'y': random.randint(50, self.height - 50),\n102             'vx': random.uniform(-4, 4),\n103             'vy': random.uniform(-4, 4),\n", "col_offset": 18, "end_col_offset": 39, "filename": "src/scripts/demo_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 102, "line_range": [102], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "102             'vx': random.uniform(-4, 4),\n103             'vy': random.uniform(-4, 4),\n104             'radius': random.randint(8, 20),\n", "col_offset": 18, "end_col_offset": 39, "filename": "src/scripts/demo_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 103, "line_range": [103], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "103             'vy': random.uniform(-4, 4),\n104             'radius': random.randint(8, 20),\n105             'color': random.choice(colors),\n", "col_offset": 22, "end_col_offset": 43, "filename": "src/scripts/demo_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 104, "line_range": [104], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "104             'radius': random.randint(8, 20),\n105             'color': random.choice(colors),\n106             'trail': []\n", "col_offset": 21, "end_col_offset": 42, "filename": "src/scripts/demo_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 105, "line_range": [105], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "12 import argparse\n13 import subprocess\n14 import threading\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/enhanced_game_launcher.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 13, "line_range": [13], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "96     handlers=[\n97         logging.FileHandler('/tmp/gameplayer.log'),\n98         logging.StreamHandler()\n", "col_offset": 28, "end_col_offset": 49, "filename": "src/scripts/enhanced_game_launcher.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 377, "link": "https://cwe.mitre.org/data/definitions/377.html"}, "issue_severity": "MEDIUM", "issue_text": "Probable insecure usage of temp file/directory.", "line_number": 97, "line_range": [97], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b108_hardcoded_tmp_directory.html", "test_id": "B108", "test_name": "hardcoded_tmp_directory"}, {"code": "213                     return info.get(\"last_played\", 0)\n214         except:\n215             pass\n216         return 0\n", "col_offset": 8, "end_col_offset": 16, "filename": "src/scripts/enhanced_game_launcher.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 214, "line_range": [214, 215], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "302                 # 检查命令是否存在\n303                 if subprocess.run([\"which\", emulator_cmd[0]], capture_output=True).returncode != 0:\n304                     continue\n", "col_offset": 19, "end_col_offset": 82, "filename": "src/scripts/enhanced_game_launcher.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 303, "line_range": [303], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "302                 # 检查命令是否存在\n303                 if subprocess.run([\"which\", emulator_cmd[0]], capture_output=True).returncode != 0:\n304                     continue\n", "col_offset": 19, "end_col_offset": 82, "filename": "src/scripts/enhanced_game_launcher.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 303, "line_range": [303], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "306                 # 启动游戏\n307                 process = subprocess.Popen(\n308                     emulator_cmd,\n309                     stdout=subprocess.PIPE,\n310                     stderr=subprocess.PIPE\n311                 )\n312 \n", "col_offset": 26, "end_col_offset": 17, "filename": "src/scripts/enhanced_game_launcher.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 307, "line_range": [307, 308, 309, 310, 311], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "8 import sys\n9 import subprocess\n10 from pathlib import Path\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/final_nes_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 9, "line_range": [9], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "140         # 检查mednafen是否存在\n141         result = subprocess.run([\"which\", \"mednafen\"], capture_output=True, text=True, timeout=10)\n142 \n", "col_offset": 17, "end_col_offset": 98, "filename": "src/scripts/final_nes_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 141, "line_range": [141], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "140         # 检查mednafen是否存在\n141         result = subprocess.run([\"which\", \"mednafen\"], capture_output=True, text=True, timeout=10)\n142 \n", "col_offset": 17, "end_col_offset": 98, "filename": "src/scripts/final_nes_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 141, "line_range": [141], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "146             # 测试帮助命令\n147             help_result = subprocess.run([\"mednafen\", \"-help\"], capture_output=True, text=True, timeout=10)\n148 \n", "col_offset": 26, "end_col_offset": 107, "filename": "src/scripts/final_nes_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 147, "line_range": [147], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "146             # 测试帮助命令\n147             help_result = subprocess.run([\"mednafen\", \"-help\"], capture_output=True, text=True, timeout=10)\n148 \n", "col_offset": 26, "end_col_offset": 107, "filename": "src/scripts/final_nes_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 147, "line_range": [147], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "157                     # 启动mednafen（后台模式）\n158                     launch_result = subprocess.run([\n159                         \"mednafen\",\n160                         \"-force_module\", \"nes\",\n161                         \"-video.fs\", \"0\",  # 窗口模式\n162                         \"-sound\", \"0\",     # 禁用声音\n163                         str(rom_path)\n164                     ], capture_output=True, text=True, timeout=5)\n165 \n", "col_offset": 36, "end_col_offset": 65, "filename": "src/scripts/final_nes_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 158, "line_range": [158, 159, 160, 161, 162, 163, 164], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "157                     # 启动mednafen（后台模式）\n158                     launch_result = subprocess.run([\n159                         \"mednafen\",\n160                         \"-force_module\", \"nes\",\n161                         \"-video.fs\", \"0\",  # 窗口模式\n162                         \"-sound\", \"0\",     # 禁用声音\n163                         str(rom_path)\n164                     ], capture_output=True, text=True, timeout=5)\n165 \n", "col_offset": 36, "end_col_offset": 65, "filename": "src/scripts/final_nes_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 158, "line_range": [158, 159, 160, 161, 162, 163, 164], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "247     try:\n248         result = subprocess.run([\"python3\", str(test_script)],\n249                               capture_output=True, text=True, timeout=10)\n250 \n", "col_offset": 17, "end_col_offset": 73, "filename": "src/scripts/final_nes_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 248, "line_range": [248, 249], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "247     try:\n248         result = subprocess.run([\"python3\", str(test_script)],\n249                               capture_output=True, text=True, timeout=10)\n250 \n", "col_offset": 17, "end_col_offset": 73, "filename": "src/scripts/final_nes_fix.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 248, "line_range": [248, 249], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "8 import sys\n9 import subprocess\n10 import json\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/fix_emulator_startup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 9, "line_range": [9], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "88         try:\n89             result = subprocess.run([\"which\", \"brew\"], capture_output=True, timeout=10)\n90 \n", "col_offset": 21, "end_col_offset": 87, "filename": "src/scripts/fix_emulator_startup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 89, "line_range": [89], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "88         try:\n89             result = subprocess.run([\"which\", \"brew\"], capture_output=True, timeout=10)\n90 \n", "col_offset": 21, "end_col_offset": 87, "filename": "src/scripts/fix_emulator_startup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 89, "line_range": [89], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "94                 # 检查Homebrew版本\n95                 version_result = subprocess.run([\"brew\", \"--version\"], capture_output=True, text=True, timeout=10)\n96                 if version_result.returncode == 0:\n", "col_offset": 33, "end_col_offset": 114, "filename": "src/scripts/fix_emulator_startup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 95, "line_range": [95], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "94                 # 检查Homebrew版本\n95                 version_result = subprocess.run([\"brew\", \"--version\"], capture_output=True, text=True, timeout=10)\n96                 if version_result.returncode == 0:\n", "col_offset": 33, "end_col_offset": 114, "filename": "src/scripts/fix_emulator_startup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 95, "line_range": [95], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "116 \n117             result = subprocess.run(install_script, shell=True, timeout=600)\n118 \n", "col_offset": 21, "end_col_offset": 76, "filename": "src/scripts/fix_emulator_startup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "HIGH", "issue_text": "subprocess call with shell=True identified, security issue.", "line_number": 117, "line_range": [117], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b602_subprocess_popen_with_shell_equals_true.html", "test_id": "B602", "test_name": "subprocess_popen_with_shell_equals_true"}, {"code": "138             # 检查命令是否存在\n139             which_result = subprocess.run([\"which\", emulator_name], capture_output=True, timeout=10)\n140 \n", "col_offset": 27, "end_col_offset": 100, "filename": "src/scripts/fix_emulator_startup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 139, "line_range": [139], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "138             # 检查命令是否存在\n139             which_result = subprocess.run([\"which\", emulator_name], capture_output=True, timeout=10)\n140 \n", "col_offset": 27, "end_col_offset": 100, "filename": "src/scripts/fix_emulator_startup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 139, "line_range": [139], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "145             # 测试运行\n146             test_result = subprocess.run([emulator_name] + test_args, capture_output=True, text=True, timeout=10)\n147 \n", "col_offset": 26, "end_col_offset": 113, "filename": "src/scripts/fix_emulator_startup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 146, "line_range": [146], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "169 \n170             result = subprocess.run(cmd_parts, capture_output=True, text=True, timeout=300)\n171 \n", "col_offset": 21, "end_col_offset": 91, "filename": "src/scripts/fix_emulator_startup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 170, "line_range": [170], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "310             # 尝试启动（3秒后自动退出）\n311             result = subprocess.run(cmd, timeout=3, capture_output=True, text=True)\n312 \n", "col_offset": 21, "end_col_offset": 83, "filename": "src/scripts/fix_emulator_startup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 311, "line_range": [311], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "8 import sys\n9 import subprocess\n10 import time\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/fix_nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 9, "line_range": [9], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "58         try:\n59             result = subprocess.run([\"which\", \"brew\"], capture_output=True, timeout=10)\n60             if result.returncode == 0:\n", "col_offset": 21, "end_col_offset": 87, "filename": "src/scripts/fix_nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 59, "line_range": [59], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "58         try:\n59             result = subprocess.run([\"which\", \"brew\"], capture_output=True, timeout=10)\n60             if result.returncode == 0:\n", "col_offset": 21, "end_col_offset": 87, "filename": "src/scripts/fix_nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 59, "line_range": [59], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "78                 install_script,\n79                 shell=True,\n80                 timeout=600  # 10分钟超时\n81             )\n82 \n83             if result.returncode == 0:\n84                 print(\"✅ Homebrew安装成功\")\n", "col_offset": 21, "end_col_offset": 13, "filename": "src/scripts/fix_nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "HIGH", "issue_text": "subprocess call with shell=True identified, security issue.", "line_number": 79, "line_range": [77, 78, 79, 80, 81], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b602_subprocess_popen_with_shell_equals_true.html", "test_id": "B602", "test_name": "subprocess_popen_with_shell_equals_true"}, {"code": "100         try:\n101             result = subprocess.run(\n102                 [\"brew\", \"update\"],\n103                 capture_output=True,\n104                 text=True,\n105                 timeout=120\n106             )\n107 \n", "col_offset": 21, "end_col_offset": 13, "filename": "src/scripts/fix_nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 101, "line_range": [101, 102, 103, 104, 105, 106], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "100         try:\n101             result = subprocess.run(\n102                 [\"brew\", \"update\"],\n103                 capture_output=True,\n104                 text=True,\n105                 timeout=120\n106             )\n107 \n", "col_offset": 21, "end_col_offset": 13, "filename": "src/scripts/fix_nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 101, "line_range": [101, 102, 103, 104, 105, 106], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "126             # 首先检查命令是否存在\n127             which_result = subprocess.run(\n128                 [\"which\", emulator_name],\n129                 capture_output=True,\n130                 timeout=10\n131             )\n132 \n", "col_offset": 27, "end_col_offset": 13, "filename": "src/scripts/fix_nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 127, "line_range": [127, 128, 129, 130, 131], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "126             # 首先检查命令是否存在\n127             which_result = subprocess.run(\n128                 [\"which\", emulator_name],\n129                 capture_output=True,\n130                 timeout=10\n131             )\n132 \n", "col_offset": 27, "end_col_offset": 13, "filename": "src/scripts/fix_nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 127, "line_range": [127, 128, 129, 130, 131], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "137             # 尝试运行帮助命令\n138             result = subprocess.run(\n139                 test_cmd,\n140                 capture_output=True,\n141                 text=True,\n142                 timeout=10\n143             )\n144 \n", "col_offset": 21, "end_col_offset": 13, "filename": "src/scripts/fix_nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 138, "line_range": [138, 139, 140, 141, 142, 143], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "174             print(\"⏳ 正在安装，请稍候...\")\n175             result = subprocess.run(\n176                 cmd_parts,\n177                 capture_output=True,\n178                 text=True,\n179                 timeout=600  # 10分钟超时\n180             )\n181 \n", "col_offset": 21, "end_col_offset": 13, "filename": "src/scripts/fix_nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 175, "line_range": [175, 176, 177, 178, 179, 180], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "250 \n251             result = subprocess.run(\n252                 cmd,\n253                 capture_output=True,\n254                 text=True,\n255                 timeout=10\n256             )\n257 \n", "col_offset": 21, "end_col_offset": 13, "filename": "src/scripts/fix_nes_emulator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 251, "line_range": [251, 252, 253, 254, 255, 256], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "8 import sys\n9 import subprocess\n10 import json\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/fix_nes_runtime_failure.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 9, "line_range": [9], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "30             # 检查mednafen是否存在\n31             which_result = subprocess.run([\"which\", \"mednafen\"], capture_output=True, text=True, timeout=10)\n32 \n", "col_offset": 27, "end_col_offset": 108, "filename": "src/scripts/fix_nes_runtime_failure.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 31, "line_range": [31], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "30             # 检查mednafen是否存在\n31             which_result = subprocess.run([\"which\", \"mednafen\"], capture_output=True, text=True, timeout=10)\n32 \n", "col_offset": 27, "end_col_offset": 108, "filename": "src/scripts/fix_nes_runtime_failure.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 31, "line_range": [31], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "37                 # 检查版本\n38                 version_result = subprocess.run([mednafen_path, \"-help\"], capture_output=True, text=True, timeout=10)\n39 \n", "col_offset": 33, "end_col_offset": 117, "filename": "src/scripts/fix_nes_runtime_failure.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 38, "line_range": [38], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "201             # 启动mednafen（3秒后自动退出）\n202             result = subprocess.run([\n203                 \"mednafen\",\n204                 \"-force_module\", \"nes\",\n205                 \"-video.fs\", \"0\",  # 窗口模式\n206                 \"-sound\", \"0\",     # 禁用声音\n207                 str(rom_path)\n208             ], timeout=3, capture_output=True, text=True)\n209 \n", "col_offset": 21, "end_col_offset": 57, "filename": "src/scripts/fix_nes_runtime_failure.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 202, "line_range": [202, 203, 204, 205, 206, 207, 208], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "201             # 启动mednafen（3秒后自动退出）\n202             result = subprocess.run([\n203                 \"mednafen\",\n204                 \"-force_module\", \"nes\",\n205                 \"-video.fs\", \"0\",  # 窗口模式\n206                 \"-sound\", \"0\",     # 禁用声音\n207                 str(rom_path)\n208             ], timeout=3, capture_output=True, text=True)\n209 \n", "col_offset": 21, "end_col_offset": 57, "filename": "src/scripts/fix_nes_runtime_failure.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 202, "line_range": [202, 203, 204, 205, 206, 207, 208], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "347         try:\n348             result = subprocess.run([\"python3\", str(test_api_file)],\n349                                   capture_output=True, text=True, timeout=15)\n350 \n", "col_offset": 21, "end_col_offset": 77, "filename": "src/scripts/fix_nes_runtime_failure.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 348, "line_range": [348, 349], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "347         try:\n348             result = subprocess.run([\"python3\", str(test_api_file)],\n349                                   capture_output=True, text=True, timeout=15)\n350 \n", "col_offset": 21, "end_col_offset": 77, "filename": "src/scripts/fix_nes_runtime_failure.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 348, "line_range": [348, 349], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "9 import json\n10 import subprocess\n11 from pathlib import Path\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/image_integration_checker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 10, "line_range": [10], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "9 import json\n10 import subprocess\n11 import threading\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/nes_game_launcher.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 10, "line_range": [10], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "29         try:\n30             import subprocess\n31             subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\"] + missing_deps)\n", "col_offset": 12, "end_col_offset": 29, "filename": "src/scripts/nes_game_launcher.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 30, "line_range": [30], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "30             import subprocess\n31             subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\"] + missing_deps)\n32             print(\"✅ 依赖安装完成\")\n", "col_offset": 12, "end_col_offset": 90, "filename": "src/scripts/nes_game_launcher.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 31, "line_range": [31], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "66                     return font\n67             except:\n68                 continue\n69 \n", "col_offset": 12, "end_col_offset": 24, "filename": "src/scripts/nes_game_launcher.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Continue detected.", "line_number": 67, "line_range": [67, 68], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b112_try_except_continue.html", "test_id": "B112", "test_name": "try_except_continue"}, {"code": "406 \n407                 process = subprocess.Popen(\n408                     cmd,\n409                     stdout=subprocess.PIPE,\n410                     stderr=subprocess.PIPE,\n411                     text=True\n412                 )\n413 \n", "col_offset": 26, "end_col_offset": 17, "filename": "src/scripts/nes_game_launcher.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 407, "line_range": [407, 408, 409, 410, 411, 412], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "164 \n165     import subprocess\n166 \n", "col_offset": 4, "end_col_offset": 21, "filename": "src/scripts/quick_fix_nes.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 165, "line_range": [165], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "168         # 检查mednafen是否安装\n169         result = subprocess.run([\"which\", \"mednafen\"], capture_output=True, timeout=10)\n170 \n", "col_offset": 17, "end_col_offset": 87, "filename": "src/scripts/quick_fix_nes.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 169, "line_range": [169], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "168         # 检查mednafen是否安装\n169         result = subprocess.run([\"which\", \"mednafen\"], capture_output=True, timeout=10)\n170 \n", "col_offset": 17, "end_col_offset": 87, "filename": "src/scripts/quick_fix_nes.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 169, "line_range": [169], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "174             # 测试帮助命令\n175             help_result = subprocess.run([\"mednafen\", \"-help\"], capture_output=True, text=True, timeout=10)\n176 \n", "col_offset": 26, "end_col_offset": 107, "filename": "src/scripts/quick_fix_nes.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 175, "line_range": [175], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "174             # 测试帮助命令\n175             help_result = subprocess.run([\"mednafen\", \"-help\"], capture_output=True, text=True, timeout=10)\n176 \n", "col_offset": 26, "end_col_offset": 107, "filename": "src/scripts/quick_fix_nes.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 175, "line_range": [175], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "8 import sys\n9 import subprocess\n10 import time\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/quick_function_test.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 9, "line_range": [9], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "102     # 检查语法\n103     result = subprocess.run([sys.executable, '-m', 'py_compile', str(launcher_file)],\n104                           capture_output=True, text=True)\n105     if result.returncode == 0:\n", "col_offset": 13, "end_col_offset": 57, "filename": "src/scripts/quick_function_test.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 103, "line_range": [103, 104], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "134     # 检查语法\n135     result = subprocess.run([sys.executable, '-m', 'py_compile', str(audio_file)],\n136                           capture_output=True, text=True)\n137     if result.returncode == 0:\n", "col_offset": 13, "end_col_offset": 57, "filename": "src/scripts/quick_function_test.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 135, "line_range": [135, 136], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "35         try:\n36             import subprocess\n37             subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\"] + missing_deps)\n", "col_offset": 12, "end_col_offset": 29, "filename": "src/scripts/rom_downloader.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 36, "line_range": [36], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "36             import subprocess\n37             subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\"] + missing_deps)\n38             print(\"✅ 依赖安装完成\")\n", "col_offset": 12, "end_col_offset": 90, "filename": "src/scripts/rom_downloader.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 37, "line_range": [37], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "8 import sys\n9 import subprocess\n10 import time\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/run_nes_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 9, "line_range": [9], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "54     for emulator, info in emulators.items():\n55         if subprocess.run(['which', emulator], capture_output=True).returncode == 0:\n56             available_emulators.append(emulator)\n", "col_offset": 11, "end_col_offset": 67, "filename": "src/scripts/run_nes_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 55, "line_range": [55], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "54     for emulator, info in emulators.items():\n55         if subprocess.run(['which', emulator], capture_output=True).returncode == 0:\n56             available_emulators.append(emulator)\n", "col_offset": 11, "end_col_offset": 67, "filename": "src/scripts/run_nes_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 55, "line_range": [55], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "110                 # 检查RetroArch是否安装\n111                 result = subprocess.run(['which', 'retroarch'],\n112                                       capture_output=True, text=True)\n113                 return result.returncode == 0\n", "col_offset": 25, "end_col_offset": 69, "filename": "src/scripts/run_nes_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 111, "line_range": [111, 112], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "110                 # 检查RetroArch是否安装\n111                 result = subprocess.run(['which', 'retroarch'],\n112                                       capture_output=True, text=True)\n113                 return result.returncode == 0\n", "col_offset": 25, "end_col_offset": 69, "filename": "src/scripts/run_nes_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 111, "line_range": [111, 112], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "116                 # 检查FCEUX是否安装\n117                 result = subprocess.run(['which', 'fceux'],\n118                                       capture_output=True, text=True)\n119                 return result.returncode == 0\n", "col_offset": 25, "end_col_offset": 69, "filename": "src/scripts/run_nes_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 117, "line_range": [117, 118], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "116                 # 检查FCEUX是否安装\n117                 result = subprocess.run(['which', 'fceux'],\n118                                       capture_output=True, text=True)\n119                 return result.returncode == 0\n", "col_offset": 25, "end_col_offset": 69, "filename": "src/scripts/run_nes_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 117, "line_range": [117, 118], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "128                         test_cmd = ['python3', '-c', f'import sys; sys.path.insert(0, \"{script_path.parent.parent}\"); exec(open(\"{script_path}\").read())']\n129                         result = subprocess.run(test_cmd, capture_output=True, timeout=5)\n130                         return True  # 如果能导入就认为可用\n", "col_offset": 33, "end_col_offset": 89, "filename": "src/scripts/run_nes_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 129, "line_range": [129], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "226             # 启动进程\n227             self.running_process = subprocess.Popen(\n228                 cmd,\n229                 stdout=subprocess.PIPE,\n230                 stderr=subprocess.PIPE,\n231                 env=env,\n232                 preexec_fn=os.setsid if hasattr(os, 'setsid') else None  # 创建新的进程组\n233             )\n234 \n", "col_offset": 35, "end_col_offset": 13, "filename": "src/scripts/run_nes_game.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 227, "line_range": [227, 228, 229, 230, 231, 232, 233], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "21         locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')\n22     except:\n23         pass\n24 \n", "col_offset": 4, "end_col_offset": 12, "filename": "src/scripts/simple_nes_player.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 22, "line_range": [22, 23], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "64                     return font\n65             except:\n66                 continue\n67 \n", "col_offset": 12, "end_col_offset": 24, "filename": "src/scripts/simple_nes_player.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Continue detected.", "line_number": 65, "line_range": [65, 66], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b112_try_except_continue.html", "test_id": "B112", "test_name": "try_except_continue"}, {"code": "232                 color = self.nes_palette[secrets.randbelow(15)]\n233                 if random.random() < 0.1:  # 10%概率绘制背景像素\n234                     pygame.draw.rect(self.screen, color, (i, j, 5, 5))\n", "col_offset": 19, "end_col_offset": 34, "filename": "src/scripts/simple_nes_player.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 233, "line_range": [233], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "268             if enemy['y'] <= 150 or enemy['y'] >= 450:\n269                 enemy['dy'] = random.choice([-1, 0, 1])\n270 \n", "col_offset": 30, "end_col_offset": 55, "filename": "src/scripts/simple_nes_player.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 269, "line_range": [269], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "8 import sys\n9 import subprocess\n10 import time\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/test_core_functions.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 9, "line_range": [9], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "24         # 检查Docker是否可用\n25         result = subprocess.run(['docker', '--version'],\n26                               capture_output=True, text=True, timeout=10)\n27         if result.returncode == 0:\n", "col_offset": 17, "end_col_offset": 73, "filename": "src/scripts/test_core_functions.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 25, "line_range": [25, 26], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "24         # 检查Docker是否可用\n25         result = subprocess.run(['docker', '--version'],\n26                               capture_output=True, text=True, timeout=10)\n27         if result.returncode == 0:\n", "col_offset": 17, "end_col_offset": 73, "filename": "src/scripts/test_core_functions.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 25, "line_range": [25, 26], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "34         print(\"🔧 测试Docker运行...\")\n35         result = subprocess.run(['docker', 'run', '--rm', 'hello-world'],\n36                               capture_output=True, text=True, timeout=30)\n37         if result.returncode == 0:\n", "col_offset": 17, "end_col_offset": 73, "filename": "src/scripts/test_core_functions.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 35, "line_range": [35, 36], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "34         print(\"🔧 测试Docker运行...\")\n35         result = subprocess.run(['docker', 'run', '--rm', 'hello-world'],\n36                               capture_output=True, text=True, timeout=30)\n37         if result.returncode == 0:\n", "col_offset": 17, "end_col_offset": 73, "filename": "src/scripts/test_core_functions.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 35, "line_range": [35, 36], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "104         # 检查语法\n105         result = subprocess.run([sys.executable, '-m', 'py_compile', str(launcher_file)],\n106                               capture_output=True, text=True)\n107         if result.returncode == 0:\n", "col_offset": 17, "end_col_offset": 61, "filename": "src/scripts/test_core_functions.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 105, "line_range": [105, 106], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "133         # 检查语法\n134         result = subprocess.run([sys.executable, '-m', 'py_compile', str(audio_file)],\n135                               capture_output=True, text=True)\n136         if result.returncode == 0:\n", "col_offset": 17, "end_col_offset": 61, "filename": "src/scripts/test_core_functions.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 134, "line_range": [134, 135], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "169         # 检查语法\n170         result = subprocess.run([sys.executable, '-m', 'py_compile', str(device_file)],\n171                               capture_output=True, text=True)\n172         if result.returncode == 0:\n", "col_offset": 17, "end_col_offset": 61, "filename": "src/scripts/test_core_functions.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 170, "line_range": [170, 171], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "256                 # 检查语法\n257                 result = subprocess.run([sys.executable, '-m', 'py_compile', str(tool_path)],\n258                                       capture_output=True, text=True)\n259                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 69, "filename": "src/scripts/test_core_functions.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 257, "line_range": [257, 258], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "280 \n281         process = subprocess.Popen([\n282             sys.executable, str(launcher_path),\n283             \"--web-only\", \"--port\", \"8085\"\n284         ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)\n285 \n", "col_offset": 18, "end_col_offset": 69, "filename": "src/scripts/test_core_functions.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 281, "line_range": [281, 282, 283, 284], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "295                 import urllib.request\n296                 response = urllib.request.urlopen(\"http://localhost:8085\", timeout=5)\n297                 if response.getcode() == 200:\n", "col_offset": 27, "end_col_offset": 85, "filename": "src/scripts/test_core_functions.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 22, "link": "https://cwe.mitre.org/data/definitions/22.html"}, "issue_severity": "MEDIUM", "issue_text": "Audit url open for permitted schemes. Allowing use of file:/ or custom schemes is often unexpected.", "line_number": 296, "line_range": [296], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_calls.html#b310-urllib-urlopen", "test_id": "B310", "test_name": "blacklist"}, {"code": "8 import shutil\n9 import subprocess\n10 from pathlib import Path\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/universal_game_launcher.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 9, "line_range": [9], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "88             cmd = emu['cmd'] + [rom_path]\n89             proc = subprocess.Popen(cmd)\n90             proc.wait()\n", "col_offset": 19, "end_col_offset": 40, "filename": "src/scripts/universal_game_launcher.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 89, "line_range": [89], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "7 import sys\n8 import subprocess\n9 from pathlib import Path\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/scripts/verify_docker_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 8, "line_range": [8], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "14     try:\n15         result = subprocess.run(['docker', '--version'], capture_output=True, text=True)\n16         return result.returncode == 0\n", "col_offset": 17, "end_col_offset": 88, "filename": "src/scripts/verify_docker_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 15, "line_range": [15], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "14     try:\n15         result = subprocess.run(['docker', '--version'], capture_output=True, text=True)\n16         return result.returncode == 0\n", "col_offset": 17, "end_col_offset": 88, "filename": "src/scripts/verify_docker_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 15, "line_range": [15], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "34 \n35         result = subprocess.run(cmd, capture_output=True, text=True)\n36 \n", "col_offset": 17, "end_col_offset": 68, "filename": "src/scripts/verify_docker_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 35, "line_range": [35], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "75 \n76             result = subprocess.run(cmd, capture_output=True, text=True)\n77 \n", "col_offset": 21, "end_col_offset": 72, "filename": "src/scripts/verify_docker_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 76, "line_range": [76], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "115 \n116             result = subprocess.run(cmd, capture_output=True, text=True)\n117 \n", "col_offset": 21, "end_col_offset": 72, "filename": "src/scripts/verify_docker_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 116, "line_range": [116], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "145         # 启动容器\n146         result = subprocess.run(cmd, capture_output=True, text=True)\n147 \n", "col_offset": 17, "end_col_offset": 68, "filename": "src/scripts/verify_docker_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 146, "line_range": [146], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "160         check_cmd = ['docker', 'ps', '--filter', f'id={container_id}', '--format', '{{.Status}}']\n161         check_result = subprocess.run(check_cmd, capture_output=True, text=True)\n162 \n", "col_offset": 23, "end_col_offset": 80, "filename": "src/scripts/verify_docker_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 161, "line_range": [161], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "171         stop_cmd = ['docker', 'stop', container_id]\n172         subprocess.run(stop_cmd, capture_output=True, text=True)\n173 \n", "col_offset": 8, "end_col_offset": 64, "filename": "src/scripts/verify_docker_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 172, "line_range": [172], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "51         hdmi_group_line = configurator.find_config_line(lines, \"hdmi_group\")\n52         assert hdmi_group_line is not None, \"未找到 hdmi_group\"\n53         print(f\"✓ 找到 hdmi_group 在第 {hdmi_group_line + 1} 行\")\n", "col_offset": 8, "end_col_offset": 66, "filename": "src/systems/systems/hdmi/tests/test_hdmi_config.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 52, "line_range": [52], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "60         new_hdmi_mode_line = configurator.find_config_line(updated_lines, \"hdmi_mode\")\n61         assert new_hdmi_mode_line is not None, \"未找到更新后的 hdmi_mode\"\n62         parsed = configurator.parse_config_line(updated_lines[new_hdmi_mode_line])\n", "col_offset": 8, "end_col_offset": 80, "filename": "src/systems/systems/hdmi/tests/test_hdmi_config.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 61, "line_range": [61], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "62         parsed = configurator.parse_config_line(updated_lines[new_hdmi_mode_line])\n63         assert parsed and parsed['value'] == '16', \"配置更新失败\"\n64         print(\"✓ 配置更新成功\")\n", "col_offset": 8, "end_col_offset": 71, "filename": "src/systems/systems/hdmi/tests/test_hdmi_config.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 63, "line_range": [63], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "84     for key in required_keys:\n85         assert key in configurator.hdmi_configs, f\"缺少配置项: {key}\"\n86         print(f\"✓ 包含配置项: {key}\")\n", "col_offset": 8, "end_col_offset": 74, "filename": "src/systems/systems/hdmi/tests/test_hdmi_config.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 85, "line_range": [85], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "100         # 测试备份\n101         assert configurator.backup_config(), \"备份功能失败\"\n102         print(\"✓ 备份功能正常\")\n", "col_offset": 8, "end_col_offset": 65, "filename": "src/systems/systems/hdmi/tests/test_hdmi_config.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 101, "line_range": [101], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "104         # 检查备份文件\n105         assert configurator.backup_path.exists(), \"备份文件未创建\"\n106         print(f\"✓ 备份文件已创建: {configurator.backup_path}\")\n", "col_offset": 8, "end_col_offset": 73, "filename": "src/systems/systems/hdmi/tests/test_hdmi_config.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 105, "line_range": [105], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "110             backup_content = f.read()\n111         assert backup_content == test_content, \"备份内容不正确\"\n112         print(\"✓ 备份内容正确\")\n", "col_offset": 8, "end_col_offset": 70, "filename": "src/systems/systems/hdmi/tests/test_hdmi_config.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 111, "line_range": [111], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "139         # 测试验证\n140         assert configurator.validate_config(), \"配置验证失败\"\n141         print(\"✓ 配置验证通过\")\n", "col_offset": 8, "end_col_offset": 67, "filename": "src/systems/systems/hdmi/tests/test_hdmi_config.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 140, "line_range": [140], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "178     configurator = HDMIConfigurator(str(config_path))\n179     assert configurator.apply_hdmi_configs() is True\n180 \n", "col_offset": 4, "end_col_offset": 52, "filename": "src/systems/systems/hdmi/tests/test_hdmi_config.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 179, "line_range": [179], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "29 import platform\n30 import subprocess\n31 import requests\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/systems/systems/retropie/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 30, "line_range": [30], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "172         try:\n173             result = subprocess.run(\n174                 cmd,\n175                 capture_output=True,\n176                 text=True,\n177                 check=check\n178             )\n179             return result.returncode, result.stdout, result.stderr\n", "col_offset": 21, "end_col_offset": 13, "filename": "src/systems/systems/retropie/core/retropie_installer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 173, "line_range": [173, 174, 175, 176, 177, 178], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}]}