FROM python:3.9-slim

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV TEST_ENV=true
ENV DOCKER_ENV=true

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    git \
    wget \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /app

# 复制项目文件
COPY . /app/

# 升级 pip
RUN pip install --upgrade pip

# 安装 Python 依赖
RUN pip install requests paramiko tqdm flask pytest pytest-cov pytest-asyncio pytest-mock pillow pyyaml psutil python-dotenv

# 创建必要的目录结构
RUN mkdir -p /opt/retropie/emulators/nesticle \
    /opt/retropie/configs/nes \
    /home/<USER>/RetroPie/roms/nes \
    /home/<USER>/RetroPie/cheats \
    /home/<USER>/RetroPie/saves/nes \
    /usr/share/applications \
    /etc/systemd/system

# 设置权限
RUN chmod +x scripts/*.sh || true

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["python3", "-c", "print('🎮 GamePlayer-Raspberry Docker 测试环境启动成功!'); import http.server; import socketserver; import os; os.chdir('/app'); PORT = 8080; Handler = http.server.SimpleHTTPRequestHandler; httpd = socketserver.TCPServer(('', PORT), Handler); print(f'🌐 HTTP 服务启动在端口 {PORT}'); httpd.serve_forever()"]
