name: Build & Deploy API Docs

on:
  push:
    branches: [ master ]

jobs:
  build-docs:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pdoc

      - name: Generate API docs
        run: |
          mkdir -p apidocs
          pdoc --html systems/ --output-dir apidocs --force

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./apidocs
          publish_branch: gh-pages 