# GamePlayer-Raspberry 简化Docker图形化环境
FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV DISPLAY=:1
ENV HOME=/home/<USER>
ENV USER=gamer

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    python3 python3-pip \
    xvfb x11vnc fluxbox \
    curl wget git \
    mednafen \
    && rm -rf /var/lib/apt/lists/*

# 创建用户
RUN useradd -m -s /bin/bash gamer && \
    echo "gamer:gamer123" | chpasswd

# 切换到用户
USER gamer
WORKDIR /home/<USER>

# 复制项目文件
COPY --chown=gamer:gamer . /home/<USER>/GamePlayer-Raspberry/

# 设置工作目录
WORKDIR /home/<USER>/GamePlayer-Raspberry

# 创建启动脚本
RUN echo '#!/bin/bash\n\
export DISPLAY=:1\n\
Xvfb :1 -screen 0 1920x1080x24 &\n\
sleep 2\n\
fluxbox &\n\
x11vnc -display :1 -nopw -listen 0.0.0.0 -xkb -forever &\n\
sleep 2\n\
echo "🎮 GamePlayer-Raspberry Docker环境启动完成"\n\
echo "🌐 Web界面: http://localhost:3020"\n\
echo "🖥️ VNC连接: localhost:5900"\n\
cd /home/<USER>/GamePlayer-Raspberry\n\
python3 src/scripts/simple_demo_server.py &\n\
wait\n\
' > /home/<USER>/start_simple.sh && chmod +x /home/<USER>/start_simple.sh

# 暴露端口
EXPOSE 3020 5900

# 启动命令
CMD ["/home/<USER>/start_simple.sh"]
